---
description:
globs:
alwaysApply: false
---
# API 服务与数据层架构指南

## API 服务架构

### 🔌 核心 API 服务
- [backend/server.js](mdc:backend/server.js) - Express.js API 服务器
  - 提供 RESTful API 接口
  - 支持文件上传（multer 中间件）
  - CORS 跨域支持
  - JSON 响应格式标准化

### 🎯 API 端点设计

#### 转换服务端点
```javascript
// 文本转换接口
POST /api/convert
Content-Type: application/json
Body: { "content": "{{#if product}}{{product.title}}{{/if}}" }

// 文件转换接口  
POST /api/convert-file
Content-Type: multipart/form-data
Body: FormData with file (.hbs, .handlebars, .html)
```

#### 规则查询端点
```javascript
// 获取所有转换规则
GET /api/rules

// 获取指定分类规则
GET /api/rules/:category
// 支持分类: conditional, iteration, context, output, partial, filter, operator

// 获取过滤器映射
GET /api/filters
```

### 📊 数据处理层

#### 转换器核心类
- [backend/converter.js](mdc:backend/converter.js) - `HandlebarsToSlineConverter` 类
  - 分类规则处理引擎
  - 格式保持处理器
  - 统计和日志收集器

#### 规则定义层
- [backend/rules.js](mdc:backend/rules.js) - 规则定义和映射表
  - **OBJECT_MAPPINGS**: 37个对象映射（shop, product, customer等）
  - **FILTER_MAPPINGS**: 55个过滤器映射（capitalize, money, date等） 
  - **LOOP_VARIABLE_MAPPINGS**: 7个循环变量映射（@index, @first等）
  - **OPERATOR_MAPPINGS**: 4个运算符映射（&&, ||, ===等）

#### 分类规则组织
```javascript
// 规则按类型分组
const CONDITIONAL_TAG_RULES = []; // 7个条件标签规则
const ITERATION_TAG_RULES = [];   // 9个循环标签规则
const CONTEXT_TAG_RULES = [];     // 2个上下文标签规则
const OUTPUT_TAG_RULES = [];      // 3个输出标签规则
const PARTIAL_TAG_RULES = [];     // 2个部分模板规则

// 规则查询函数
getRulesByCategory(category)  // 按分类获取规则
getFilterByName(name)         // 获取过滤器映射
getObjectByName(name)         // 获取对象映射
getStats()                    // 获取统计信息
```

## 🔄 数据流处理

### 转换处理流程
1. **输入验证** - 检查内容类型和格式
2. **预处理** - 保存格式信息，预处理特殊字符
3. **分类转换** - 按规则类型依次执行转换
   - conditional → iteration → context → output → partial → filter → operator
4. **对象映射** - 转换 Handlebars 对象引用
5. **过滤器转换** - 转换辅助函数为 Sline 过滤器
6. **后处理** - 恢复格式，清理输出
7. **统计收集** - 记录转换统计和日志

### 转换状态管理
```javascript
class HandlebarsToSlineConverter {
  constructor(options = {}) {
    this.conversionLog = [];      // 转换日志
    this.errors = [];             // 错误记录
    this.warnings = [];           // 警告信息
    this.categoryStats = {};      // 分类统计
  }
  
  // 核心转换方法
  convert(input) { /* 主转换逻辑 */ }
  convertByCategory(input, category) { /* 分类转换 */ }
  convertFilters(input) { /* 过滤器转换 */ }
  convertObjects(input) { /* 对象转换 */ }
}
```

## 📁 数据存储设计

### 文件系统存储
- **规则定义**: [backend/rules.js](mdc:backend/rules.js) - JavaScript 对象形式存储
- **示例数据**: [examples/](mdc:examples) - 转换示例和测试用例
- **主题数据**: [theme-handlebars/](mdc:theme-handlebars) 和 [theme-sline/](mdc:theme-sline)
- **日志存储**: [logs/](mdc:logs) - PM2 管理的结构化日志

### 静态数据结构
```javascript
// 规则数据结构
const rule = {
  name: 'if 标签转换',
  category: 'conditional', 
  description: '转换描述',
  pattern: /regex/,
  replacement: 'replacement',
  examples: {
    handlebars: '{{#if condition}}',
    sline: '{{#if condition}}'
  }
}

// 映射数据结构
const mapping = {
  'product.title': 'product.title',
  'product.price': 'product.price'
}
```

## 🚀 性能与扩展

### 性能优化策略
- **规则缓存**: 规则对象在内存中缓存，避免重复解析
- **流式处理**: 大文件支持流式读取和转换
- **并发处理**: PM2 集群模式支持多进程处理
- **内存管理**: 转换器实例可复用，避免重复创建

### 扩展性设计
- **插件化规则**: 支持自定义规则添加
- **中间件架构**: Express 中间件支持功能扩展
- **API 版本控制**: 预留 API 版本控制空间
- **错误处理**: 统一错误处理和响应格式

## 🔧 开发和调试

### 数据调试工具
```javascript
// 启用详细日志
converter.options.enableCategoryLogging = true

// 获取转换统计
const stats = converter.getStats()
console.log(stats.categoryStats)  // 分类统计
console.log(stats.totalRules)     // 总规则数

// 查看规则应用情况
converter.conversionLog.forEach(log => console.log(log))
```

### API 测试
```bash
# 测试转换 API
curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"content":"{{#if product}}{{product.title}}{{/if}}"}'

# 测试规则查询
curl http://localhost:3000/api/rules/conditional

# 测试过滤器映射
curl http://localhost:3000/api/filters
```

### 错误处理机制
- **输入验证**: 严格的参数类型和格式检查
- **转换异常**: 转换过程中的错误捕获和记录
- **API 错误**: 统一的 HTTP 错误响应格式
- **日志记录**: 完整的错误日志和追踪信息

## 📊 监控和分析

### 转换统计
- **规则应用次数**: 每个规则的使用频率统计
- **分类转换量**: 各分类规则的转换数量
- **错误率统计**: 转换失败和警告的比例
- **性能指标**: 转换时间和内存使用情况

### 日志分析
- **结构化日志**: JSON 格式的详细日志记录
- **分类日志**: 按规则分类的转换日志
- **性能日志**: 转换时间和资源使用日志
- **错误日志**: 详细的错误堆栈和上下文信息
