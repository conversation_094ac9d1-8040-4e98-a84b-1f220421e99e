---
description:
globs:
alwaysApply: false
---
# 配置文件清理与统一化指南

## 🧹 **清理目标**

### 问题描述
项目中存在重复的 PM2 配置文件：
- ✅ **根目录**: [ecosystem.config.js](mdc:ecosystem.config.js) - 完整配置
- ❌ **backend目录**: `backend/ecosystem.config.js` - 冗余配置

### 清理原因
1. **避免配置冲突**：防止开发者混淆使用哪个配置文件
2. **简化部署流程**：统一使用根目录配置文件
3. **减少维护成本**：避免同步更新多个配置文件
4. **标准化项目结构**：符合单体应用的配置管理最佳实践

## 🗂️ **配置文件对比**

### 根目录配置 ([ecosystem.config.js](mdc:ecosystem.config.js))
```javascript
module.exports = {
  apps: [
    {
      name: 'hbs2sline-backend',
      script: './backend/server.js',     // ✅ 正确的相对路径
      cwd: './',                         // ✅ 根目录工作目录
      log_file: './logs/backend-combined.log'  // ✅ 统一日志路径
    },
    {
      name: 'hbs2sline-frontend',
      script: 'npx',
      args: 'serve -s frontend -l 8080',
      cwd: './',                         // ✅ 根目录工作目录
      log_file: './logs/frontend-combined.log' // ✅ 统一日志路径
    }
  ],
  deploy: {                             // ✅ 生产环境部署配置
    production: { ... }
  }
};
```

### 已删除的backend配置 (`backend/ecosystem.config.js`)
```javascript
module.exports = {
  apps: [
    {
      name: 'hbs2sline-backend',
      script: './server.js',             // ❌ 仅限backend目录
      log_file: '../logs/backend-combined.log'  // ❌ 相对路径混乱
    }
  ]
  // ❌ 缺少前端配置
  // ❌ 缺少部署配置
};
```

## ✅ **清理执行**

### 删除冗余配置
```bash
# 已执行的清理操作
rm backend/ecosystem.config.js
```

### 验证服务运行
```bash
# 确认PM2服务正常运行
pm2 status
# 期望输出：两个进程都显示 "online" 状态
```

## 🎯 **标准化部署流程**

### 唯一配置文件
- **配置文件**: [ecosystem.config.js](mdc:ecosystem.config.js)（根目录）
- **工作目录**: 项目根目录 `/Users/<USER>/Documents/shopline/hbs-to-sline-website`
- **日志目录**: `./logs/` （统一管理）

### PM2 部署命令
```bash
# 在项目根目录执行
pm2 start ecosystem.config.js          # 开发环境
pm2 start ecosystem.config.js --env production  # 生产环境

# 重新加载配置
pm2 reload ecosystem.config.js

# 删除所有进程
pm2 delete ecosystem.config.js
```

### 部署脚本集成
```bash
# 使用deploy.sh脚本（推荐）
./deploy.sh pm2                        # 开发环境
ENVIRONMENT=production ./deploy.sh pm2 # 生产环境
```

## 📂 **项目结构优化**

### 清理前结构
```
hbs-to-sline-website/
├── ecosystem.config.js          # 主配置
├── backend/
│   ├── ecosystem.config.js      # ❌ 冗余配置
│   └── server.js
└── frontend/
    └── index.html
```

### 清理后结构
```
hbs-to-sline-website/
├── ecosystem.config.js          # ✅ 唯一配置
├── backend/
│   └── server.js                # ✅ 简洁目录
└── frontend/
    └── index.html
```

## 🔧 **配置管理最佳实践**

### 单体应用原则
- **一个配置文件**：避免配置分散和冲突
- **根目录管理**：便于整体项目部署
- **统一日志路径**：便于日志收集和监控

### 微服务架构对比
```markdown
# 如果是微服务架构（本项目不适用）
backend/ecosystem.config.js     # 后端独立部署
frontend/ecosystem.config.js    # 前端独立部署

# 本项目是单体应用架构（当前方案）
ecosystem.config.js             # 统一部署配置
```

### 环境隔离策略
```javascript
// 在统一配置文件中管理多环境
env: {
  NODE_ENV: 'development',
  PORT: 3000
},
env_production: {
  NODE_ENV: 'production', 
  PORT: 3000
}
```

## 🚀 **后续维护指南**

### 配置变更流程
1. **修改根目录配置**：编辑 [ecosystem.config.js](mdc:ecosystem.config.js)
2. **重新加载配置**：`pm2 reload ecosystem.config.js`
3. **验证服务状态**：`pm2 status && pm2 logs`

### 避免重复配置
- ❌ 不要在子目录创建 `ecosystem.config.js`
- ❌ 不要使用多个PM2配置文件
- ✅ 所有环境配置在根目录统一管理
- ✅ 使用 `env` 和 `env_production` 区分环境

## 📚 **相关文档**

- [PM2故障排除指南](mdc:.cursor/rules/troubleshooting-pm2.mdc) - PM2问题解决
- [调试工具指南](mdc:.cursor/rules/debugging-and-tools.mdc) - PM2部署工具
- [项目架构指南](mdc:.cursor/rules/project-guide.mdc) - 整体项目结构
