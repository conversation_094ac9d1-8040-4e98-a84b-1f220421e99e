---
description:
globs:
alwaysApply: false
---
# HBS to Sline 转换语法规则

## 🎯 规则系统概述

基于 [backend/rules.js](mdc:backend/rules.js) 的分类规则系统，包含 **29个转换规则** 和 **4个映射表**：

### 📊 规则统计
- **条件标签 (Conditional Tags)**: 7个规则
- **循环标签 (Iteration Tags)**: 9个规则
- **上下文标签 (Context Tags)**: 2个规则
- **输出标签 (Output Tags)**: 3个规则
- **部分模板标签 (Partial Tags)**: 2个规则
- **过滤器转换 (Filter Tags)**: 3个规则
- **运算符转换 (Operator Rules)**: 7个规则

### 🗂️ 映射表统计
- **过滤器映射**: 55个（字符串、数字、货币、日期、数组、URL等）
- **对象映射**: 37个（商店、产品、集合、页面、客户、购物车等）
- **循环变量映射**: 7个（@index、@first、@last等）
- **运算符映射**: 4个（&&、||、===、!==等）

## 🔄 分类转换规则

### 1. 条件标签转换 (Conditional Tags)

#### 基础条件转换
```handlebars
// Handlebars
{{#if product.available}}有库存{{/if}}

// Sline
{{#if product.available}}有库存{{/if}}
```

#### 复杂条件转换
```handlebars
// Handlebars - 复杂条件
{{#if (and product.available (gt product.price 100))}}
  高价商品有库存
{{/if}}

// Sline - 转换后
{{#if product.available and product.price > 100}}
  高价商品有库存
{{/if}}
```

#### unless 转换
```handlebars
// Handlebars
{{#unless product.available}}缺货{{/unless}}

// Sline - 转换为 if 否定形式
{{#if !(product.available)}}缺货{{/if}}
```

#### else 和 elseif 转换
```handlebars
// Handlebars
{{#if condition1}}
  情况1
{{else if condition2}}
  情况2
{{else}}
  其他情况
{{/if}}

// Sline
{{#if condition1}}
  情况1
{{#else/}}{{#if condition2}}
  情况2
{{#else/}}
  其他情况
{{/if}}{{/if}}
```

### 2. 循环标签转换 (Iteration Tags)

#### 基础循环转换
```handlebars
// Handlebars
{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

// Sline
{{#for product in products}}
  <div>{{product.title}}</div>
{{/for}}
```

#### 带别名的循环
```handlebars
// Handlebars
{{#each products as |product index|}}
  <div data-index="{{index}}">{{product.title}}</div>
{{/each}}

// Sline
{{#for product in products}}
  <div data-index="{{forloop.index0}}">{{product.title}}</div>
{{/for}}
```

#### 循环变量映射
```handlebars
// Handlebars 循环变量
{{@index}}     → {{forloop.index0}}    // 从0开始的索引
{{@first}}     → {{forloop.first}}     // 是否第一个
{{@last}}      → {{forloop.last}}      // 是否最后一个
{{@key}}       → {{forloop.key}}       // 对象键
{{this}}       → {{item}}              // 当前项
{{@root}}      → {{global}}            // 根上下文
{{@../}}       → {{../}}               // 父级上下文
```

### 3. 上下文标签转换 (Context Tags)

#### with 上下文
```handlebars
// Handlebars
{{#with product}}
  <h1>{{title}}</h1>
  <p>{{description}}</p>
{{/with}}

// Sline - 保持不变
{{#with product}}
  <h1>{{title}}</h1>
  <p>{{description}}</p>
{{/with}}
```

### 4. 输出标签转换 (Output Tags)

#### 原始输出转换
```handlebars
// Handlebars - 三重花括号输出原始HTML
{{{product.description}}}

// Sline - 使用 raw 过滤器
{{product.description | raw}}
```

#### 注释转换
```handlebars
// Handlebars - 块注释
{{!-- 这是一个注释 --}}

// Sline - 注释标签
{{#comment}}这是一个注释{{/comment}}

// Handlebars - 简单注释
{{! 简单注释}}

// Sline
{{#comment}}简单注释{{/comment}}
```

### 5. 部分模板转换 (Partial Tags)

#### 基础部分模板
```handlebars
// Handlebars
{{>product-card}}

// Sline
{{#include "product-card"}}
```

#### 带上下文的部分模板
```handlebars
// Handlebars
{{>product-card product}}

// Sline
{{#include "product-card" with product}}
```

### 6. 🆕 过滤器转换 (Filter Tags)

#### 单参数过滤器
```handlebars
// Handlebars - 辅助函数形式
{{capitalize product.title}}
{{upcase product.vendor}}
{{money product.price}}

// Sline - 管道过滤器形式
{{product.title | capitalize}}
{{product.vendor | upcase}}
{{product.price | money}}
```

#### 多参数过滤器
```handlebars
// Handlebars
{{truncate product.description 100}}
{{date product.created_at "%Y-%m-%d"}}
{{default product.sale_price product.price}}

// Sline
{{product.description | truncate: 100}}
{{product.created_at | date: "%Y-%m-%d"}}
{{product.sale_price | default: product.price}}
```

#### 链式过滤器转换
```handlebars
// Handlebars - 嵌套辅助函数
{{upcase (truncate product.title 20)}}

// Sline - 链式过滤器
{{product.title | truncate: 20 | upcase}}
```

### 7. 🆕 运算符转换 (Operator Rules)

#### 逻辑运算符
```handlebars
// Handlebars
{{#if (and condition1 condition2)}}
{{#if (or condition1 condition2)}}

// Sline
{{#if condition1 and condition2}}
{{#if condition1 or condition2}}
```

#### 比较运算符
```handlebars
// Handlebars - 辅助函数形式
{{#if (gt product.price 100)}}     // 大于
{{#if (lt product.price 50)}}      // 小于
{{#if (eq product.type "book")}}   // 等于

// Sline - 运算符形式
{{#if product.price > 100}}
{{#if product.price < 50}}
{{#if product.type == "book"}}
```

#### 严格比较转换
```handlebars
// JavaScript 风格 → Sline 风格
=== → ==
!== → !=
&&  → and
||  → or
```

## 🗺️ 对象映射表

### 商店对象 (Shop Objects)
```handlebars
{{shop.name}}              // 商店名称
{{shop.description}}       // 商店描述
{{shop.url}}              // 商店URL
{{store}} → {{shop}}      // 兼容性映射
```

### 产品对象 (Product Objects)
```handlebars
{{product.title}}          // 产品标题
{{product.price}}          // 产品价格
{{product.compare_at_price}} // 对比价格
{{product.description}}    // 产品描述
{{product.images}}         // 产品图片
{{product.variants}}       // 产品变体
{{product.tags}}           // 产品标签
{{product.handle}}         // 产品句柄
{{product.available}}      // 是否有库存
{{product.vendor}}         // 供应商
{{product.type}}           // 产品类型
```

### 集合对象 (Collection Objects)
```handlebars
{{collection.title}}       // 集合标题
{{collection.description}} // 集合描述
{{collection.products}}    // 集合中的产品
{{collection.handle}}      // 集合句柄
```

### 客户对象 (Customer Objects)
```handlebars
{{customer.email}}         // 客户邮箱
{{customer.first_name}}    // 客户名
{{customer.last_name}}     // 客户姓
```

### 购物车对象 (Cart Objects)
```handlebars
{{cart.item_count}}        // 商品数量
{{cart.total_price}}       // 总价格
{{cart.items}}             // 购物车商品
```

## 🔧 过滤器映射表

### 字符串过滤器
```handlebars
capitalize, downcase, upcase, truncate
strip_html, strip_newlines, newline_to_br
escape, url_encode, url_decode
strip, lstrip, rstrip
```

### 数字过滤器
```handlebars
abs, ceil, floor, round
plus, minus, times, divided_by, modulo
```

### 货币过滤器
```handlebars
money, money_with_currency
money_without_currency, money_without_trailing_zeros
```

### 日期过滤器
```handlebars
date
```

### 数组过滤器
```handlebars
size, first, last, join
sort, sort_natural, reverse, uniq
map, where, slice
```

### URL 过滤器
```handlebars
asset_url, file_url, img_url, url
```

### HTML 过滤器
```handlebars
link_to, script_tag, stylesheet_tag, img_tag
```

## ⚡ 转换处理流程

### [backend/converter.js](mdc:backend/converter.js) 转换顺序
1. **预处理** - 保存格式信息
2. **分类转换** - 按以下顺序执行：
   - conditional → iteration → context → output → partial → filter → operator
3. **对象映射** - 转换对象引用
4. **过滤器转换** - 转换辅助函数
5. **循环变量映射** - 转换循环变量
6. **运算符转换** - 转换运算符
7. **后处理** - 恢复格式

### 转换示例 - 完整流程
```handlebars
// 原始 Handlebars 模板
{{#each products as |product index|}}
  {{#if (gt product.price 100)}}
    <div class="{{#unless @first}}mt-4{{/unless}}">
      <h3>{{capitalize product.title}}</h3>
      <p>{{money product.price}}</p>
      {{{product.description}}}
    </div>
  {{/if}}
{{/each}}

// 转换后的 Sline 模板
{{#for product in products}}
  {{#if product.price > 100}}
    <div class="{{#if !(forloop.first)}}mt-4{{/if}}">
      <h3>{{product.title | capitalize}}</h3>
      <p>{{product.price | money}}</p>
      {{product.description | raw}}
    </div>
  {{/if}}
{{/for}}
```

## 🛠️ 开发工具

### 规则查询函数
```javascript
// 在 backend/rules.js 中可用的工具函数
getRulesByCategory('conditional')  // 获取条件规则
getFilterByName('capitalize')      // 获取过滤器映射
getObjectByName('product.title')   // 获取对象映射
getStats()                         // 获取规则统计
```

### CLI 测试命令
```bash
# 测试单个文件转换
node backend/cli.js examples/test-template.hbs -o output.sline

# 查看详细转换日志
node backend/cli.js input.hbs --verbose

# 预览转换结果
node backend/cli.js input.hbs --preview
```
