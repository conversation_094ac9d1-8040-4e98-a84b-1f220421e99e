---
description:
globs:
alwaysApply: false
---
# 调试和工具使用指南

## CLI 工具完整指南

### 基本命令
主要通过 [hbs-to-sline/cli.js](mdc:hbs-to-sline/cli.js) 提供命令行接口。

#### 单文件转换
```bash
# 基本转换
node hbs-to-sline/cli.js input.hbs -o output.sline

# 预览模式（不保存文件）
node hbs-to-sline/cli.js input.hbs --preview

# 详细日志模式
node hbs-to-sline/cli.js input.hbs -o output.sline --verbose

# 模拟运行（不创建实际文件）
node hbs-to-sline/cli.js input.hbs -o output.sline --dry-run

# 监视模式（文件变化时自动转换）
node hbs-to-sline/cli.js input.hbs -o output.sline --watch

# 转换前备份原文件
node hbs-to-sline/cli.js input.hbs -o output.sline --backup
```

#### 批量转换
```bash
# 批量转换命令
node hbs-to-sline/cli.js batch "**/*.hbs" -o ./converted/

# 使用自定义配置文件
node hbs-to-sline/cli.js batch "**/*.hbs" -o ./converted/ --config ./my-config.js

# 批量转换并显示详细日志
node hbs-to-sline/cli.js batch "**/*.hbs" -o ./converted/ --verbose --backup
```

#### 配置和验证
```bash
# 初始化默认配置文件
node hbs-to-sline/cli.js init

# 强制覆盖现有配置文件
node hbs-to-sline/cli.js init --force

# 验证转换结果
node hbs-to-sline/cli.js validate output.sline
```

## 配置文件详解

### 配置文件位置
- 默认配置文件：`./hbs2sline.config.js`
- 自定义配置文件：通过 `--config` 参数指定

### 完整配置示例
```javascript
module.exports = {
  // 自定义助手函数映射
  helperMappings: {
    'custom_helper': 'custom_filter',
    'format_date': 'date',
    'get_price': 'money',
    'truncate_words': 'truncatewords'
  },
  
  // 自定义转换规则
  customRules: [
    {
      name: '自定义if语法',
      pattern: /\{\{\#custom_if\s+(.+?)\}\}/g,
      replacement: '{{#if $1}}'
    },
    {
      name: '自定义循环语法',
      pattern: /\{\{\#custom_each\s+(.+?)\}\}/g,
      replacement: '{{#for item in $1}}'
    }
  ],
  
  // 转换选项
  preserveComments: true,      // 保留注释
  logErrors: true,             // 记录错误日志
  strictMode: false,           // 严格模式
  enableCategoryLogging: true, // 启用分类日志
  
  // 输出选项
  outputExtension: '.sline',   // 输出文件扩展名
  indentSize: 2               // 缩进大小
};
```

## 调试功能详解

### 日志级别
转换器使用分层日志系统，包含以下级别：
- **info**: 一般信息日志
- **warning**: 警告信息
- **error**: 错误信息
- **success**: 成功信息

### 详细日志输出
```bash
# 启用详细日志
node hbs-to-sline/cli.js input.hbs --verbose

# 输出示例：
# [INFO] 转换开始
# [INFO] 应用 conditional 规则: 转换if语句
# [INFO] 应用 iteration 规则: 转换each循环
# [INFO] 转换过滤器: truncate → truncate
# [INFO] 转换对象: this → current
# [SUCCESS] 转换完成
```

### 分类统计信息
启用 `enableCategoryLogging` 后，会显示每个分类的转换统计：
```bash
=== 转换分类统计 ===
conditional: 5 个规则
iteration: 3 个规则
output: 12 个规则
总计应用规则: 20 个
```

## 转换器内部调试

### 使用转换器 API
```javascript
const { HandlebarsToSlineConverter } = require('./hbs-to-sline/converter');

// 创建转换器实例
const converter = new HandlebarsToSlineConverter({
  logErrors: true,
  enableCategoryLogging: true
});

// 转换内容
const result = converter.convert(handlebarsContent);

// 获取转换统计
const stats = converter.getStats();
console.log('转换统计:', stats);

// 获取规则系统统计
const ruleStats = converter.getRuleStats();
console.log('规则统计:', ruleStats);

// 列出指定分类的规则
const conditionalRules = converter.listRulesByCategory('conditional');
console.log('条件规则:', conditionalRules);
```

### 转换日志分析
```javascript
// 获取转换日志
const logs = converter.getStats().logs;

// 按级别过滤日志
const errors = logs.filter(log => log.level === 'error');
const warnings = logs.filter(log => log.level === 'warning');

// 分析转换问题
errors.forEach(error => {
  console.log(`错误: ${error.message} (${error.timestamp})`);
});
```

## 常见问题调试

### 转换结果不符合预期
1. **启用详细日志**:
   ```bash
   node hbs-to-sline/cli.js input.hbs --verbose --preview
   ```

2. **检查规则应用情况**:
   - 查看日志中的规则应用记录
   - 确认规则匹配模式是否正确

3. **使用分类转换测试**:
   ```javascript
   // 测试特定分类的转换
   const result = converter.convertByCategory(input, 'conditional');
   ```

### 批量转换失败
1. **检查文件匹配模式**:
   ```bash
   # 先列出匹配的文件
   ls **/*.hbs
   ```

2. **使用 dry-run 模式测试**:
   ```bash
   node hbs-to-sline/cli.js batch "**/*.hbs" --dry-run
   ```

3. **逐个文件测试**:
   ```bash
   # 对每个文件单独测试
   for file in **/*.hbs; do
     node hbs-to-sline/cli.js "$file" --preview
   done
   ```

### 自定义规则不生效
1. **检查配置文件加载**:
   ```bash
   # 确认配置文件被正确加载
   node hbs-to-sline/cli.js input.hbs --config ./custom-config.js --verbose
   ```

2. **验证正则表达式**:
   ```javascript
   // 在 Node.js 中测试正则表达式
   const pattern = /\{\{\#custom_if\s+(.+?)\}\}/g;
   const testString = '{{#custom_if condition}}';
   console.log(pattern.test(testString));
   ```

3. **检查规则执行顺序**:
   - 自定义规则在内置规则之前执行
   - 确保规则之间没有冲突

## 性能调试

### 转换性能分析
```javascript
// 测量转换时间
const startTime = Date.now();
const result = converter.convert(largeTemplate);
const endTime = Date.now();
console.log(`转换耗时: ${endTime - startTime}ms`);
```

### 内存使用监控
```javascript
// 监控内存使用
const used = process.memoryUsage();
console.log('内存使用:', {
  rss: Math.round(used.rss / 1024 / 1024 * 100) / 100 + ' MB',
  heapTotal: Math.round(used.heapTotal / 1024 / 1024 * 100) / 100 + ' MB',
  heapUsed: Math.round(used.heapUsed / 1024 / 1024 * 100) / 100 + ' MB'
});
```

## 高级调试技巧

### 规则匹配调试
```javascript
// 自定义规则匹配调试函数
function debugRuleMatching(input, rules) {
  rules.forEach(rule => {
    const matches = input.match(rule.pattern);
    if (matches) {
      console.log(`规则 "${rule.name}" 匹配到:`, matches);
    }
  });
}
```

### 分步转换调试
```javascript
// 分步执行转换过程
let output = input;
console.log('原始内容:', output);

output = converter.convertByCategory(output, 'conditional');
console.log('条件转换后:', output);

output = converter.convertByCategory(output, 'iteration');
console.log('循环转换后:', output);

output = converter.convertFilters(output);
console.log('过滤器转换后:', output);
```

### 错误堆栈追踪
```javascript
try {
  const result = converter.convert(input);
} catch (error) {
  console.error('转换错误:', error.message);
  console.error('错误堆栈:', error.stack);
  
  // 获取转换器内部错误
  const converterErrors = converter.getStats().errors;
  converterErrors.forEach(err => {
    console.error('内部错误:', err);
  });
}
```

# 调试工具与开发环境指南

## 🛠️ 开发工具套件

### 命令行工具 (CLI)
- [backend/cli.js](mdc:backend/cli.js) - 完整的命令行转换工具 (545行)
  - 支持单文件、批量文件和监视模式转换
  - 提供详细的转换日志和统计信息
  - 集成验证和预览功能

### Web 界面调试
- [frontend/index.html](mdc:frontend/index.html) - Web 转换界面 (216行)
- [frontend/app.js](mdc:frontend/app.js) - 前端应用逻辑 (885行)
  - 实时转换预览
  - 文件上传和下载
  - 转换规则查看器
  - 转换统计展示

## 🚀 部署和进程管理

### PM2 进程管理
- [ecosystem.config.js](mdc:ecosystem.config.js) - 统一PM2配置文件（根目录）
  - **后端进程**: `hbs2sline-backend` (端口 3000)
  - **前端进程**: `hbs2sline-frontend` (端口 8080)
  - 支持开发和生产环境配置
  - 自动重启和内存限制管理
  - **注意**: 只使用根目录配置，backend子目录配置已删除

### 部署脚本
- [deploy.sh](mdc:deploy.sh) - 自动化部署脚本 (224行)
  - 支持开发和生产环境部署
  - PM2 集成和服务管理
  - 健康检查和错误处理

## 📊 调试和监控工具

### CLI 调试命令

#### 基础转换命令
```bash
# 单文件转换
node backend/cli.js input.hbs -o output.sline

# 批量转换
node backend/cli.js batch "**/*.hbs" -o ./converted/

# 监视模式 (文件变化时自动转换)
node backend/cli.js input.hbs -w -o output.sline
```

#### 调试和分析选项
```bash
# 详细模式 - 显示所有转换步骤
node backend/cli.js input.hbs --verbose

# 预览模式 - 不写入文件，只显示结果
node backend/cli.js input.hbs --preview

# 统计模式 - 显示详细的转换统计
node backend/cli.js input.hbs --stats

# 规则分析 - 显示应用的规则
node backend/cli.js input.hbs --show-rules
```

### API 调试接口

#### 转换 API 测试
```bash
# 文本转换测试
curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"content":"{{#if product}}{{product.title}}{{/if}}"}'

# 文件上传转换测试
curl -X POST http://localhost:3000/api/convert-file \
  -F "file=@examples/test-template.hbs"
```

#### 规则查询 API
```bash
# 获取所有转换规则
curl http://localhost:3000/api/rules

# 获取指定分类规则
curl http://localhost:3000/api/rules/conditional

# 获取过滤器映射
curl http://localhost:3000/api/filters

# 获取对象映射
curl http://localhost:3000/api/objects
```

### 转换器内部调试

#### 启用详细日志
```javascript
// 在 backend/converter.js 中启用分类日志
const converter = new HandlebarsToSlineConverter({
  enableCategoryLogging: true,
  logErrors: true,
  strictMode: false
});

// 获取转换统计
const result = converter.convert(input);
const stats = converter.getStats();
console.log('分类统计:', stats.categoryStats);
console.log('总规则数:', stats.totalRules);
console.log('应用规则数:', stats.appliedRules);
```

#### 规则查询函数
```javascript
// 在 backend/rules.js 中可用的调试函数
const rules = require('./rules');

// 按分类查询规则
const conditionalRules = rules.getRulesByCategory('conditional');
console.log('条件规则数量:', conditionalRules.length);

// 查询过滤器映射
const filter = rules.getFilterByName('capitalize');
console.log('过滤器映射:', filter);

// 查询对象映射
const object = rules.getObjectByName('product.title');
console.log('对象映射:', object);

// 获取全部统计
const ruleStats = rules.getStats();
console.log('规则系统统计:', ruleStats);
```

## 📁 日志系统

### PM2 日志管理
```bash
# 查看所有进程状态
pm2 status

# 查看实时日志
pm2 logs

# 查看指定进程日志
pm2 logs hbs2sline-backend
pm2 logs hbs2sline-frontend

# 查看进程详细信息
pm2 describe hbs2sline-backend

# 清空日志
pm2 flush

# 重新加载日志
pm2 reloadLogs
```

### 日志文件位置
- `logs/backend-combined.log` - 后端综合日志
- `logs/backend-out.log` - 后端标准输出
- `logs/backend-error.log` - 后端错误日志
- `logs/frontend-combined.log` - 前端综合日志
- `logs/frontend-out.log` - 前端标准输出
- `logs/frontend-error.log` - 前端错误日志

### 日志分析工具
```bash
# 实时监控错误日志
tail -f logs/backend-error.log

# 搜索特定错误
grep "转换失败" logs/backend-combined.log

# 统计转换次数
grep "转换完成" logs/backend-combined.log | wc -l

# 分析转换性能
grep "耗时" logs/backend-combined.log | tail -10
```

## 🔧 开发环境配置

### 本地开发启动
```bash
# 方式1: 使用部署脚本
./deploy.sh start

# 方式2: 手动启动
npm run start:backend   # 启动后端 (端口 3000)
npm run start:frontend  # 启动前端 (端口 8080)

# 方式3: 开发模式
npm run dev:backend     # 开发模式后端
npm run dev:frontend    # 开发模式前端
```

### 生产环境部署
```bash
# PM2 部署
./deploy.sh pm2
ENVIRONMENT=production ./deploy.sh pm2

# 服务管理
npm run pm2:status      # 查看状态
npm run pm2:logs        # 查看日志
npm run pm2:restart     # 重启服务
npm run pm2:stop        # 停止服务
```

### 环境变量配置
```bash
# 开发环境
NODE_ENV=development
PORT=3000
BACKEND_PORT=3000
FRONTEND_PORT=8080

# 生产环境
NODE_ENV=production
PORT=3000
```

## 🧪 测试和验证

### 单元测试
```bash
# 运行后端测试
cd backend && npm test

# 运行前端测试
cd frontend && npm test
```

### 转换质量检查
```bash
# 验证转换结果
node backend/cli.js validate output.sline

# 比较转换前后
node backend/cli.js compare input.hbs output.sline

# 批量验证
node backend/cli.js validate-batch "converted/**/*.sline"
```

### 示例文件测试
- [examples/test-template.hbs](mdc:examples/test-template.hbs) - 基础语法测试
- [examples/demo-new-rules.hbs](mdc:examples/demo-new-rules.hbs) - 新规则演示
- [examples/test-template.html](mdc:examples/test-template.html) - 完整页面测试

## 🐛 故障排除

### 常见问题诊断

#### 端口冲突问题
```bash
# 检查端口占用
lsof -i :3000  # 检查后端端口
lsof -i :8080  # 检查前端端口

# 修改端口配置
export PORT=3001
export FRONTEND_PORT=8081
```

#### 权限问题
```bash
# 确保部署脚本有执行权限
chmod +x deploy.sh

# 检查文件权限
ls -la logs/
sudo chown -R $USER:$USER logs/
```

#### 依赖问题
```bash
# 清理依赖重新安装
npm run clean
npm run install:all

# 检查 Node.js 版本
node --version  # 需要 >= 14.0.0
npm --version   # 需要 >= 6.0.0
```

#### PM2 进程问题
```bash
# 重启 PM2 进程
pm2 restart all

# 删除异常进程
pm2 delete all
pm2 start ecosystem.config.js

# 重置 PM2
pm2 kill
pm2 start ecosystem.config.js
```

### 转换错误调试

#### 语法错误处理
```javascript
// 在转换器中启用严格模式
const converter = new HandlebarsToSlineConverter({
  strictMode: true,
  logErrors: true
});

try {
  const result = converter.convert(input);
} catch (error) {
  console.error('转换错误:', error.message);
  console.error('错误位置:', error.line, error.column);
}
```

#### 规则冲突检测
```bash
# 检查规则冲突
node backend/cli.js check-rules

# 分析规则应用顺序
node backend/cli.js debug-rules input.hbs
```

## 📈 性能监控

### 转换性能分析
```javascript
// 性能计时
console.time('转换耗时');
const result = converter.convert(input);
console.timeEnd('转换耗时');

// 内存使用监控
const memUsage = process.memoryUsage();
console.log('内存使用:', {
  rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
  heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`
});
```

### API 性能监控
```bash
# API 响应时间测试
curl -w "@curl-format.txt" -s -o /dev/null \
  -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"content":"test"}'
```

### 批量处理性能
```bash
# 大文件转换测试
time node backend/cli.js large-file.hbs -o output.sline

# 批量转换性能
time node backend/cli.js batch "theme-handlebars/**/*.hbs" -o converted/
```

## 🔍 高级调试技巧

### 自定义规则调试
```javascript
// 添加自定义规则进行调试
const customRule = {
  name: '调试规则',
  category: 'debug',
  pattern: /特定语法/g,
  replacement: (match) => {
    console.log('匹配到:', match);
    return 'replacement';
  }
};

converter.options.customRules.push(customRule);
```

### 分类规则调试
```javascript
// 单独测试某个分类的规则
const result = converter.convertByCategory(input, 'conditional');
console.log('条件规则转换结果:', result);
```

### 实时调试服务器
```bash
# 启动调试服务器
node backend/server.js --debug

# 使用 Node.js 调试器
node --inspect backend/server.js
```
