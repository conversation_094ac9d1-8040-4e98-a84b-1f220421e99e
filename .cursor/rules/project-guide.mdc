---
description: 
globs: 
alwaysApply: false
---
# HBS to Sline 转换器项目指导

## 项目概述
这是一个将 Handlebars 模板语法转换为 Shopline Sline 模板语法的工具，采用前后端分离架构，基于 Shopline 官方文档的分类规则系统。

## 核心架构组件

### 🚀 前后端分离架构
- [backend/](mdc:backend) - Node.js/Express API 服务
  - [backend/server.js](mdc:backend/server.js) - Express API 服务器，提供转换接口
  - [backend/converter.js](mdc:backend/converter.js) - 核心转换器，支持分类规则处理
  - [backend/rules.js](mdc:backend/rules.js) - 完整的转换规则定义（762行，29个规则）
  - [backend/cli.js](mdc:backend/cli.js) - 命令行工具，支持批量转换
- [frontend/](mdc:frontend) - 前端 Web 界面
  - [frontend/index.html](mdc:frontend/index.html) - 主界面
  - [frontend/app.js](mdc:frontend/app.js) - 前端应用逻辑（885行）
  - [frontend/style.css](mdc:frontend/style.css) - 样式文件（1159行）

### 📁 主题示例和测试
- [theme-handlebars/](mdc:theme-handlebars) - Handlebars 原始主题模板
- [theme-sline/](mdc:theme-sline) - 转换后的 Sline 主题模板  
- [examples/](mdc:examples) - 转换示例和测试文件

### ⚙️ 配置和部署
- [ecosystem.config.js](mdc:ecosystem.config.js) - PM2 进程管理配置
- [deploy.sh](mdc:deploy.sh) - 自动化部署脚本
- [package.json](mdc:package.json) - 工作区配置和脚本

### 📖 文档系统
- [README.md](mdc:README.md) - 主要文档（511行，包含完整部署指南）
- [ARCHITECTURE.md](mdc:ARCHITECTURE.md) - 架构说明文档
- [RULES_SYSTEM.md](mdc:RULES_SYSTEM.md) - 分类规则系统文档（379行）
- [conversion-examples.md](mdc:conversion-examples.md) - 转换示例文档

## 🔄 分类规则系统

### 主要规则分类（基于 Shopline 官方文档）
1. **条件标签 (Conditional Tags)** - 7个规则
   - if/unless 条件转换，支持复杂表达式
2. **循环标签 (Iteration Tags)** - 9个规则  
   - each 循环转换为 for，循环变量映射
3. **上下文标签 (Context Tags)** - 2个规则
   - with 上下文处理
4. **输出标签 (Output Tags)** - 3个规则
   - 原始输出和注释转换
5. **部分模板标签 (Partial Tags)** - 2个规则
   - 部分模板包含处理
6. **过滤器转换 (Filter Tags)** - 3个规则
   - 单参数和多参数过滤器转换
7. **运算符转换 (Operator Rules)** - 7个规则
   - 逻辑和比较运算符转换

### 映射统计
- **过滤器映射**: 55个（字符串、数字、货币、日期、数组、URL等）
- **对象映射**: 37个（商店、产品、集合、页面、客户、购物车等）
- **循环变量映射**: 7个（@index、@first、@last等）
- **运算符映射**: 4个（&&、||、===、!==等）

## 🌐 API 接口

### 主要 API 端点
- `POST /api/convert` - 转换文本内容
- `POST /api/convert-file` - 转换上传文件
- `GET /api/rules` - 获取所有转换规则
- `GET /api/rules/:category` - 获取指定分类规则
- `GET /api/filters` - 获取过滤器映射

### API 响应结构
```json
{
  "success": true,
  "data": {
    "original": "原始内容",
    "converted": "转换结果", 
    "stats": "转换统计"
  }
}
```

## 🚀 部署和运行

### 开发环境启动
```bash
# 使用部署脚本（推荐）
./deploy.sh start

# 手动启动
npm run start:backend  # 启动后端 (端口 3000)
npm run start:frontend # 启动前端 (端口 8080)
```

### 生产环境部署
```bash
# PM2 部署（推荐）
./deploy.sh pm2
ENVIRONMENT=production ./deploy.sh pm2

# PM2 管理
npm run pm2:status   # 查看状态
npm run pm2:logs     # 查看日志
npm run pm2:restart  # 重启服务
```

### 命令行工具使用
```bash
# 转换单个文件
node backend/cli.js input.hbs -o output.sline

# 批量转换
node backend/cli.js batch "**/*.hbs" -o ./converted/

# 监视模式
node backend/cli.js input.hbs -w -o output.sline
```

## 🔧 开发指南

### 添加新的转换规则
1. 在 [backend/rules.js](mdc:backend/rules.js) 中按分类添加规则
2. 使用 `getRulesByCategory()` 测试规则
3. 在 [examples/](mdc:examples) 中添加测试用例
4. 更新 [RULES_SYSTEM.md](mdc:RULES_SYSTEM.md) 文档

### 调试转换功能
```bash
# 启用分类日志
converter.options.enableCategoryLogging = true

# 查看转换统计
const stats = converter.getStats()

# CLI 详细模式
node backend/cli.js input.hbs --verbose
```

### 前端开发
- 使用原生 JavaScript，无框架依赖
- 支持文件上传和实时转换预览
- 包含转换规则展示和统计功能

## 📊 监控和日志

### PM2 监控
```bash
pm2 status                    # 查看进程状态
pm2 logs hbs2sline-backend   # 查看后端日志
pm2 logs hbs2sline-frontend  # 查看前端日志
pm2 describe hbs2sline-backend # 详细信息
```

### 日志文件位置
- `logs/backend-combined.log` - 后端综合日志
- `logs/backend-error.log` - 后端错误日志
- `logs/frontend-combined.log` - 前端综合日志
- `logs/frontend-error.log` - 前端错误日志

## 🎯 关键特性

### 智能转换功能
- **分类处理**: 按规则类型分类执行转换
- **格式保持**: 保持原始模板的缩进和格式
- **错误处理**: 详细的错误报告和警告信息
- **统计分析**: 完整的转换统计和规则应用记录

### 高级功能
- **复杂条件处理**: 支持嵌套括号和复杂表达式
- **链式过滤器**: 自动转换嵌套辅助函数为链式过滤器
- **循环变量映射**: 完整的 Handlebars 循环变量支持
- **批量处理**: 支持文件夹批量转换和监视模式

## 🔍 故障排除

### 常见问题
1. **端口冲突**: 检查 3000(后端) 和 8080(前端) 端口
2. **权限问题**: 确保 deploy.sh 有执行权限
3. **依赖问题**: 使用 `npm run clean && npm run install:all`
4. **PM2 问题**: 使用 `pm2 delete all && npm run pm2:start`

### 开发工具
- 使用 `backend/cli.js --help` 查看所有命令选项
- 访问 `/api/rules` 查看当前规则配置
- 使用前端界面的"规则查看"功能了解转换详情
