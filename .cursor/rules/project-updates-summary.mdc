---
description:
globs:
alwaysApply: false
---
# 项目最新更新总结

## 🚀 最新架构更新概览

### 核心架构升级
- **前后端分离架构**: 完全分离的 backend (Node.js/Express) 和 frontend (原生 JS)
- **API 驱动设计**: RESTful API 接口提供转换服务，支持文本和文件转换
- **分类规则系统**: 按 Object、Filter、Tag 等分类组织的 29 个转换规则
- **PM2 进程管理**: 企业级部署和进程管理解决方案

### 📊 规则系统重大更新

#### 新的分类规则架构 ([backend/rules.js](mdc:backend/rules.js) - 762行)
```javascript
// 7大规则分类，29个具体规则
- 条件标签 (Conditional Tags): 7个规则
- 循环标签 (Iteration Tags): 9个规则  
- 上下文标签 (Context Tags): 2个规则
- 输出标签 (Output Tags): 3个规则
- 部分模板标签 (Partial Tags): 2个规则
- 过滤器转换 (Filter Tags): 3个规则 [新增]
- 运算符转换 (Operator Rules): 7个规则 [新增]
```

#### 4大映射表系统
```javascript
- 过滤器映射 (FILTER_MAPPINGS): 55个过滤器
- 对象映射 (OBJECT_MAPPINGS): 37个对象  
- 循环变量映射 (LOOP_VARIABLE_MAPPINGS): 7个变量
- 运算符映射 (OPERATOR_MAPPINGS): 4个运算符
```

## 🔄 转换器引擎升级

### 智能分类处理 ([backend/converter.js](mdc:backend/converter.js) - 580行)
```javascript
class HandlebarsToSlineConverter {
  // 按分类执行转换的新流程
  convertByCategory(input, category) {
    // conditional → iteration → context → output → 
    // partial → filter → operator
  }
  
  // 新增功能
  - 格式保持处理器
  - 分类统计收集器  
  - 错误追踪系统
  - 性能监控工具
}
```

### 🆕 新增转换功能

#### 过滤器转换引擎
```handlebars
// 单参数过滤器: {{helper value}} → {{value | filter}}
{{capitalize product.title}} → {{product.title | capitalize}}

// 多参数过滤器: {{helper value param}} → {{value | filter: param}}
{{truncate description 100}} → {{description | truncate: 100}}

// 链式过滤器: {{helper1 (helper2 value)}} → {{value | filter2 | filter1}}
{{upcase (truncate title 20)}} → {{title | truncate: 20 | upcase}}
```

#### 运算符转换引擎
```handlebars
// 复杂条件转换
{{#if (and product.available (gt product.price 100))}}
→ {{#if product.available and product.price > 100}}

// 比较运算符转换
{{#if (eq product.type "book")}} → {{#if product.type == "book"}}
{{#if (gt product.price 50)}} → {{#if product.price > 50}}
```

## 🌐 API 服务架构

### 完整的 API 接口 ([backend/server.js](mdc:backend/server.js) - 307行)

#### 核心转换 API
```javascript
POST /api/convert              // 文本转换
POST /api/convert-file         // 文件上传转换
GET  /api/rules               // 获取所有规则
GET  /api/rules/:category     // 获取分类规则
GET  /api/filters             // 获取过滤器映射
```

#### 标准化响应格式
```json
{
  "success": true,
  "data": {
    "original": "原始内容",
    "converted": "转换结果",
    "stats": {
      "categoryStats": {},
      "totalRules": 29,
      "appliedRules": 15
    }
  }
}
```

## 📱 前端界面升级

### 现代化 Web 界面 ([frontend/](mdc:frontend))
- **index.html** (216行) - 响应式布局设计
- **app.js** (885行) - 完整的前端应用逻辑
- **style.css** (1159行) - 现代化样式系统

#### 新增功能特性
- 🔄 **实时转换预览**: 输入即时转换显示
- 📁 **文件上传支持**: 拖拽上传 .hbs/.handlebars/.html 文件
- 📊 **转换统计展示**: 详细的规则应用统计
- 🔍 **规则查看器**: 在线浏览所有转换规则
- 💾 **结果下载**: 一键下载转换结果

## 🚀 部署系统完善

### PM2 企业级部署 ([ecosystem.config.js](mdc:ecosystem.config.js))
```javascript
apps: [
  {
    name: 'hbs2sline-backend',
    script: './backend/server.js',
    instances: 1,
    env: { NODE_ENV: 'development', PORT: 3000 },
    env_production: { NODE_ENV: 'production', PORT: 3000 }
  },
  {
    name: 'hbs2sline-frontend', 
    script: 'npx serve -s frontend -l 8080',
    instances: 1
  }
]
```

### 自动化部署脚本 ([deploy.sh](mdc:deploy.sh) - 224行)
```bash
./deploy.sh start           # 开发环境启动
./deploy.sh pm2             # PM2 部署
./deploy.sh stop            # 停止服务
ENVIRONMENT=production ./deploy.sh pm2  # 生产环境部署
```

## 🛠️ 命令行工具增强

### 功能完整的 CLI ([backend/cli.js](mdc:backend/cli.js) - 545行)
```bash
# 新增功能
node backend/cli.js --verbose          # 详细日志模式
node backend/cli.js --preview          # 预览模式
node backend/cli.js --stats            # 统计模式
node backend/cli.js batch "**/*.hbs"   # 批量转换
node backend/cli.js -w input.hbs       # 监视模式
```

## 📊 监控和日志系统

### 结构化日志系统
```
logs/
├── backend-combined.log     # 后端综合日志
├── backend-error.log        # 后端错误日志
├── frontend-combined.log    # 前端综合日志
└── frontend-error.log       # 前端错误日志
```

### PM2 监控工具
```bash
pm2 status                   # 进程状态
pm2 logs hbs2sline-backend  # 后端日志
pm2 logs hbs2sline-frontend # 前端日志
pm2 describe <app-name>     # 详细信息
```

## 📚 文档系统完善

### 全面的项目文档
- **[README.md](mdc:README.md)** (511行) - 完整的部署和使用指南
- **[ARCHITECTURE.md](mdc:ARCHITECTURE.md)** - 系统架构说明
- **[RULES_SYSTEM.md](mdc:RULES_SYSTEM.md)** (379行) - 分类规则系统详解
- **[conversion-examples.md](mdc:conversion-examples.md)** (428行) - 转换示例

### 示例和测试文件
- **[examples/](mdc:examples)** - 完整的示例和测试用例
- **[theme-handlebars/](mdc:theme-handlebars)** - 完整的 Handlebars 主题示例
- **[theme-sline/](mdc:theme-sline)** - 完整的 Sline 主题示例

## 🔧 开发工具生态

### 调试和分析工具
```javascript
// 转换器调试 API
converter.options.enableCategoryLogging = true;
const stats = converter.getStats();

// 规则查询 API
getRulesByCategory('conditional');
getFilterByName('capitalize'); 
getObjectByName('product.title');
```

### 工作区脚本系统 ([package.json](mdc:package.json))
```bash
npm run start:backend        # 启动后端
npm run start:frontend       # 启动前端
npm run deploy:pm2          # PM2 部署
npm run pm2:status          # 查看状态
npm run pm2:logs           # 查看日志
```

## 🎯 核心改进亮点

### 1. 🧠 智能转换引擎
- **分类处理**: 按规则类型分类执行，提高转换准确性
- **格式保持**: 保持原始模板的缩进和换行格式
- **错误追踪**: 详细的错误报告和调试信息

### 2. 🔌 API 优先设计
- **RESTful 架构**: 标准化的 API 接口设计
- **文件上传**: 支持 .hbs/.handlebars/.html 文件上传
- **实时转换**: 支持实时文本转换预览

### 3. 🚀 企业级部署
- **PM2 集成**: 进程守护和自动重启
- **环境分离**: 开发和生产环境独立配置
- **日志管理**: 结构化日志和监控系统

### 4. 🎨 现代化界面
- **响应式设计**: 适配桌面和移动设备
- **拖拽上传**: 直观的文件上传体验
- **实时预览**: 即时转换结果显示

## 🔄 升级影响

### 向后兼容性
- ✅ **完全兼容**: 所有原有转换规则保持有效
- ✅ **API 扩展**: 新增 API 不影响现有功能
- ✅ **CLI 增强**: 原有 CLI 命令继续支持

### 性能提升
- 🚀 **转换速度**: 分类处理提升转换效率
- 📊 **内存优化**: 更好的内存管理和资源使用
- 🔧 **错误处理**: 更快的错误定位和修复

### 开发体验
- 🛠️ **调试工具**: 丰富的调试和分析工具
- 📚 **文档完善**: 详细的使用和开发文档
- 🔍 **错误提示**: 清晰的错误信息和修复建议
