---
description:
globs:
alwaysApply: false
---
# Shopify 主题开发指南

## 主题文件结构对照

### Handlebars 主题结构
位于 [hbs-to-sline/theme-handlebars/](mdc:hbs-to-sline/theme-handlebars/) 目录：

```
theme-handlebars/
├── assets/           # 静态资源文件
│   ├── commons/      # 公用资源
│   ├── customer/     # 客户相关资源
│   ├── product/      # 产品相关资源
│   └── vendors/      # 第三方库
├── config/           # 配置文件
│   ├── settings_data.json    # 主题设置数据
│   └── settings_schema.json  # 设置架构
├── layout/           # 布局模板
├── sections/         # 区块模板
├── snippets/         # 代码片段
└── templates/        # 页面模板
```

### Sline 主题结构
位于 [hbs-to-sline/theme-sline/](mdc:hbs-to-sline/theme-sline/) 目录：

```
theme-sline/
├── public/           # 公共静态资源
│   ├── base/         # 基础样式
│   ├── components/   # 组件样式
│   └── images/       # 图片资源
├── components/       # 可复用组件
├── blocks/           # 内容块
├── sections/         # 页面区块
├── layout/           # 布局文件
├── i18n/             # 国际化文件
├── templates/        # 页面模板
├── theme.config.json # 主题配置
└── theme.schema.json # 主题架构
```

## 关键配置文件

### Handlebars 主题配置
- [hbs-to-sline/theme-handlebars/config/settings_data.json](mdc:hbs-to-sline/theme-handlebars/config/settings_data.json) - 主题设置数据
- [hbs-to-sline/theme-handlebars/config/settings_schema.json](mdc:hbs-to-sline/theme-handlebars/config/settings_schema.json) - 设置架构定义

### Sline 主题配置
- [hbs-to-sline/theme-sline/theme.config.json](mdc:hbs-to-sline/theme-sline/theme.config.json) - 主题配置文件
- [hbs-to-sline/theme-sline/theme.schema.json](mdc:hbs-to-sline/theme-sline/theme.schema.json) - 主题架构定义

## 模板文件对照

### 布局模板
| Handlebars | Sline | 用途 |
|------------|-------|------|
| `layout/theme.liquid` | `layout/theme.html` | 主布局文件 |
| `layout/password.html` | `layout/password.html` | 密码页面布局 |
| `layout/gift_card.html` | `layout/gift_card.html` | 礼品卡布局 |

### 页面模板
| Handlebars | Sline | 用途 |
|------------|-------|------|
| `templates/index.json` | `templates/index.json` | 首页模板 |
| `templates/product.json` | `templates/product.json` | 产品页模板 |
| `templates/collection.json` | `templates/collection.json` | 集合页模板 |
| `templates/cart.json` | `templates/cart.json` | 购物车模板 |
| `templates/404.json` | `templates/404.json` | 404 页面模板 |

### 组件和代码片段
| Handlebars | Sline | 说明 |
|------------|-------|------|
| `snippets/` | `components/` | 可复用代码片段 |
| `snippets/icons/` | `components/icons/` | 图标组件 |
| `snippets/product/` | `components/product/` | 产品相关组件 |

## 资源文件管理

### 静态资源路径
- **Handlebars**: `assets/` 目录下的文件
- **Sline**: `public/` 目录下的文件

### 样式文件组织
```bash
# Handlebars 样式结构
assets/
├── commons/base/     # 基础样式
├── product/         # 产品页样式
├── customer/        # 客户页样式
└── vendors/         # 第三方样式

# Sline 样式结构
public/
├── base/            # 基础样式
├── components/      # 组件样式
├── product/         # 产品页样式
└── customers/       # 客户页样式
```

### JavaScript 文件组织
```bash
# Handlebars JS 结构
assets/
├── commons/         # 公用脚本
├── product/         # 产品页脚本
└── vendors/         # 第三方库

# Sline JS 结构
public/
├── base/            # 基础脚本
├── components/      # 组件脚本
└── product/         # 产品页脚本
```

## 国际化文件

### 语言文件对照
| Handlebars | Sline | 说明 |
|------------|-------|------|
| `locales/en.json` | `i18n/en.json` | 英文翻译 |
| `locales/zh.json` | `i18n/zh.json` | 中文翻译 |
| `locales/ar.json` | `i18n/ar.json` | 阿拉伯文翻译 |

### 翻译文件结构
```json
{
  "general": {
    "title": "标题",
    "description": "描述"
  },
  "products": {
    "add_to_cart": "添加到购物车",
    "price": "价格"
  },
  "cart": {
    "title": "购物车",
    "checkout": "结账"
  }
}
```

## 开发最佳实践

### 文件命名规范
- 使用短横线分隔：`product-card.html`
- 组件文件放在 `components/` 目录
- 区块文件放在 `sections/` 目录
- 布局文件放在 `layout/` 目录

### 目录结构建议
```bash
# 按功能组织文件
components/
├── cart/            # 购物车相关组件
├── product/         # 产品相关组件
├── customer/        # 客户相关组件
└── icons/           # 图标组件

sections/
├── header/          # 头部区块
├── footer/          # 底部区块
├── product/         # 产品页区块
└── collection/      # 集合页区块
```

### 样式组织原则
1. **基础样式** (`public/base/`) - 全局样式和重置样式
2. **组件样式** (`public/components/`) - 可复用组件样式
3. **页面样式** (`public/product/`, `public/cart/` 等) - 特定页面样式

## 转换后检查清单

### 文件结构检查
- [ ] 所有 `assets/` 文件已移动到 `public/`
- [ ] `snippets/` 已重命名为 `components/`
- [ ] `locales/` 已重命名为 `i18n/`
- [ ] 配置文件已更新格式

### 模板语法检查
- [ ] `{{#each}}` 已转换为 `{{#for}}`
- [ ] `{{else}}` 已转换为 `{{#else/}}`
- [ ] `{{>}}` 已转换为 `{{#component}}`
- [ ] 过滤器语法已更新

### 资源引用检查
- [ ] CSS 文件路径已更新
- [ ] JavaScript 文件路径已更新
- [ ] 图片文件路径已更新
- [ ] 字体文件路径已更新

## 主题测试工具

### 使用 CLI 工具验证
```bash
# 验证单个模板文件
node hbs-to-sline/cli.js validate theme-sline/templates/product.json

# 批量验证所有模板
find theme-sline -name "*.html" -exec node hbs-to-sline/cli.js validate {} \;
```

### 常见验证错误
1. **语法错误**: 未转换的 Handlebars 语法
2. **路径错误**: 资源文件路径不正确
3. **配置错误**: 配置文件格式不匹配
