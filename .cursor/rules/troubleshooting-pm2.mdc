---
description: 
globs: 
alwaysApply: false
---
# PM2 启动问题故障排除指南

## 🚨 常见问题诊断与解决

### 问题症状
- PM2 进程重启次数过多（16+ 次）
- 进程状态显示为 "stopped" 或 "errored"
- 日志中出现 "ENOENT: no such file or directory" 错误
- 前端服务启动失败，提示缺少命令

### 🔧 解决步骤

#### 1. 清理 PM2 进程
```bash
# 删除所有PM2进程，重新开始
pm2 delete all

# 确认进程已清理
pm2 status
```

#### 2. 检查依赖完整性
```bash
# 检查是否缺少 serve 命令
which serve

# 如果缺少，全局安装
npm install --global serve

# 安装项目依赖
npm install --workspaces
```

#### 3. 验证服务可独立启动
```bash
# 测试后端服务
cd backend && node server.js &
curl http://localhost:3000/api/rules

# 测试前端服务  
serve -s frontend -l 8080 &
curl http://localhost:8080

# 停止测试进程
pkill -f "node.*server" && pkill -f "serve.*frontend"
```

#### 4. 使用 PM2 正确启动
```bash
# 确保在项目根目录
pwd  # 应该是 .../hbs-to-sline-website

# 启动 PM2 服务
pm2 start ecosystem.config.js

# 验证状态
pm2 status
```

### 📊 成功指标

#### PM2 状态检查
```bash
pm2 status
# 期望输出：两个进程都显示 "online" 状态，重启次数为 0
```

#### 服务功能验证
```bash
# 后端 API 检查
curl -s http://localhost:3000/api/rules | jq '.success'
# 期望返回：true

# 前端页面检查
curl -s http://localhost:8080 | grep -o '<title>.*</title>'
# 期望返回：网站标题
```

### 🛠️ 核心配置文件

#### PM2 配置 ([ecosystem.config.js](mdc:ecosystem.config.js))
- **后端配置**: `./backend/server.js` (端口 3000)
- **前端配置**: `npx serve -s frontend -l 8080` (端口 8080)
- **日志路径**: `./logs/` 目录（相对路径）
- **工作目录**: `./` (项目根目录)

#### 关键依赖
- **serve**: 前端静态文件服务器 (全局安装)
- **backend依赖**: 在 [backend/package.json](mdc:backend/package.json) 中定义
- **前端依赖**: 在 [frontend/package.json](mdc:frontend/package.json) 中定义

### 🔍 问题根因分析

#### 缺少 serve 命令
- **原因**: PM2 配置中使用 `npx serve`，但系统未安装 serve 包
- **解决**: `npm install --global serve`

#### 日志路径问题
- **原因**: PM2 在错误路径查找日志文件
- **解决**: 确保在正确的工作目录启动 PM2

#### 依赖缺失
- **原因**: 工作区依赖未正确安装
- **解决**: `npm install --workspaces`

### 📋 预防措施

#### 环境检查清单
```bash
# 1. 确认工作目录
pwd | grep "hbs-to-sline-website"

# 2. 检查必要命令
which serve && which node && which pm2

# 3. 验证依赖安装
npm list --depth=0 --workspaces

# 4. 检查日志目录
ls -la logs/

# 5. 测试配置文件
pm2 ecosystem ecosystem.config.js
```

#### 监控脚本
```bash
# 健康检查脚本
#!/bin/bash
echo "=== PM2 Health Check ==="
pm2 status
echo ""
echo "=== Backend API Test ==="
curl -f http://localhost:3000/api/rules > /dev/null && echo "✅ Backend OK" || echo "❌ Backend Failed"
echo ""
echo "=== Frontend Test ==="
curl -f http://localhost:8080 > /dev/null && echo "✅ Frontend OK" || echo "❌ Frontend Failed"
```

### 🚀 快速恢复命令

```bash
# 一键重启脚本
pm2 delete all
npm install --global serve
npm install --workspaces
pm2 start ecosystem.config.js
pm2 status
```

## 📚 相关文档

- [项目部署指南](mdc:README.md) - 完整部署说明
- [API服务架构](mdc:.cursor/rules/api-and-data-layer.mdc) - API设计文档
- [调试工具指南](mdc:.cursor/rules/debugging-and-tools.mdc) - 调试方法
