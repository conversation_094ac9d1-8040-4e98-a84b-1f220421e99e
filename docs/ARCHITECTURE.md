# 项目架构文档

## 概述

本项目已重构为前后端分离架构，便于独立开发、部署和维护。

## 项目结构

```
hbs-to-sline/
├── backend/                    # 后端 API 服务
│   ├── server.js              # Express 服务器主文件
│   ├── converter.js           # 核心转换逻辑
│   ├── rules.js               # 转换规则定义
│   ├── non-convertible-rules.js # 不可转换规则
│   ├── cli.js                 # 命令行工具
│   ├── generate-seo-content.js # SEO 内容生成
│   ├── package.json           # 后端依赖配置
│   └── Dockerfile             # 后端容器配置
├── frontend/                   # 前端 Web 界面
│   ├── index.html             # 主页面
│   ├── app.js                 # 应用逻辑
│   ├── style.css              # 样式定义
│   ├── seo-data.json          # SEO 数据
│   ├── rules-static.html      # 规则静态页面
│   ├── sitemap.xml            # 网站地图
│   ├── robots.txt             # 搜索引擎配置
│   ├── package.json           # 前端依赖配置
│   ├── nginx.conf             # Nginx 配置
│   └── Dockerfile             # 前端容器配置
├── deployment/                 # 部署文档和脚本
│   ├── deploy-ubuntu.sh       # Ubuntu 部署脚本
│   ├── UBUNTU_DEPLOYMENT.md   # Ubuntu 部署文档
│   ├── QUICK_START.md         # 快速开始指南
│   └── README.md              # 部署说明
├── theme-handlebars/          # Handlebars 主题示例
├── theme-sline/               # Sline 主题示例
├── seo-tools/                 # SEO 工具
├── docker-compose.yml         # Docker Compose 配置
├── nginx.conf                 # 反向代理配置
├── deploy.sh                  # 部署脚本
├── package.json               # 工作区配置
├── .gitignore                 # Git 忽略文件
├── README.md                  # 项目说明
└── ARCHITECTURE.md            # 架构文档（本文件）
```

## 技术栈

### 后端 (Backend)
- **运行时**: Node.js 18+
- **框架**: Express.js
- **核心功能**: 
  - 模板转换引擎
  - RESTful API 服务
  - 文件上传处理
  - 命令行工具

### 前端 (Frontend)
- **技术**: 原生 HTML5, CSS3, JavaScript (ES6+)
- **服务器**: Nginx (生产环境)
- **功能**:
  - 在线转换界面
  - 文件上传和下载
  - 实时预览
  - 响应式设计

### 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2 (可选)

## 服务架构

### 开发环境
```
[前端:8080] ←→ [后端:3000]
```

### 生产环境
```
[Nginx:80] ←→ [后端:3000]
     ↓
[静态文件服务]
```

### Docker 环境
```
[Nginx:8080] ←→ [后端容器:3000]
     ↓
[前端容器:80]
```

## API 接口

### 后端 API 端点
- `POST /api/convert` - 转换文本内容
- `POST /api/convert-file` - 转换上传文件
- `GET /api/rules` - 获取转换规则
- `GET /api/rules/:category` - 获取指定分类规则
- `GET /api/filters` - 获取过滤器映射

### 前端静态文件
- `/` - 主页面
- `/robots.txt` - 搜索引擎配置
- `/sitemap.xml` - 网站地图
- `/rules-static.html` - 规则静态页面

## 部署方式

### 1. 开发环境部署
```bash
# 方式一：使用部署脚本
./deploy.sh start

# 方式二：手动启动
cd backend && npm start &
cd frontend && npm start &
```

### 2. 生产环境部署
```bash
# 使用 PM2 管理后端进程
cd backend
npm install -g pm2
pm2 start server.js --name hbs2sline-backend

# 使用 Nginx 服务前端
sudo cp frontend/nginx.conf /etc/nginx/sites-available/hbs2sline
sudo ln -s /etc/nginx/sites-available/hbs2sline /etc/nginx/sites-enabled/
sudo cp -r frontend/* /var/www/html/
sudo systemctl restart nginx
```

### 3. Docker 部署
```bash
# 构建并启动
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 配置说明

### 环境变量
| 变量名 | 默认值 | 说明 |
|--------|-------|------|
| `NODE_ENV` | development | 环境模式 |
| `PORT` | 3000 | 后端端口 |
| `BACKEND_PORT` | 3000 | 后端服务端口 |
| `FRONTEND_PORT` | 8080 | 前端服务端口 |

### 端口分配
- **后端**: 3000 (API 服务)
- **前端**: 8080 (开发服务器)
- **Nginx**: 80 (生产环境)
- **Docker Nginx**: 8080 (反向代理)

## 开发指南

### 后端开发
```bash
cd backend
npm install
npm run dev  # 开发模式
npm test     # 运行测试
```

### 前端开发
```bash
cd frontend
npm install
npm run dev  # 开发服务器
```

### 添加新功能
1. 后端 API：修改 `backend/server.js`
2. 转换规则：修改 `backend/rules.js`
3. 前端界面：修改 `frontend/` 下的文件

## 监控和维护

### 健康检查
```bash
# 后端健康检查
curl http://localhost:3000/api/rules

# 前端健康检查
curl http://localhost:8080/
```

### 日志管理
- 后端日志：`console.log` 输出
- Docker 日志：`docker-compose logs`
- PM2 日志：`pm2 logs`

### 备份策略
- 代码：Git 版本控制
- 配置：独立配置文件
- 数据：无需备份（无状态服务）

## 安全考虑

### 后端安全
- CORS 配置
- 文件上传限制
- 请求大小限制
- 错误处理

### 前端安全
- CSP 头设置
- XSS 防护
- 文件类型验证

### 部署安全
- 非 root 用户运行
- 端口访问控制
- HTTPS 配置（生产环境）

## 故障排除

### 常见问题
1. **端口冲突**: 修改环境变量
2. **权限问题**: 检查文件权限
3. **依赖问题**: 清除 `node_modules` 重新安装
4. **Docker 问题**: 检查 Docker 服务状态

### 调试方法
```bash
# 查看服务状态
./deploy.sh stop
./deploy.sh start

# 查看 Docker 状态
docker-compose ps
docker-compose logs backend
docker-compose logs frontend
```

## 扩展建议

### 性能优化
- 启用 Gzip 压缩
- 静态文件缓存
- API 响应缓存
- 负载均衡

### 功能扩展
- 用户认证系统
- 转换历史记录
- 批量转换 API
- 插件系统

### 监控告警
- 服务健康监控
- 性能指标收集
- 错误日志告警
- 资源使用监控

---

**维护者**: Shopline Theme Developer  
**文档版本**: 1.0.0  
**最后更新**: 2024年 