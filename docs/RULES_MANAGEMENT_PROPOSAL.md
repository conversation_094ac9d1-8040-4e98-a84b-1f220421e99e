# 转换规则管理方案

## 🚨 当前问题

删除后端后，转换规则现在硬编码在 WASM 源码中，导致：

1. **维护困难**: 每次更新规则需要重新编译 WASM
2. **开发复杂**: 需要 AssemblyScript 知识
3. **灵活性不足**: 无法动态调整规则

## 💡 推荐解决方案

### 方案 1: 外部规则配置 + WASM 引擎 (推荐)

```
frontend/
├── rules/                     # 规则配置目录
│   ├── handlebars/            # Handlebars 规则
│   │   ├── objects.json       # 对象映射规则
│   │   ├── filters.json       # 过滤器规则
│   │   ├── conditional.json   # 条件标签规则
│   │   ├── iteration.json     # 循环标签规则
│   │   └── ...
│   ├── liquid/                # Liquid 规则
│   │   ├── objects.json
│   │   ├── filters.json
│   │   └── ...
│   └── sline/                 # Sline 语法定义
│       └── grammar.json
├── wasm-converter.js          # WASM 包装器（加载规则）
└── ...
```

#### 优势
- ✅ **易于维护**: 规则以 JSON 格式存储，易于编辑
- ✅ **动态加载**: 运行时加载规则，无需重编译
- ✅ **版本控制**: 规则变更可以独立版本控制
- ✅ **高性能**: WASM 引擎处理转换逻辑
- ✅ **可扩展**: 支持插件式规则扩展

#### 实现方式
```javascript
// wasm-converter.js
class WasmConverter {
    constructor() {
        this.rules = null;
        this.wasmModule = null;
    }
    
    async initialize() {
        // 加载规则配置
        this.rules = await this.loadRules();
        
        // 初始化 WASM 模块
        this.wasmModule = await this.loadWasm();
        
        // 将规则传递给 WASM
        this.wasmModule.setRules(this.rules);
    }
    
    async loadRules() {
        const handlebarsRules = await Promise.all([
            fetch('/rules/handlebars/objects.json').then(r => r.json()),
            fetch('/rules/handlebars/filters.json').then(r => r.json()),
            // ... 其他规则文件
        ]);
        
        return { handlebars: handlebarsRules, liquid: liquidRules };
    }
}
```

### 方案 2: 规则编辑器 + 热重载

```
frontend/
├── rules-editor.html          # 规则编辑界面
├── rules/                     # 规则配置
└── wasm-converter.js          # 支持热重载
```

#### 特性
- 🎨 **可视化编辑**: Web 界面编辑规则
- 🔄 **热重载**: 规则更新后立即生效
- 🧪 **实时测试**: 编辑规则时实时预览效果
- 📊 **规则统计**: 显示规则使用情况和性能

### 方案 3: 混合模式（核心规则 + 扩展规则）

```
wasm/                          # 核心规则（编译到 WASM）
├── core-rules/                # 高频使用的核心规则
└── ...

frontend/
├── rules/                     # 扩展规则（动态加载）
│   ├── custom/                # 自定义规则
│   ├── experimental/          # 实验性规则
│   └── overrides/             # 规则覆盖
└── ...
```

## 🛠️ 实施计划

### 阶段 1: 规则提取 (1-2 天)
1. 从 WASM 源码中提取所有规则
2. 转换为 JSON 配置格式
3. 创建规则加载器

### 阶段 2: WASM 重构 (2-3 天)
1. 修改 WASM 代码支持外部规则
2. 实现规则动态加载接口
3. 保持转换性能

### 阶段 3: 工具开发 (1-2 天)
1. 开发规则验证工具
2. 创建规则编辑界面
3. 实现热重载功能

### 阶段 4: 文档和测试 (1 天)
1. 编写规则管理文档
2. 创建规则更新流程
3. 完善测试覆盖

## 📋 规则文件格式示例

### objects.json
```json
{
  "version": "1.0.0",
  "category": "objects",
  "mappings": {
    "shop": "shop",
    "store": "shop",
    "product.title": "product.title",
    "product.price": "product.price"
  }
}
```

### conditional.json
```json
{
  "version": "1.0.0",
  "category": "conditional",
  "rules": [
    {
      "name": "if-statement",
      "pattern": "{{#if condition}}",
      "replacement": "{{#if condition}}",
      "description": "Basic if statement"
    }
  ]
}
```

## 🔧 规则更新流程

### 开发者更新规则
1. 编辑 `frontend/rules/` 中的 JSON 文件
2. 运行验证脚本: `npm run validate-rules`
3. 测试转换效果: 访问测试页面
4. 提交规则变更

### 用户自定义规则
1. 访问规则编辑器页面
2. 添加或修改规则
3. 实时预览效果
4. 保存到本地存储

## 🎯 推荐实施方案

**推荐采用方案 1**，理由：

1. **平衡性好**: 兼顾性能和可维护性
2. **实施简单**: 不需要大幅修改现有架构
3. **扩展性强**: 支持未来功能扩展
4. **用户友好**: 规则更新不影响用户使用

## 📞 下一步行动

1. **确认方案**: 选择最适合的实施方案
2. **规则提取**: 从 WASM 源码提取现有规则
3. **架构调整**: 修改 WASM 加载器支持外部规则
4. **工具开发**: 创建规则管理和验证工具

这样既保持了 WASM 的高性能优势，又解决了规则维护的问题。您觉得这个方案如何？
