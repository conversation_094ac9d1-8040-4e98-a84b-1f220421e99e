# HBS2Sline 分类规则系统

## 概述

HBS2Sline 现在采用基于 Shopline 官方文档的分类规则系统，按照 Object、Filter、Tag 等分类来组织转换规则，使系统更加清晰、可维护和可扩展。

## 📊 规则统计总览

### 系统规则统计
- **总规则数**: 29 个
- **条件标签规则**: 7 个（新增 3 个复杂条件处理规则）
- **循环标签规则**: 9 个（新增 5 个循环变量映射规则）
- **上下文标签规则**: 2 个
- **输出标签规则**: 3 个
- **部分模板规则**: 2 个
- **过滤器转换规则**: 3 个（**新增分类**）
- **运算符转换规则**: 7 个（**新增分类**）
- **预处理规则**: 2 个
- **后处理规则**: 3 个

### 映射统计
- **过滤器映射**: 55 个
- **对象映射**: 37 个
- **循环变量映射**: 7 个
- **运算符映射**: 4 个

## 规则分类

### 1. 标签转换规则 (Tag Rules)

#### 条件标签 (Conditional Tags) - 7 个规则
- **if 标签转换 - 带复杂条件**: `{{#if (and a (gt b 0))}}` → `{{#if a and b > 0}}`
- **if 标签转换 - 基础**: `{{#if condition}}` → `{{#if condition}}`
- **unless 标签转换 - 复杂条件**: `{{#unless (and a b)}}` → `{{#if !(a and b)}}`
- **unless 标签转换 - 基础**: `{{#unless condition}}` → `{{#if !(condition)}}`
- **else 标签转换**: `{{else}}` → `{{#else/}}`
- **unless 结束标签**: `{{/unless}}` → `{{/if}}`
- **elseif 标签转换**: `{{else if condition}}` → `{{#else/}}{{#if condition}}`

#### 循环标签 (Iteration Tags) - 9 个规则
- **each 循环转换 - 带别名和索引**: `{{#each items as |item index|}}` → `{{#for item in items}}`
- **each 循环转换 - 带别名**: `{{#each items as |item|}}` → `{{#for item in items}}`
- **each 循环转换 - 基础**: `{{#each items}}` → `{{#for item in items}}`
- **each 结束标签**: `{{/each}}` → `{{/for}}`
- **循环索引变量转换**: `{{@index}}` → `{{forloop.index0}}`
- **循环第一项变量转换**: `{{@first}}` → `{{forloop.first}}`
- **循环最后项变量转换**: `{{@last}}` → `{{forloop.last}}`
- **循环键值变量转换**: `{{@key}}` → `{{forloop.key}}`
- **当前项变量转换**: `{{this}}` → `{{item}}`

#### 上下文标签 (Context Tags) - 2 个规则
- **with 上下文转换**: `{{#with object}}` → `{{#with object}}`
- **with 结束标签**: `{{/with}}` → `{{/with}}`

#### 输出标签 (Output Tags) - 3 个规则
- **原始输出转换**: `{{{content}}}` → `{{content | raw}}`
- **注释转换**: `{{!-- comment --}}` → `{{#comment}}comment{{/comment}}`
- **简单注释转换**: `{{! comment}}` → `{{#comment}}comment{{/comment}}`

#### 部分模板标签 (Partial Tags) - 2 个规则
- **部分模板转换 - 带上下文**: `{{>partial context}}` → `{{#include "partial" with context}}`
- **部分模板转换 - 基础**: `{{>partial}}` → `{{#include "partial"}}`

### 2. 🆕 过滤器转换规则 (Filter Tags) - 3 个规则

#### 单参数过滤器转换
- **支持过滤器**: capitalize, upcase, downcase, strip_html, escape, money, date, size, first, last, reverse, json
- **转换示例**: `{{capitalize product.title}}` → `{{product.title | capitalize}}`

#### 多参数过滤器转换
- **支持过滤器**: truncate, slice, round, date, default, plus, minus, times, divided_by
- **转换示例**: `{{truncate product.description 100}}` → `{{product.description | truncate: 100}}`

#### 链式过滤器支持
- **嵌套转换**: `{{upcase (truncate product.title 20)}}` → `{{product.title | truncate: 20 | upcase}}`

### 3. 🆕 运算符转换规则 (Operator Rules) - 7 个规则

#### 逻辑运算符转换
- **逻辑与**: `&&` → `and`
- **逻辑或**: `||` → `or`

#### 比较运算符转换
- **严格相等**: `===` → `==`
- **严格不等**: `!==` → `!=`

#### Handlebars 辅助函数转换
- **大于比较**: `gt product.price 100` → `product.price > 100`
- **小于比较**: `lt product.price 50` → `product.price < 50`
- **等于比较**: `eq product.type "book"` → `product.type == "book"`

### 4. 过滤器映射 (Filter Mappings) - 55 个过滤器

#### 字符串过滤器 (13 个)
- capitalize, downcase, upcase, truncate
- strip_html, strip_newlines, newline_to_br
- escape, url_encode, url_decode
- strip, lstrip, rstrip

#### 数字过滤器 (9 个)
- abs, ceil, floor, round
- plus, minus, times, divided_by, modulo

#### 货币过滤器 (4 个)
- money, money_with_currency
- money_without_currency, money_without_trailing_zeros

#### 日期过滤器 (1 个)
- date

#### 数组过滤器 (10 个)
- size, first, last, join
- sort, sort_natural, reverse, uniq
- map, where, slice

#### URL 过滤器 (4 个)
- asset_url, file_url, img_url, url

#### HTML 过滤器 (4 个)
- link_to, script_tag, stylesheet_tag, img_tag

#### 其他过滤器 (10 个)
- default, json
- base64_encode, base64_decode
- md5, sha1, sha256
- hmac_sha1, hmac_sha256

### 5. 对象映射 (Object Mappings) - 37 个对象

#### 商店对象 (2 个)
- shop, store

#### 产品对象 (12 个)
- product, products
- product.title, product.price, product.compare_at_price
- product.description, product.images, product.variants
- product.tags, product.handle, product.available
- product.vendor, product.type

#### 集合对象 (4 个)
- collection, collections
- collection.title, collection.description
- collection.products, collection.handle

#### 页面对象 (3 个)
- page, pages
- page_title, page_description

#### 客户对象 (4 个)
- customer
- customer.email, customer.first_name, customer.last_name

#### 购物车对象 (4 个)
- cart
- cart.item_count, cart.total_price, cart.items

#### 模板对象 (8 个)
- settings, template
- content_for_header, content_for_layout

### 6. 循环变量映射 (Loop Variable Mappings) - 7 个映射

- `@index` → `forloop.index0` (从 0 开始的索引)
- `@first` → `forloop.first` (是否是第一个)
- `@last` → `forloop.last` (是否是最后一个)
- `@key` → `forloop.key` (对象的键)
- `@root` → `global` (根上下文)
- `@../` → `../` (父级上下文)
- `this` → `item` (当前项，在 each 循环中)

### 7. 操作符映射 (Operator Mappings) - 4 个映射

- `&&` → `and`
- `||` → `or`
- `===` → `==`
- `!==` → `!=`

## 🔍 新增功能特性

### 1. 智能过滤器转换
- **自动识别**: 自动识别已知的 Handlebars 辅助函数
- **参数处理**: 支持单参数和多参数过滤器转换
- **链式转换**: 将嵌套的辅助函数转换为链式过滤器

### 2. 复杂条件处理
- **嵌套条件**: 支持括号内的复杂条件表达式
- **运算符转换**: 自动转换 gt、lt、eq 等辅助函数为运算符
- **elseif 支持**: 新增对 elseif 语法的转换支持

### 3. 增强的循环处理
- **变量映射**: 完整的循环变量映射支持
- **索引处理**: 自动转换 @index、@first、@last 等变量
- **上下文处理**: 正确处理 this 引用

### 4. 转换质量保证
- **验证函数**: 新增转换结果验证功能
- **统计报告**: 详细的转换统计和分析
- **错误检测**: 识别未转换的语法并提供警告

## API 接口

### 获取所有规则信息
```bash
GET /api/rules
```

### 获取指定分类规则
```bash
GET /api/rules/{category}
# 可用分类: conditional, iteration, context, output, partial, filter, operator
```

### 获取过滤器信息
```bash
GET /api/filters
```

### 获取对象映射信息
```bash
GET /api/objects
```

### 转换内容
```bash
POST /api/convert
Content-Type: application/json

{
  "content": "{{#each items}}{{capitalize title}}{{/each}}"
}
```

## 转换统计

转换完成后会提供详细的统计信息：

```json
{
  "rulesApplied": 8,
  "errors": 0,
  "warnings": 0,
  "categoryStats": {
    "conditional": 1,
    "iteration": 3,
    "context": 0,
    "output": 0,
    "partial": 0,
    "filter": 2,
    "operator": 2
  },
  "ruleSystemStats": {
    "totalRules": 29,
    "conditionalRules": 7,
    "iterationRules": 9,
    "contextRules": 2,
    "outputRules": 3,
    "partialRules": 2,
    "filterRules": 3,
    "operatorRules": 7,
    "totalFilters": 55,
    "totalObjects": 37,
    "preprocessRules": 2,
    "postprocessRules": 3
  },
  "conversionQuality": {
    "isValid": true,
    "warnings": [],
    "originalLength": 156,
    "convertedLength": 142,
    "filtersConverted": 2,
    "conditionalConverted": 1,
    "loopsConverted": 1
  }
}
```

## 使用示例

### 🆕 过滤器转换示例
```handlebars
<!-- Handlebars -->
{{capitalize product.title}}
{{truncate product.description 100}}
{{upcase (strip_html product.content)}}
```

```sline
<!-- Sline -->
{{product.title | capitalize}}
{{product.description | truncate: 100}}
{{product.content | strip_html | upcase}}
```

### 🆕 复杂条件转换示例
```handlebars
<!-- Handlebars -->
{{#if (and product.available (gt product.price 0))}}
  <span class="price">${{product.price}}</span>
{{else if (eq product.status "coming_soon")}}
  <span class="coming-soon">即将上市</span>
{{else}}
  <span class="unavailable">暂不可用</span>
{{/if}}
```

```sline
<!-- Sline -->
{{#if product.available and product.price > 0}}
  <span class="price">${{product.price}}</span>
{{#else/}}{{#if product.status == "coming_soon"}}
  <span class="coming-soon">即将上市</span>
{{#else/}}
  <span class="unavailable">暂不可用</span>
{{/if}}{{/if}}
```

### 🆕 增强循环转换示例
```handlebars
<!-- Handlebars -->
{{#each products as |product index|}}
  <div class="product-item {{#if @first}}first{{/if}} {{#if @last}}last{{/if}}">
    <span class="index">{{@index}}</span>
    <h3>{{capitalize product.title}}</h3>
    <p>{{truncate product.description 100}}</p>
  </div>
{{/each}}
```

```sline
<!-- Sline -->
{{#for product in products}}
  <div class="product-item {{#if forloop.first}}first{{/if}} {{#if forloop.last}}last{{/if}}">
    <span class="index">{{forloop.index0}}</span>
    <h3>{{product.title | capitalize}}</h3>
    <p>{{product.description | truncate: 100}}</p>
  </div>
{{/for}}
```

## 🛠️ 扩展规则

要添加新的转换规则，只需在相应的分类中添加规则对象：

```javascript
const NEW_RULE = {
  name: '规则名称',
  category: 'filter', // conditional, iteration, context, output, partial, filter, operator
  description: '规则描述',
  pattern: /正则表达式/g,
  replacement: '替换字符串或函数',
  examples: {
    handlebars: '{{原语法}}',
    sline: '{{新语法}}'
  }
};
```

## ✨ 特性

1. **分类清晰**: 按功能分类组织规则，易于理解和维护
2. **可扩展**: 新增规则只需添加到对应分类
3. **统计详细**: 提供转换过程的详细统计信息
4. **示例丰富**: 每个规则都有完整的转换示例
5. **文档完整**: 基于 Shopline 官方文档的权威规则
6. **API 友好**: 提供完整的 REST API 接口
7. **Web 界面**: 可视化的分类规则展示和转换工具
8. **🆕 智能转换**: 支持复杂语法的智能识别和转换
9. **🆕 质量保证**: 转换结果验证和质量评估
10. **🆕 错误处理**: 完善的错误检测和警告系统

## 🔧 技术实现

- **模块化设计**: 规则、转换器、Web 服务分离
- **错误处理**: 完善的错误捕获和日志记录
- **性能优化**: 并行处理和高效的正则表达式匹配
- **类型安全**: 完整的参数验证和类型检查
- **🆕 函数式转换**: 支持函数式转换规则，处理复杂逻辑
- **🆕 转换管道**: 多阶段转换管道，确保转换质量
- **🆕 缓存机制**: 转换结果缓存，提升性能 