# 转换规则更新指南

## 📋 概述

当 Handlebars 或 Liquid 的转换规则更新时，需要重新构建 WASM 模块以确保前端使用最新的转换逻辑。本指南详细说明了转换规则的结构和更新流程。

## 📁 转换规则文件结构

### Handlebars 转换规则
```
backend/rules/
├── tags/                           # Handlebars 标签转换规则
│   ├── if.json                     # if 标签转换规则
│   ├── each.json                   # each 标签转换规则
│   ├── unless.json                 # unless 标签转换规则
│   └── ...                         # 其他标签规则
├── mappings/                       # Handlebars 映射规则
│   ├── basic-mappings.json         # 基础映射规则
│   ├── helper-mappings.json        # 助手函数映射
│   └── ...                         # 其他映射规则
└── handlebars-to-sline.json        # 主配置文件
```

### Liquid 转换规则
```
backend/liquid-to-sline-rules/
├── tags/                           # Liquid 标签转换规则
│   ├── if.json                     # if 标签转换规则
│   ├── for.json                    # for 标签转换规则
│   ├── unless.json                 # unless 标签转换规则
│   └── ...                         # 其他标签规则
├── mappings/                       # Liquid 映射规则
│   ├── basic-mappings.json         # 基础映射规则
│   ├── filter-mappings.json        # 过滤器映射
│   └── ...                         # 其他映射规则
└── liquid-to-sline.json            # 主配置文件
```

### Sline 语法定义
```
frontend/sline-parser/grammar/
└── sline.ohm                       # Sline 语法定义文件
```

## 🔄 更新流程

### 1. 检查哪些规则需要更新

```bash
# 检查 Handlebars 规则变更
git status backend/rules/
git diff backend/rules/

# 检查 Liquid 规则变更
git status backend/liquid-to-sline-rules/
git diff backend/liquid-to-sline-rules/

# 检查 Sline 语法变更
git status frontend/sline-parser/grammar/sline.ohm
git diff frontend/sline-parser/grammar/sline.ohm
```

### 2. 使用自动检查工具

```bash
cd frontend

# 检查是否需要更新
./check-grammar-updates.sh

# 自动模式（适合 CI/CD）
./check-grammar-updates.sh --auto
```

### 3. 重新构建 WASM

```bash
# 完整构建流程
./build-wasm.sh

# 快速构建
./quick-build.sh

# 构建并测试
./build-wasm.sh --test
```

## 📝 规则文件格式

### Handlebars 标签规则示例

```json
{
  "name": "if",
  "pattern": "{{#if condition}}content{{/if}}",
  "slineOutput": "{{#if condition}}content{{/if}}",
  "description": "条件判断标签",
  "examples": [
    {
      "input": "{{#if user.isActive}}Welcome{{/if}}",
      "output": "{{#if user.isActive}}Welcome{{/if}}"
    }
  ]
}
```

### Liquid 标签规则示例

```json
{
  "name": "if",
  "pattern": "{% if condition %}content{% endif %}",
  "slineOutput": "{{#if condition}}content{{/if}}",
  "description": "Liquid 条件判断转换为 Sline",
  "examples": [
    {
      "input": "{% if user.active %}Welcome{% endif %}",
      "output": "{{#if user.active}}Welcome{{/if}}"
    }
  ]
}
```

## 🛠️ WASM 源码更新

当转换规则有重大变更时，可能需要更新 WASM 源码：

### 1. 更新转换逻辑

```typescript
// wasm/assembly/index.ts

export function convertHbsToSline(input: string): string {
    // 根据 backend/rules/ 中的规则实现转换逻辑
    return processHandlebarsRules(input);
}

export function convertLiquidToSline(input: string): string {
    // 根据 backend/liquid-to-sline-rules/ 中的规则实现转换逻辑
    return processLiquidRules(input);
}
```

### 2. 同步规则文件

确保 WASM 源码中的转换逻辑与规则文件保持一致：

```typescript
// 示例：处理 Handlebars if 标签
function processIfTag(input: string): string {
    // 实现 backend/rules/tags/if.json 中定义的转换规则
    return input.replace(
        /\{\{#if\s+(.+?)\}\}(.*?)\{\{\/if\}\}/gs,
        '{{#if $1}}$2{{/if}}'
    );
}
```

## 🧪 测试新规则

### 1. 创建测试用例

```javascript
// 在浏览器控制台中测试新规则
const testCases = [
    {
        name: "新的 if 标签规则",
        input: "{{#if newCondition}}content{{/if}}",
        expected: "{{#if newCondition}}content{{/if}}"
    },
    {
        name: "新的 Liquid for 循环",
        input: "{% for item in items %}{{item}}{% endfor %}",
        expected: "{{#for item in items}}{{item}}{{/for}}"
    }
];

for (const testCase of testCases) {
    const result = await wasmConverter.convertHandlebarsToSline(testCase.input);
    console.log(`测试: ${testCase.name}`);
    console.log(`输入: ${testCase.input}`);
    console.log(`输出: ${result.data.converted}`);
    console.log(`期望: ${testCase.expected}`);
    console.log(`通过: ${result.data.converted === testCase.expected ? '✅' : '❌'}`);
    console.log('---');
}
```

### 2. 使用测试页面

```bash
# 启动测试服务器
python3 -m http.server 8080

# 访问测试页面
# http://localhost:8080/verify-wasm.html
# http://localhost:8080/test-liquid-wasm.html
```

## 📋 更新检查清单

在更新转换规则后，请确保：

- [ ] 检查了所有相关的规则文件变更
- [ ] 更新了 WASM 源码中的转换逻辑（如需要）
- [ ] 重新构建了 WASM 模块
- [ ] 测试了新规则的转换结果
- [ ] 验证了现有规则仍然正常工作
- [ ] 更新了相关文档
- [ ] 记录了变更日志

## 🔧 常见问题

### Q: 如何知道哪些规则文件被修改了？
A: 使用 `./check-grammar-updates.sh` 脚本，它会自动检查所有规则文件的修改时间。

### Q: 规则文件更新后，WASM 转换结果不正确怎么办？
A: 
1. 检查 WASM 源码是否需要更新
2. 确认规则文件格式是否正确
3. 使用测试页面验证具体的转换案例
4. 查看浏览器控制台的错误信息

### Q: 如何添加新的转换规则？
A:
1. 在相应的规则目录中添加新的 JSON 文件
2. 更新 WASM 源码以支持新规则
3. 重新构建 WASM 模块
4. 测试新规则的转换效果

### Q: 构建失败怎么办？
A: 参考 `WASM_BUILD_GUIDE.md` 中的故障排除部分，或检查：
- Node.js 和 npm 版本是否兼容
- 规则文件 JSON 格式是否正确
- WASM 源码语法是否有错误

## 📞 支持

如果在更新转换规则时遇到问题：

1. 查看 `WASM_BUILD_GUIDE.md` 获取详细的构建指南
2. 使用 `./check-grammar-updates.sh` 检查文件状态
3. 查看构建日志中的错误信息
4. 参考本指南的常见问题部分

---

**注意**: 转换规则的更新可能会影响现有的转换结果，建议在更新前进行充分的测试。
