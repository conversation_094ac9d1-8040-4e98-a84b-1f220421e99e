# WASM 构建和更新指南

## 📋 概述

当 Sline 语法规则更新时，需要重新构建 WASM 模块以确保前端使用最新的语法解析器。本指南详细说明了完整的更新流程。

## 🔄 更新流程

### 1. 转换规则更新

当转换规则文件更新时：

**Handlebars 转换规则**:
```bash
# 查看 Handlebars 规则变更
git diff backend/rules/

# 检查具体规则文件
ls backend/rules/tags/
ls backend/rules/mappings/
```

**Liquid 转换规则**:
```bash
# 查看 Liquid 规则变更
git diff backend/liquid-to-sline-rules/

# 检查具体规则文件
ls backend/liquid-to-sline-rules/tags/
ls backend/liquid-to-sline-rules/mappings/
```

### 2. Sline 语法定义

如果 Sline 语法本身有变更：

```bash
# 查看 Sline 语法文件变更
git diff frontend/sline-parser/grammar/sline.ohm

# 确认语法定义
cat frontend/sline-parser/grammar/sline.ohm
```

### 3. WASM 模块重新构建

#### 方法 A: 使用现有构建脚本

```bash
# 进入 WASM 源码目录
cd wasm

# 重新构建 WASM 模块
npm run build

# 或者使用 AssemblyScript 直接构建
npx asc assembly/index.ts --target release --outFile build/converter.wasm
```

#### 方法 B: 手动构建流程

```bash
# 1. 清理旧的构建文件
rm -rf wasm/build/*

# 2. 安装依赖（如果需要）
cd wasm
npm install

# 3. 构建 WASM 模块
npm run build:release

# 4. 验证构建结果
ls -la build/
# 应该包含：
# - converter.wasm
# - converter.js
# - converter.d.ts
```

### 4. 更新前端 WASM 文件

```bash
# 复制新构建的 WASM 文件到前端目录
cp wasm/build/* frontend/wasm/build/

# 或者使用部署脚本
cd frontend
./setup-wasm.sh
```

### 5. 测试新版本

```bash
# 启动前端服务器
cd frontend
python3 -m http.server 8080

# 访问测试页面
# http://localhost:8080/verify-wasm.html
# http://localhost:8080/test-liquid-wasm.html
```

## 🛠️ 详细构建步骤

### 步骤 1: 准备构建环境

```bash
# 确保安装了 Node.js 和 npm
node --version
npm --version

# 安装 AssemblyScript 编译器（如果未安装）
npm install -g assemblyscript

# 进入 WASM 项目目录
cd wasm
```

### 步骤 2: 更新源码

如果转换规则有重大变更，需要更新 AssemblyScript 源码以反映新的转换逻辑：

```typescript
// wasm/assembly/index.ts
// 根据 backend/rules/ 和 backend/liquid-to-sline-rules/ 中的规则更新转换逻辑

export function convertHbsToSline(input: string): string {
    // 实现 backend/rules/ 中定义的 Handlebars 转换规则
    return processHandlebarsRules(input);
}

export function convertLiquidToSline(input: string): string {
    // 实现 backend/liquid-to-sline-rules/ 中定义的 Liquid 转换规则
    return processLiquidRules(input);
}
```

**需要同步的规则文件**:
- `backend/rules/tags/` - Handlebars 标签转换规则
- `backend/rules/mappings/` - Handlebars 映射规则
- `backend/liquid-to-sline-rules/tags/` - Liquid 标签转换规则
- `backend/liquid-to-sline-rules/mappings/` - Liquid 映射规则

### 步骤 3: 构建配置

检查 `wasm/package.json` 中的构建脚本：

```json
{
  "scripts": {
    "build": "asc assembly/index.ts --target release --outFile build/converter.wasm --bindings esm",
    "build:debug": "asc assembly/index.ts --target debug --outFile build/converter.wasm --bindings esm",
    "build:release": "asc assembly/index.ts --target release --optimize --outFile build/converter.wasm --bindings esm"
  }
}
```

### 步骤 4: 执行构建

```bash
# Debug 版本（用于开发和调试）
npm run build:debug

# Release 版本（用于生产环境）
npm run build:release

# 检查构建结果
ls -la build/
file build/converter.wasm
```

### 步骤 5: 验证构建

```bash
# 检查 WASM 文件大小和格式
wasm-objdump -h build/converter.wasm

# 或者使用简单的文件检查
hexdump -C build/converter.wasm | head -5
```

## 🧪 测试新版本

### 1. 基本功能测试

```bash
# 启动测试服务器
cd frontend
python3 -m http.server 8081

# 访问验证页面
open http://localhost:8081/verify-wasm.html
```

### 2. 语法规则测试

创建测试用例验证新语法规则：

```javascript
// 在浏览器控制台中测试
const testCases = [
    '{{#if condition}}content{{/if}}',
    '{% if condition %}content{% endif %}',
    // 添加新语法规则的测试用例
];

for (const testCase of testCases) {
    const result = await wasmConverter.convertHandlebarsToSline(testCase);
    console.log('Input:', testCase);
    console.log('Output:', result.data.converted);
    console.log('---');
}
```

### 3. 性能测试

```javascript
// 性能基准测试
const iterations = 100;
const testInput = '{{#each items}}{{name}}{{/each}}';

const times = [];
for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    await wasmConverter.convertHandlebarsToSline(testInput);
    const end = performance.now();
    times.push(end - start);
}

const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
console.log(`Average conversion time: ${avgTime.toFixed(2)}ms`);
```

## 📦 版本管理

### 1. 版本号更新

更新相关文件中的版本号：

```bash
# 更新 package.json
cd wasm
npm version patch  # 或 minor/major

# 更新 WASM 模块中的版本信息
# 编辑 assembly/index.ts 中的 getVersion() 函数
```

### 2. 构建标记

在构建时添加版本和时间戳信息：

```typescript
// assembly/index.ts
export function getVersion(): string {
    return "1.1.0";  // 更新版本号
}

export function getBuildTime(): string {
    return "2025-01-17T10:30:00Z";  // 构建时间
}
```

### 3. 文档更新

```bash
# 更新 CHANGELOG.md
echo "## [1.1.0] - $(date +%Y-%m-%d) - 语法规则更新" >> CHANGELOG.md

# 更新构建文档
echo "Last build: $(date)" >> frontend/WASM_BUILD_LOG.md
```

## 🚀 自动化构建脚本

创建自动化构建脚本：

```bash
#!/bin/bash
# build-wasm.sh

set -e

echo "🚀 Starting WASM build process..."

# 1. 清理旧文件
echo "🧹 Cleaning old build files..."
rm -rf wasm/build/*

# 2. 构建 WASM
echo "🔨 Building WASM module..."
cd wasm
npm run build:release

# 3. 复制到前端
echo "📦 Copying files to frontend..."
cp build/* ../frontend/wasm/build/

# 4. 验证构建
echo "✅ Verifying build..."
ls -la ../frontend/wasm/build/

# 5. 运行测试
echo "🧪 Running tests..."
cd ../frontend
python3 -c "
import http.server
import socketserver
import threading
import time
import webbrowser

PORT = 8082
Handler = http.server.SimpleHTTPRequestHandler

with socketserver.TCPServer(('', PORT), Handler) as httpd:
    print(f'Server running at http://localhost:{PORT}')
    threading.Thread(target=httpd.serve_forever, daemon=True).start()
    time.sleep(1)
    webbrowser.open(f'http://localhost:{PORT}/verify-wasm.html')
    input('Press Enter to stop server...')
"

echo "🎉 WASM build completed successfully!"
```

## 📋 检查清单

构建新版本时的检查清单：

- [ ] 语法文件 `sline.ohm` 已更新
- [ ] 后端转换规则已同步更新
- [ ] WASM 源码已更新（如需要）
- [ ] 版本号已更新
- [ ] WASM 模块已重新构建
- [ ] 前端 WASM 文件已更新
- [ ] 基本功能测试通过
- [ ] 新语法规则测试通过
- [ ] 性能测试通过
- [ ] 文档已更新
- [ ] 变更日志已记录

## 🔧 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查 AssemblyScript 版本
   npx asc --version
   
   # 重新安装依赖
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **WASM 加载失败**
   ```bash
   # 检查文件完整性
   file frontend/wasm/build/converter.wasm
   
   # 检查文件权限
   ls -la frontend/wasm/build/
   ```

3. **转换结果不正确**
   ```bash
   # 对比新旧版本结果
   # 检查语法规则是否正确实现
   # 查看浏览器控制台错误信息
   ```

## 📞 支持

如果在构建过程中遇到问题：

1. 检查构建日志中的错误信息
2. 验证语法文件格式是否正确
3. 确认所有依赖都已正确安装
4. 参考本指南的故障排除部分

---

**注意**: 每次语法规则更新后，建议进行完整的测试以确保所有功能正常工作。
