# WASM 集成使用指南

## 概述

本指南介绍如何在前端页面中集成和使用 WebAssembly (WASM) 高性能转换模块，实现 Handlebars/Liquid 到 Sline 的快速转换。

## 🚀 快速开始

### 1. 设置 WASM 文件

首先运行设置脚本来复制 WASM 构建文件：

```bash
cd frontend
chmod +x setup-wasm.sh
./setup-wasm.sh
```

这将创建以下文件结构：
```
frontend/
├── wasm/
│   └── build/
│       ├── converter.wasm      # WASM 二进制文件
│       ├── converter.js        # JavaScript 绑定
│       └── converter.d.ts      # TypeScript 类型定义
├── wasm-converter.js           # WASM 转换器封装
└── handlebars-to-sline.html    # 主页面（已集成 WASM）
```

### 2. 在 HTML 中引入 WASM 转换器

```html
<!-- 引入 WASM 转换器 -->
<script src="wasm-converter.js"></script>
<script src="app.js"></script>
```

### 3. 基本使用

```javascript
// 初始化 WASM 转换器
const wasmConverter = window.wasmConverter;

// 转换 Handlebars 到 Sline
const result = await wasmConverter.convertHandlebarsToSline(handlebarsCode);
if (result.success) {
    console.log('转换结果:', result.data.converted);
    console.log('统计信息:', result.data.stats);
}

// 转换 Liquid 到 Sline
const liquidResult = await wasmConverter.convertLiquidToSline(liquidCode);
```

## 🔧 API 参考

### WasmConverter 类

#### 方法

##### `initialize(): Promise<boolean>`
初始化 WASM 模块。

**返回值**: Promise<boolean> - 初始化是否成功

```javascript
const success = await wasmConverter.initialize();
if (success) {
    console.log('WASM 模块初始化成功');
}
```

##### `convertHandlebarsToSline(input: string): Promise<ConversionResult>`
转换 Handlebars 代码到 Sline 格式。

**参数**:
- `input` (string): Handlebars 模板代码

**返回值**: Promise<ConversionResult>

```javascript
const result = await wasmConverter.convertHandlebarsToSline(`
{{#if user.isActive}}
  <h1>{{user.name}}</h1>
{{/if}}
`);
```

##### `convertLiquidToSline(input: string): Promise<ConversionResult>`
转换 Liquid 代码到 Sline 格式。

**参数**:
- `input` (string): Liquid 模板代码

**返回值**: Promise<ConversionResult>

##### `isAvailable(): boolean`
检查 WASM 模块是否可用。

##### `getModuleInfo(): object`
获取 WASM 模块信息。

### 数据类型

#### ConversionResult
```typescript
interface ConversionResult {
    success: boolean;
    data?: {
        converted: string;    // 转换后的代码
        stats: {
            inputLines: number;
            inputChars: number;
            conversionTime: number;
            engine: 'WASM' | 'API';
            version: number;
        };
    };
    error?: string;
}
```

## 🎛️ 引擎切换功能

页面支持在 WASM 和 API 两种转换引擎之间切换：

### 自动切换
- 优先使用 WASM 引擎（高性能）
- 如果 WASM 不可用，自动回退到 API 引擎

### 手动切换
点击输出面板中的引擎切换按钮：
- 🚀 **WASM** - 高性能本地转换
- 📡 **API** - 服务器端转换

### 引擎状态指示
- **绿色按钮** + ⚡ 图标：WASM 引擎激活
- **灰色按钮**：API 引擎激活

## ⚡ 性能优势

### WASM vs API 性能对比

| 指标 | WASM | API |
|------|------|-----|
| 初始化时间 | ~0.68ms | N/A |
| 转换速度 | <1ms | 10-50ms |
| 网络依赖 | 无 | 需要 |
| 离线支持 | ✅ | ❌ |
| 内存占用 | 0.05MB | N/A |

### 性能测试

使用 `test-wasm-integration.html` 页面进行性能测试：

```bash
# 启动本地服务器
python3 -m http.server 8080

# 访问测试页面
open http://localhost:8080/test-wasm-integration.html
```

## 🛠️ 开发集成

### 在现有项目中集成

1. **复制必要文件**:
   ```bash
   cp wasm-converter.js your-project/
   cp -r wasm/ your-project/
   ```

2. **修改 HTML**:
   ```html
   <!-- 普通脚本标签，无需 type="module" -->
   <script src="wasm-converter.js"></script>
   ```

3. **修改 JavaScript**:
   ```javascript
   // 在转换函数中添加 WASM 支持
   async function convertCode(input) {
       try {
           // 初始化 WASM 转换器
           const success = await window.wasmConverter.initialize();
           if (success) {
               return await window.wasmConverter.convertHandlebarsToSline(input);
           } else {
               // 回退到 API 转换
               return await convertWithApi(input);
           }
       } catch (error) {
           console.warn('WASM conversion failed, using API fallback:', error);
           return await convertWithApi(input);
       }
   }
   ```

### 错误处理

```javascript
try {
    const result = await wasmConverter.convertHandlebarsToSline(input);
    if (result.success) {
        // 处理成功结果
        handleSuccess(result.data);
    } else {
        // 处理转换错误
        handleError(result.error);
    }
} catch (error) {
    // 处理异常
    console.error('WASM 转换异常:', error);
    // 可以回退到 API 转换
}
```

## 🔍 调试和测试

### 测试页面
- `test-wasm-integration.html` - 完整的 WASM 集成测试
- `handlebars-to-sline.html` - 主转换页面（已集成 WASM）

### 调试技巧

1. **检查 WASM 模块状态**:
   ```javascript
   console.log('WASM 可用:', wasmConverter.isAvailable());
   console.log('模块信息:', wasmConverter.getModuleInfo());
   ```

2. **性能监控**:
   ```javascript
   const start = performance.now();
   const result = await wasmConverter.convertHandlebarsToSline(input);
   const end = performance.now();
   console.log(`转换耗时: ${end - start}ms`);
   ```

3. **错误日志**:
   - 打开浏览器开发者工具
   - 查看 Console 标签页的 WASM 相关日志

## 📋 注意事项

### 浏览器兼容性
- 需要支持 WebAssembly 的现代浏览器
- Chrome 57+, Firefox 52+, Safari 11+, Edge 16+

### 安全考虑
- WASM 文件需要通过 HTTPS 或本地服务器提供
- 不能直接从 file:// 协议加载

### 性能优化
- WASM 模块会在首次使用时初始化
- 建议在页面加载时预初始化以获得最佳性能

## 🚀 生产部署

### 文件部署
确保以下文件部署到生产环境：
- `wasm-converter.js`
- `wasm/build/converter.wasm`
- `wasm/build/converter.js`

### CDN 优化
可以将 WASM 文件部署到 CDN 以提高加载速度：

```javascript
// 修改 wasm-converter.js 中的路径
const wasmPath = 'https://your-cdn.com/wasm/build/converter.wasm';
```

### 缓存策略
建议为 WASM 文件设置长期缓存：
```
Cache-Control: public, max-age=31536000
```

## 🔧 故障排除

### 常见问题

#### 1. "Cannot use import statement outside a module" 错误
**原因**: 脚本标签使用了 `type="module"` 但代码中有 import 语句
**解决**: 移除 `type="module"` 属性，使用普通脚本标签
```html
<!-- 错误 -->
<script type="module" src="wasm-converter.js"></script>

<!-- 正确 -->
<script src="wasm-converter.js"></script>
```

#### 2. WASM 模块加载失败
**原因**: WASM 文件路径不正确或服务器配置问题
**解决**:
- 确保 `wasm/build/converter.wasm` 文件存在
- 使用 HTTP/HTTPS 服务器，不要使用 file:// 协议
- 检查服务器是否正确提供 .wasm 文件

#### 3. 转换结果为空或错误
**原因**: WASM 模块初始化失败或函数调用错误
**解决**:
- 检查浏览器控制台错误信息
- 使用 `verify-wasm.html` 页面进行诊断
- 确保浏览器支持 WebAssembly

#### 4. 性能不如预期
**原因**: 可能使用了演示转换而非真实 WASM 转换
**解决**:
- 检查控制台是否有 "using demo conversion" 警告
- 确保 WASM 模块正确初始化
- 使用性能测试页面验证

### 调试步骤

1. **打开浏览器开发者工具**
2. **访问验证页面**: `verify-wasm.html`
3. **查看控制台日志**:
   - `🚀 Initializing WASM converter...`
   - `✅ WASM converter initialized...`
4. **检查网络请求**: 确保 WASM 文件成功加载
5. **运行测试**: 使用测试页面验证功能

## 📞 支持

如有问题或建议，请查看：
- 项目文档
- 测试页面示例 (`verify-wasm.html`, `test-final-wasm.html`)
- 控制台错误日志
- WASM 集成指南
