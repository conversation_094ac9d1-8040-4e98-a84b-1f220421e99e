# Handlebars to Sline 语法转换示例

本文档提供详细的语法转换示例，展示如何将 Handlebars 模板语法转换为 Sline 模板语法。

## 🔄 **基础语法转换**

### **1. 变量输出**

**Handlebars:**
```handlebars
<!-- 普通输出 -->
{{product.title}}
{{shop.name}}

<!-- 原始输出（不转义HTML） -->
{{{product.description}}}
{{{page.content}}}
```

**Sline:**
```sline
<!-- 普通输出 -->
{{product.title}}
{{shop.name}}

<!-- 原始输出（使用 raw 过滤器） -->
{{product.description | raw}}
{{page.content | raw}}
```

**转换要点：**
- 普通变量输出保持不变
- 三重花括号 `{{{...}}}` 转换为 `{{... | raw}}`

---

### **2. 条件语句转换**

**Handlebars:**
```handlebars
<!-- 基础条件 -->
{{#if product.available}}
  <p>商品可用</p>
{{else}}
  <p>商品不可用</p>
{{/if}}

<!-- 否定条件 -->
{{#unless product.sold_out}}
  <button>立即购买</button>
{{/unless}}

<!-- 复杂条件 -->
{{#if (and product.available (gt product.price 0))}}
  <span class="price">${{product.price}}</span>
{{/if}}
```

**Sline:**
```sline
<!-- 基础条件 -->
{{#if product.available}}
  <p>商品可用</p>
{{#else/}}
  <p>商品不可用</p>
{{/if}}

<!-- 否定条件转换为 if not -->
{{#if !(product.sold_out)}}
  <button>立即购买</button>
{{/if}}

<!-- 复杂条件（运算符转换） -->
{{#if product.available and product.price > 0}}
  <span class="price">${{product.price}}</span>
{{/if}}
```

**转换要点：**
- `{{else}}` → `{{#else/}}`
- `{{#unless condition}}` → `{{#if !(condition)}}`
- `{{/unless}}` → `{{/if}}`
- 逻辑运算符：`and`、`or` 替代 `&&`、`||`
- 比较运算符：`gt` → `>`，`lt` → `<`，`eq` → `==`

---

### **3. 循环语句转换**

**Handlebars:**
```handlebars
<!-- 基础循环 -->
{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

<!-- 带别名的循环 -->
{{#each products as |product|}}
  <div>{{product.title}}</div>
{{/each}}

<!-- 带索引的循环 -->
{{#each products as |product index|}}
  <div data-index="{{index}}">
    {{product.title}}
    {{#if @first}}<span>第一个</span>{{/if}}
    {{#if @last}}<span>最后一个</span>{{/if}}
  </div>
{{/each}}
```

**Sline:**
```sline
<!-- 基础循环 -->
{{#for item in products}}
  <div>{{item.title}}</div>
{{/for}}

<!-- 带别名的循环 -->
{{#for product in products}}
  <div>{{product.title}}</div>
{{/for}}

<!-- 带索引的循环 -->
{{#for product in products}}
  <div data-index="{{forloop.index0}}">
    {{product.title}}
    {{#if forloop.first}}<span>第一个</span>{{/if}}
    {{#if forloop.last}}<span>最后一个</span>{{/if}}
  </div>
{{/for}}
```

**转换要点：**
- `{{#each array}}` → `{{#for item in array}}`
- `{{#each array as |item|}}` → `{{#for item in array}}`
- `{{/each}}` → `{{/for}}`
- `{{this}}` → `{{item}}`（基础循环中）
- `{{@index}}` → `{{forloop.index0}}`
- `{{@first}}` → `{{forloop.first}}`
- `{{@last}}` → `{{forloop.last}}`

---

### **4. 过滤器转换**

**Handlebars:**
```handlebars
<!-- 单参数过滤器 -->
{{capitalize product.title}}
{{upcase product.vendor}}
{{money product.price}}

<!-- 多参数过滤器 -->
{{truncate product.description 100}}
{{date order.created_at "%Y-%m-%d"}}
{{default product.image "placeholder.jpg"}}

<!-- 嵌套过滤器 -->
{{upcase (truncate product.title 20)}}
```

**Sline:**
```sline
<!-- 单参数过滤器 -->
{{product.title | capitalize}}
{{product.vendor | upcase}}
{{product.price | money}}

<!-- 多参数过滤器 -->
{{product.description | truncate: 100}}
{{order.created_at | date: "%Y-%m-%d"}}
{{product.image | default: "placeholder.jpg"}}

<!-- 链式过滤器 -->
{{product.title | truncate: 20 | upcase}}
```

**转换要点：**
- `{{filter value}}` → `{{value | filter}}`
- `{{filter value param}}` → `{{value | filter: param}}`
- 嵌套过滤器转换为链式过滤器

---

### **5. 部分模板转换**

**Handlebars:**
```handlebars
<!-- 基础部分模板 -->
{{> header}}
{{> product-card}}

<!-- 带上下文的部分模板 -->
{{> product-card product}}
{{> collection-item collection}}
```

**Sline:**
```sline
<!-- 基础部分模板 -->
{{#include "header"}}
{{#include "product-card"}}

<!-- 带上下文的部分模板 -->
{{#include "product-card" with product}}
{{#include "collection-item" with collection}}
```

**转换要点：**
- `{{> template}}` → `{{#include "template"}}`
- `{{> template context}}` → `{{#include "template" with context}}`

---

### **6. 注释转换**

**Handlebars:**
```handlebars
{{!-- 这是一个块注释 --}}
{{! 这是一个行注释}}
```

**Sline:**
```sline
{{#comment}}这是一个块注释{{/comment}}
{{#comment}}这是一个行注释{{/comment}}
```

**转换要点：**
- 所有注释格式统一转换为 `{{#comment}}...{{/comment}}`

## 🔍 **复杂转换示例**

### **示例1: 产品列表页面**

**Handlebars:**
```handlebars
<div class="product-grid">
  {{#each collections.featured.products as |product index|}}
    <div class="product-item {{#if @first}}first{{/if}}">
      <h3>{{capitalize product.title}}</h3>
      
      {{#if product.images}}
        <img src="{{product.featured_image}}" alt="{{product.title}}">
      {{else}}
        <img src="placeholder.jpg" alt="No image">
      {{/if}}
      
      <div class="price">
        {{#if product.compare_at_price}}
          <span class="original">{{money product.compare_at_price}}</span>
          <span class="sale">{{money product.price}}</span>
        {{else}}
          <span class="regular">{{money product.price}}</span>
        {{/if}}
      </div>
      
      {{#unless product.available}}
        <span class="sold-out">售罄</span>
      {{/unless}}
    </div>
  {{/each}}
</div>
```

**Sline:**
```sline
<div class="product-grid">
  {{#for product in collections.featured.products}}
    <div class="product-item {{#if forloop.first}}first{{/if}}">
      <h3>{{product.title | capitalize}}</h3>
      
      {{#if product.images}}
        <img src="{{product.featured_image}}" alt="{{product.title}}">
      {{#else/}}
        <img src="placeholder.jpg" alt="No image">
      {{/if}}
      
      <div class="price">
        {{#if product.compare_at_price}}
          <span class="original">{{product.compare_at_price | money}}</span>
          <span class="sale">{{product.price | money}}</span>
        {{#else/}}
          <span class="regular">{{product.price | money}}</span>
        {{/if}}
      </div>
      
      {{#if !(product.available)}}
        <span class="sold-out">售罄</span>
      {{/if}}
    </div>
  {{/for}}
</div>
```

### **示例2: 带搜索的博客页面**

**Handlebars:**
```handlebars
{{#if blog.tags}}
  <div class="tag-filter">
    {{#each blog.tags as |tag|}}
      <a href="/blogs/{{blog.handle}}/tagged/{{tag.handle}}" 
         class="tag {{#if (eq current_tags tag.handle)}}active{{/if}}">
        {{tag.title}} ({{tag.count}})
      </a>
    {{/each}}
  </div>
{{/if}}

<div class="articles">
  {{#each articles as |article|}}
    <article class="article-item">
      <h2><a href="{{article.url}}">{{article.title}}</a></h2>
      <div class="meta">
        <time>{{date article.published_at "%B %d, %Y"}}</time>
        {{#if article.author}}<span>by {{article.author}}</span>{{/if}}
      </div>
      <div class="excerpt">
        {{truncate (strip_html article.content) 200}}
      </div>
      <div class="tags">
        {{#each article.tags as |tag|}}
          <span class="tag">{{tag}}</span>
        {{/each}}
      </div>
    </article>
  {{/each}}
</div>
```

**Sline:**
```sline
{{#if blog.tags}}
  <div class="tag-filter">
    {{#for tag in blog.tags}}
      <a href="/blogs/{{blog.handle}}/tagged/{{tag.handle}}" 
         class="tag {{#if current_tags == tag.handle}}active{{/if}}">
        {{tag.title}} ({{tag.count}})
      </a>
    {{/for}}
  </div>
{{/if}}

<div class="articles">
  {{#for article in articles}}
    <article class="article-item">
      <h2><a href="{{article.url}}">{{article.title}}</a></h2>
      <div class="meta">
        <time>{{article.published_at | date: "%B %d, %Y"}}</time>
        {{#if article.author}}<span>by {{article.author}}</span>{{/if}}
      </div>
      <div class="excerpt">
        {{article.content | strip_html | truncate: 200}}
      </div>
      <div class="tags">
        {{#for tag in article.tags}}
          <span class="tag">{{tag}}</span>
        {{/for}}
      </div>
    </article>
  {{/for}}
</div>
```

## ⚠️ **需要特殊处理的情况**

### **1. 空白符控制**

**Handlebars:**
```handlebars
{{~product.title~}}
```

**处理方案:**
```sline
{{product.title}}
<!-- 注意：Sline 可能不支持空白符控制，需要通过 CSS 处理 -->
```

### **2. 复杂嵌套条件**

**Handlebars:**
```handlebars
{{#if (and (or product.available product.preorder) (gt product.inventory 0))}}
```

**处理方案:**
```sline
<!-- 建议分解为多个条件 -->
{{#if (product.available or product.preorder) and product.inventory > 0}}
<!-- 或者使用中间变量 -->
```

### **3. 自定义辅助函数**

**Handlebars:**
```handlebars
{{customFormat product.price currency}}
```

**处理方案:**
```sline
<!-- 需要检查 Sline 是否有对应过滤器，或者重新实现逻辑 -->
{{product.price | money_with_currency}}
```

## 📋 **转换检查清单**

完成转换后，请检查以下项目：

- [ ] 所有 `{{#each}}` 都已转换为 `{{#for}}`
- [ ] 所有 `{{else}}` 都已转换为 `{{#else/}}`
- [ ] 所有 `{{#unless}}` 都已转换为 `{{#if !()}}`
- [ ] 所有 `{{{...}}}` 都已转换为 `{{... | raw}}`
- [ ] 所有 `{{>partial}}` 都已转换为 `{{#include "partial"}}`
- [ ] 所有循环变量 `@index`、`@first`、`@last` 都已转换
- [ ] 所有过滤器都已从辅助函数格式转换为管道格式
- [ ] 所有运算符都已转换（`&&` → `and`，`||` → `or`）
- [ ] 检查是否有不支持的语法需要手动处理

## 🛠️ **使用建议**

1. **分步转换**: 建议按功能模块分别转换，而不是一次性转换整个模板
2. **测试验证**: 每个模块转换后都应该进行功能测试
3. **备份原文件**: 转换前务必备份原始的 Handlebars 模板文件
4. **逐步部署**: 建议在测试环境充分验证后再部署到生产环境 