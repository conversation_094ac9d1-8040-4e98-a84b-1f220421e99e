{"designSystem": {"name": "Browser MCP Interface", "version": "1.0", "theme": "Modern Developer Tool", "colorPalette": {"primary": {"background": "#FFFFFF", "surface": "#F8F9FA", "accent": "#000000"}, "secondary": {"cardBackground": "#FFFFFF", "border": "#E5E7EB", "shadow": "rgba(0, 0, 0, 0.1)"}, "interactive": {"buttonPrimary": "#000000", "buttonSecondary": "#FFFFFF", "buttonBorder": "#D1D5DB", "hover": "#F3F4F6"}, "text": {"primary": "#111827", "secondary": "#6B7280", "muted": "#9CA3AF"}, "accent": {"gradient": "linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%)"}}, "typography": {"fontFamily": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "monospace": "SF Mono, Monaco, Consolas, monospace"}, "scale": {"hero": {"fontSize": "48px", "fontWeight": "700", "lineHeight": "1.2"}, "h1": {"fontSize": "32px", "fontWeight": "600", "lineHeight": "1.25"}, "h2": {"fontSize": "24px", "fontWeight": "600", "lineHeight": "1.3"}, "h3": {"fontSize": "20px", "fontWeight": "600", "lineHeight": "1.4"}, "body": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.5"}, "small": {"fontSize": "14px", "fontWeight": "400", "lineHeight": "1.4"}, "caption": {"fontSize": "12px", "fontWeight": "500", "lineHeight": "1.3"}}}, "spacing": {"scale": "8px base unit", "values": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px", "4xl": "96px"}, "containerMaxWidth": "1200px", "sectionPadding": "80px 24px"}, "layout": {"structure": "Single-column centered layout", "maxWidth": "1200px", "margins": "auto", "grid": {"columns": 12, "gap": "24px", "breakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1200px"}}}, "components": {"header": {"height": "80px", "background": "transparent", "position": "sticky", "alignment": "center", "elements": ["logo", "navigation"]}, "hero": {"textAlignment": "center", "spacing": "64px vertical padding", "elements": ["icon", "title", "subtitle", "cta_buttons"]}, "cards": {"background": "#FFFFFF", "borderRadius": "12px", "shadow": "0 4px 6px rgba(0, 0, 0, 0.05)", "padding": "24px", "border": "1px solid #E5E7EB", "hover": {"shadow": "0 8px 16px rgba(0, 0, 0, 0.1)", "transform": "translateY(-2px)"}}, "buttons": {"primary": {"background": "#000000", "color": "#FFFFFF", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": "500", "border": "none"}, "secondary": {"background": "#FFFFFF", "color": "#000000", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": "500", "border": "1px solid #D1D5DB"}}, "icons": {"style": "minimal line icons", "size": {"small": "16px", "medium": "24px", "large": "32px", "hero": "48px"}, "stroke": "2px", "color": "inherit"}, "grid": {"type": "responsive grid", "columns": {"mobile": 1, "tablet": 2, "desktop": 4}, "gap": "24px"}}, "visualHierarchy": {"principles": ["Center-aligned content sections", "Clear visual separation between sections", "Consistent card-based information architecture", "Progressive disclosure of information", "Emphasis through color contrast and typography weight"], "contentFlow": "Top-down linear progression", "emphasis": {"primary": "Large typography and central positioning", "secondary": "Card elevation and whitespace", "tertiary": "Icon and color coding"}}, "effects": {"shadows": {"subtle": "0 2px 4px rgba(0, 0, 0, 0.05)", "medium": "0 4px 6px rgba(0, 0, 0, 0.05)", "elevated": "0 8px 16px rgba(0, 0, 0, 0.1)"}, "transitions": {"duration": "200ms", "easing": "ease-in-out", "properties": ["transform", "box-shadow", "opacity"]}, "borderRadius": {"small": "6px", "medium": "8px", "large": "12px", "xl": "16px"}}, "responsive": {"approach": "Mobile-first", "breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px"}, "behavior": {"grid": "Collapses from 4 columns to 2 to 1", "spacing": "Reduces proportionally on smaller screens", "typography": "Scales down hero text on mobile"}}, "accessibility": {"contrast": "WCAG AA compliant", "focusStates": "Visible outline on interactive elements", "semanticStructure": "Proper heading hierarchy", "interactiveElements": "Minimum 44px touch targets"}, "designPrinciples": ["Clean and minimal aesthetic", "Developer-focused functionality", "Clear information hierarchy", "Consistent spacing and alignment", "Subtle but effective visual feedback", "Professional and trustworthy appearance"]}}