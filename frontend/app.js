// HBS2Sline Web Application - Version 2.0.1 (updateEngineStatus fix)
console.log('🔄 Loading app.js version 2.0.1 with updateEngineStatus fix');
class HBS2SlineApp {
    constructor() {
        this.highlighter = null;
        this.handlebarsTextarea = null;
        this.slineTextarea = null;
        this.handlebarsEditor = null;
        this.slineEditor = null;
        this.currentStats = null;
        this.isConverting = false;

        // WASM 转换器配置
        this.wasmConverter = null;



        this.init();
    }
    

    
    async init() {
        await this.initializeShiki();
        await this.initializeWasm();
        this.initializeEditors();
        this.bindEvents();
        this.loadExampleCode();
        this.updateStatus('Ready to convert');

        this.updateEngineStatus();

        this.addNotificationStyles();
    }
    
    // 初始化 Shiki 高亮器
    async initializeShiki() {
        try {
            if (window.createHighlighter) {
                this.highlighter = await window.createHighlighter({
                    themes: ['github-light'],
                    langs: ['html', 'handlebars', 'go']
                });
                console.log('Shi<PERSON> highlighter initialized');
            }
        } catch (error) {
            console.warn('Failed to initialize Shiki:', error);
        }
    }

    // 初始化 WASM 转换器
    async initializeWasm() {
        try {
            if (window.wasmConverter) {
                console.log('🚀 Initializing WASM converter...');
                this.updateStatus('Initializing WASM converter...');

                const initialized = await window.wasmConverter.initialize();
                if (initialized) {
                    this.wasmConverter = window.wasmConverter;
                    console.log('✅ WASM converter ready');
                    this.updateStatus('WASM converter ready');
                    this.showNotification('🚀 WASM high-performance converter loaded!', 'success');
                } else {
                    console.error('❌ WASM converter failed to initialize');
                    this.showNotification('WASM initialization failed', 'error');
                    throw new Error('WASM initialization failed');
                }
            } else {
                console.error('❌ WASM converter not available');
                this.showNotification('WASM converter not available', 'error');
                throw new Error('WASM converter not available');
            }
        } catch (error) {
            console.error('Failed to initialize WASM converter:', error);
            this.showNotification('WASM initialization failed', 'error');
            throw error;
        }
    }
    
    // 初始化代码编辑器
    initializeEditors() {
        // 获取编辑器元素
        this.handlebarsEditor = document.getElementById('handlebarsEditor');
        this.slineEditor = document.getElementById('slineEditor');
        this.handlebarsTextarea = document.getElementById('handlebarsTextarea');
        this.slineTextarea = document.getElementById('slineTextarea');
        
        // 检查元素是否存在
        if (!this.handlebarsEditor || !this.slineEditor || !this.handlebarsTextarea || !this.slineTextarea) {
            console.error('Required DOM elements not found');
            setTimeout(() => this.initializeEditors(), 100); // 重试
            return;
        }
        
        // 设置只读属性
        this.slineEditor.setAttribute('data-readonly', 'true');
        
        // 初始化占位符
        this.updatePlaceholder(this.handlebarsEditor, 'Enter or paste your Handlebars template code here...');
        this.updatePlaceholder(this.slineEditor, 'Converted Sline code will appear here...');
        
        // 绑定文本区域事件
        this.handlebarsTextarea.addEventListener('input', (e) => {
            this.updateHighlighting(this.handlebarsEditor, e.target.value, 'handlebars');
            this.updateEditorState(this.handlebarsEditor.parentElement, e.target.value);
            this.toggleDropZone();
            this.enableConvertButton();
        });
        
        // 绑定键盘事件
        this.handlebarsTextarea.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.convertCode();
            }
        });
    }
    
    // 更新语法高亮
    async updateHighlighting(editorElement, code, language) {
        if (!this.highlighter) {
            editorElement.textContent = code;
            return;
        }
        
        try {
            if (code.trim()) {
                const highlighted = this.highlighter.codeToHtml(code, {
                    lang: language,
                    theme: 'github-light'
                });
                editorElement.innerHTML = highlighted;
                editorElement.removeAttribute('data-placeholder');
            } else {
                editorElement.innerHTML = '';
                this.updatePlaceholder(editorElement, 
                    language === 'handlebars' ? 
                    'Enter or paste your Handlebars template code here...' : 
                    'Converted Sline code will appear here...'
                );
            }
        } catch (error) {
            console.warn('Highlighting error:', error);
            editorElement.textContent = code;
        }
    }
    
    // 更新占位符
    updatePlaceholder(editorElement, text) {
        editorElement.setAttribute('data-placeholder', text);
    }
    
    // 获取编辑器值
    getValue(editorType) {
        if (editorType === 'handlebars') {
            return this.handlebarsTextarea.value;
        } else {
            return this.slineTextarea.value;
        }
    }
    
    // 设置编辑器值
    async setValue(editorType, value) {
        if (editorType === 'handlebars') {
            this.handlebarsTextarea.value = value;
            await this.updateHighlighting(this.handlebarsEditor, value, 'handlebars');
            this.updateEditorState(this.handlebarsEditor.parentElement, value);
        } else {
            this.slineTextarea.value = value;
            await this.updateHighlighting(this.slineEditor, value, 'go');
            this.updateEditorState(this.slineEditor.parentElement, value);
        }
    }
    
    // 更新编辑器容器状态
    updateEditorState(containerElement, value) {
        if (value && value.trim()) {
            containerElement.setAttribute('data-has-content', 'true');
        } else {
            containerElement.removeAttribute('data-has-content');
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 转换按钮
        document.getElementById('convertBtn').addEventListener('click', () => {
            this.convertCode();
        });
        
        // 清空按钮
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAll();
        });
        
        // 下载按钮
        document.getElementById('downloadBtn').addEventListener('click', () => {
            this.downloadResult();
        });
        
        // 复制按钮
        document.getElementById('copyBtn').addEventListener('click', () => {
            this.copyResult();
        });
        
        // 格式化按钮
        document.getElementById('formatBtn').addEventListener('click', () => {
            this.formatResult();
        });
        
        // 加载示例按钮
        document.getElementById('loadExampleBtn').addEventListener('click', () => {
            this.loadExampleCode();
        });


        
        // 文件上传
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });
        
        // 拖拽上传
        this.setupDragAndDrop();
        

        

        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.convertCode();
            }
        });
    }
    
    // 设置拖拽上传
    setupDragAndDrop() {
        const dropZone = document.getElementById('dropZone');
        const editorCard = document.querySelector('.editor-card');
        
        // 检查元素是否存在
        if (!dropZone) {
            console.warn('Drop zone element not found');
            return;
        }
        
        if (!editorCard) {
            console.warn('Editor card element not found');
        }
        
        // 阻止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
            if (editorCard) {
                editorCard.addEventListener(eventName, this.preventDefaults, false);
            }
        });
        
        // 高亮拖拽区域
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('drag-over');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('drag-over');
            }, false);
        });
        
        // 处理文件拖拽
        dropZone.addEventListener('drop', (e) => {
            this.handleFileDrop(e);
        }, false);
        
        // 点击拖拽区域触发文件选择
        dropZone.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
    }
    
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // 处理文件拖拽
    handleFileDrop(e) {
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }
    
    // 处理文件选择
    handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }
    
    // 处理文件
    async handleFile(file) {
        if (!file.name.match(/\.(hbs|handlebars|html)$/)) {
            this.showNotification('Please upload a .hbs, .handlebars or .html file', 'warning');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('File size cannot exceed 5MB', 'warning');
            return;
        }
        
        try {
            this.showLoading(true);
            this.updateStatus('Reading file...');

            // 读取文件内容
            const content = await this.readFileContent(file);

            // 设置输入内容
            await this.setValue('handlebars', content);

            // 开始计时
            const startTime = performance.now();

            // 使用 WASM 转换
            const result = await this.wasmConverter.convertHandlebarsToSline(content);

            // 结束计时
            const endTime = performance.now();
            const conversionTime = Math.round(endTime - startTime);

            if (result.success && result.data) {
                const convertedContent = result.data.converted || '';
                await this.setValue('sline', convertedContent);

                // 设置统计信息
                this.currentStats = {
                    ...result.data.stats,
                    conversionTime: conversionTime
                };

                this.updateStats();
                this.enableButtons();
                this.updateStatus(`File "${file.name}" converted successfully`);
                this.showNotification(`File "${result.data.filename || 'Unknown file'}" loaded and converted successfully!`, 'success');
            } else {
                this.showNotification(result.error || 'File conversion failed', 'error');
            }
        } catch (error) {
            console.error('File upload error:', error);
            this.showNotification('File upload failed, please try again', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    // 转换代码
    async convertCode() {
        const content = this.getValue('handlebars').trim();

        if (!content) {
            this.showNotification('Please enter Handlebars code to convert', 'warning');
            return;
        }

        if (this.isConverting) {
            return;
        }

        try {
            this.isConverting = true;
            this.showLoading(true);

            // 使用 WASM 转换
            this.updateStatus('Converting with WASM...');
            const result = await this.wasmConverter.convertHandlebarsToSline(content);

            if (result.success && result.data) {
                const convertedContent = result.data.converted || '';
                await this.setValue('sline', convertedContent);

                this.currentStats = result.data.stats;
                this.updateStats();
                this.enableButtons();
                this.updateStatus('Conversion completed');

                this.showNotification('✅ Handlebars converted successfully with WASM!', 'success');
            } else {
                this.showNotification(result.error || 'Conversion failed', 'error');
            }
        } catch (error) {
            console.error('Conversion error:', error);
            this.showNotification(`Conversion failed: ${error.message}`, 'error');
        } finally {
            this.isConverting = false;
            this.showLoading(false);
        }
    }





    // 读取文件内容
    async readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    // 清空所有内容
    async clearAll() {
        await this.setValue('handlebars', '');
        await this.setValue('sline', '');
        this.currentStats = null;
        this.updateStats();
        this.disableButtons();
        this.toggleDropZone();
        this.updateStatus('Content cleared');
        this.showNotification('All content cleared', 'info');

        // 重置文件输入
        document.getElementById('fileInput').value = '';
    }
    
    // 下载结果
    downloadResult() {
        const content = this.getValue('sline');
        if (!content) {
            this.showNotification('No Sline code to download', 'warning');
            return;
        }

        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'converted-template.sline';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.updateStatus('File downloaded');
        this.showNotification('Sline code downloaded!', 'success');
    }
    
    // 复制结果
    copyResult() {
        const content = this.getValue('sline');
        if (!content) {
            this.showNotification('No Sline code to copy', 'warning');
            return;
        }

        navigator.clipboard.writeText(content).then(() => {
            this.updateStatus('Content copied to clipboard');
            this.showNotification('Sline code copied to clipboard!', 'success');
        }).catch(() => {
            // 备用方案
            this.slineEditor.select();
            document.execCommand('copy');
            this.updateStatus('Content copied to clipboard');
            this.showNotification('Sline code copied to clipboard!', 'success');
        });
    }
    
    // 格式化结果
    async formatResult() {
        const content = this.getValue('sline');
        if (!content) return;
        
        // 简单的格式化逻辑
        const formatted = content
            .replace(/\s*\{\{/g, '{{')
            .replace(/\}\}\s*/g, '}}')
            .replace(/\n\s*\n/g, '\n')
            .trim();
        
        await this.setValue('sline', formatted);
        this.updateStatus('Code formatted');
    }
    
    // 加载示例代码
    async loadExampleCode() {
        const exampleCode = `<!-- Handlebars Example Template -->
<div class="product-card">
    <h2>{{product.title}}</h2>
    <p class="price">{{money product.price}}</p>
    
    {{#if product.available}}
        <span class="badge available">In Stock</span>
    {{else}}
        <span class="badge unavailable">Out of Stock</span>
    {{/if}}
    
    {{#each product.images}}
        <img src="{{this.url}}" alt="{{this.alt}}" loading="lazy">
    {{/each}}
    
    {{#unless product.tags.empty}}
        <div class="tags">
            {{#each product.tags}}
                <span class="tag">{{uppercase this}}</span>
            {{/each}}
        </div>
    {{/unless}}
    
    {{>product-options product=product}}
</div>`;
        
        await this.setValue('handlebars', exampleCode);
        this.toggleDropZone();
        this.enableConvertButton();
        this.updateStatus('Example code loaded');
        this.showNotification('Example Handlebars code loaded', 'info');
    }
    

    




    // 切换拖拽区域显示
    toggleDropZone() {
        const dropZone = document.getElementById('dropZone');
        const hasContent = this.getValue('handlebars').trim().length > 0;
        
        if (hasContent) {
            dropZone.classList.add('hidden');
        } else {
            dropZone.classList.remove('hidden');
        }
    }
    
    // 启用转换按钮
    enableConvertButton() {
        const convertBtn = document.getElementById('convertBtn');
        const hasContent = this.getValue('handlebars').trim().length > 0;
        convertBtn.disabled = !hasContent;
    }
    
    // 启用相关按钮
    enableButtons() {
        document.getElementById('downloadBtn').disabled = false;
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('formatBtn').disabled = false;
    }
    
    // 禁用相关按钮
    disableButtons() {
        document.getElementById('downloadBtn').disabled = true;
        document.getElementById('copyBtn').disabled = true;
        document.getElementById('formatBtn').disabled = true;
    }
    
    // 显示加载状态
    showLoading(show) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        
        if (show) {
            loadingIndicator.style.display = 'flex';
        } else {
            loadingIndicator.style.display = 'none';
        }
    }
    
    // 更新状态文本
    updateStatus(message) {
        document.getElementById('statusText').textContent = message;
    }

    // 更新引擎状态信息
    updateEngineStatus() {
        try {
            if (this.wasmConverter) {
                const moduleInfo = this.wasmConverter.getModuleInfo();
                if (moduleInfo && moduleInfo.available) {
                    console.log(`🚀 WASM Engine Status:`);
                    console.log(`   Version: ${moduleInfo.version || 'Unknown'}`);
                    console.log(`   Functions: ${moduleInfo.functions ? moduleInfo.functions.length : 0}`);
                    console.log(`   Rules: ${moduleInfo.rulesBuiltIn ? '✅ Built-in' : '❌ Not available'}`);

                    // 更新页面上的引擎状态显示
                    const engineStatusElement = document.getElementById('engineStatus');
                    if (engineStatusElement) {
                        engineStatusElement.innerHTML = `
                            <span class="engine-status-item">🚀 WASM v${moduleInfo.version || 'Unknown'}</span>
                            <span class="engine-status-item">📦 ${moduleInfo.functions ? moduleInfo.functions.length : 0} functions</span>
                            <span class="engine-status-item">${moduleInfo.rulesBuiltIn ? '✅ Rules built-in' : '❌ No rules'}</span>
                        `;
                        engineStatusElement.style.display = 'block';
                    }
                } else {
                    console.log('❌ WASM Engine: Not available');
                }
            } else {
                console.log('❌ WASM Engine: Not initialized');
            }
        } catch (error) {
            console.error('Failed to update engine status:', error);
        }
    }
    
    // 更新统计信息
    updateStats() {
        const statsInfo = document.getElementById('statsInfo');
        const statsText = document.getElementById('statsText');
        
        if (this.currentStats) {
            const stats = this.currentStats;
            statsText.textContent = `Converted: ${stats.rulesApplied || 0} rules | Time: ${stats.conversionTime || 0}ms`;
            statsInfo.style.display = 'flex';
        } else {
            statsInfo.style.display = 'none';
        }
    }
    
    // 显示错误信息
    showError(message) {
        this.updateStatus(message);
        document.getElementById('statusText').className = 'error';

        // 3秒后重置状态
        setTimeout(() => {
            document.getElementById('statusText').className = '';
            this.updateStatus('Ready to convert');
        }, 3000);
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            min-width: 300px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideInRight 0.3s ease;
        `;

        // 添加关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 获取通知颜色
    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }

    // 添加通知动画样式
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .notification-content {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;
            }

            .notification-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
                transition: opacity 0.2s;
            }

            .notification-close:hover {
                opacity: 1;
                background: rgba(255,255,255,0.1);
            }
        `;
        document.head.appendChild(style);
    }
}

// 初始化应用
function initializeApp() {
    // 确保DOM和必要的元素都已加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
        return;
    }
    
    // 检查关键元素是否存在
    const requiredElements = [
        'handlebarsEditor', 'slineEditor', 
        'handlebarsTextarea', 'slineTextarea',
        'convertBtn', 'clearBtn'
    ];
    
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        console.warn('Missing elements:', missingElements);
        // 延迟重试
        setTimeout(initializeApp, 100);
        return;
    }
    
    // 所有元素都存在，初始化应用
    window.app = new HBS2SlineApp();
}

// 开始初始化
initializeApp();
