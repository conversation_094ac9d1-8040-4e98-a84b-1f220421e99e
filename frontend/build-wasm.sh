#!/bin/bash

# WASM 自动化构建脚本
# 用于在 Sline 语法规则更新后重新构建 WASM 模块

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    # 检查关键目录和文件
    local required_paths=(
        "wasm"
        "wasm/src"
        "wasm/package.json"
        "wasm-converter.js"
        "wasm/build"
    )
    
    for path in "${required_paths[@]}"; do
        if [[ ! -e "$path" ]]; then
            log_error "缺少必要的文件或目录: $path"
            exit 1
        fi
    done
    
    log_success "项目结构检查通过"
}

# 备份当前 WASM 文件
backup_current_wasm() {
    log_info "备份当前 WASM 文件..."
    
    local backup_dir="wasm/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [[ -f "wasm/build/converter.wasm" ]]; then
        cp wasm/build/* "$backup_dir/" 2>/dev/null || true
        log_success "WASM 文件已备份到: $backup_dir"
    else
        log_warning "未找到现有的 WASM 文件，跳过备份"
    fi
}

# 清理构建目录
clean_build_directory() {
    log_info "清理构建目录..."
    
    # 清理 WASM 构建目录
    if [[ -d "wasm/build" ]]; then
        rm -rf wasm/build/*
        log_success "WASM 构建目录已清理"
    fi
        rm -rf wasm/build/*
        log_success "前端 WASM 目录已清理"
    fi
}

# 构建 WASM 模块
build_wasm_module() {
    log_info "构建 WASM 模块..."
    
    # 进入 WASM 项目目录
    cd wasm
    
    # 安装依赖（如果需要）
    if [[ ! -d "node_modules" ]]; then
        log_info "安装 npm 依赖..."
        npm install
    fi
    
    # 生成规则文件
    log_info "生成规则文件..."
    npm run generate:rules

    # 构建 Release 版本
    log_info "编译 WASM 模块..."
    npm run build:release
    
    # 检查构建结果
    if [[ ! -f "build/converter.wasm" ]]; then
        log_error "WASM 构建失败：未生成 converter.wasm 文件"
        exit 1
    fi
    
    # 显示构建信息
    local wasm_size=$(du -h build/converter.wasm | cut -f1)
    log_success "WASM 模块构建成功 (大小: $wasm_size)"
    
    # 返回前端目录
    cd ..
}

# 验证 WASM 构建结果
verify_wasm_build() {
    log_info "验证 WASM 构建结果..."

    # 验证构建文件
    if [[ -f "wasm/build/converter.wasm" ]]; then
        local wasm_size=$(du -h wasm/build/converter.wasm | cut -f1)
        log_success "WASM 构建验证成功 (大小: $wasm_size)"
    else
        log_error "WASM 构建验证失败：未找到 converter.wasm"
        exit 1
    fi
}

# 更新版本信息
update_version_info() {
    log_info "更新版本信息..."
    
    local build_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local build_log="wasm/BUILD_INFO.md"
    
    cat > "$build_log" << EOF
# WASM 构建信息

## 最新构建
- **构建时间**: $build_time
- **构建脚本**: build-wasm.sh
- **WASM 文件**: converter.wasm
- **构建类型**: Release

## 文件信息
$(ls -la wasm/build/ 2>/dev/null || echo "构建文件未找到")

## 转换引擎
- **WASM 转换器**: wasm-converter.js
- **规则系统**: frontend/rules/

## 构建命令
\`\`\`bash
./build-wasm.sh
\`\`\`
EOF
    
    log_success "版本信息已更新: $build_log"
}

# 运行基本测试
run_basic_tests() {
    log_info "运行基本测试..."
    
    # 检查 WASM 文件格式
    if command -v file &> /dev/null; then
        local file_info=$(file wasm/build/converter.wasm)
        if [[ $file_info == *"WebAssembly"* ]]; then
            log_success "WASM 文件格式验证通过"
        else
            log_warning "WASM 文件格式可能有问题: $file_info"
        fi
    fi
    
    # 启动测试服务器（可选）
    if [[ "$1" == "--test" ]]; then
        log_info "启动测试服务器..."
        python3 -m http.server 8082 &
        local server_pid=$!
        
        sleep 2
        log_success "测试服务器已启动: http://localhost:8082"
        log_info "访问 http://localhost:8082/verify-wasm.html 进行测试"
        log_info "按 Enter 键停止服务器..."
        read
        
        kill $server_pid 2>/dev/null || true
        log_success "测试服务器已停止"
    fi
}

# 显示使用说明
show_usage() {
    echo "WASM 构建脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --test     构建完成后启动测试服务器"
    echo "  --help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 仅构建 WASM"
    echo "  $0 --test       # 构建并启动测试服务器"
    echo ""
}

# 主函数
main() {
    echo "🚀 WASM 自动化构建脚本"
    echo "=========================="
    
    # 处理命令行参数
    case "${1:-}" in
        --help)
            show_usage
            exit 0
            ;;
        --test)
            local run_test=true
            ;;
        "")
            local run_test=false
            ;;
        *)
            log_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
    
    # 执行构建流程
    check_dependencies
    check_project_structure
    backup_current_wasm
    clean_build_directory
    build_wasm_module
    verify_wasm_build
    update_version_info
    
    if [[ "$run_test" == true ]]; then
        run_basic_tests --test
    else
        run_basic_tests
    fi
    
    echo ""
    log_success "🎉 WASM 构建完成！"
    echo ""
    echo "下一步:"
    echo "1. 访问 http://localhost:8080/verify-wasm.html 验证功能"
    echo "2. 测试新的语法规则转换"
    echo "3. 更新 CHANGELOG.md 记录变更"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
