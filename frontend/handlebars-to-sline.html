<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HBS2Sline - Handlebars to Sline 语法转换工具 | Sline.dev</title>
    <meta name="description" content="专业的 Handlebars 模板语法转换工具，支持将 Handlebars 语法自动转换为 SHOPLINE Sline 语法。提供条件标签、循环标签、过滤器转换、运算符转换、上下文标签、输出标签、部分模板等 7 大分类 33+ 精准转换规则，实时在线转换，支持批量文件处理，内置转换质量检查功能，为前端开发者提供高效的模板迁移解决方案。">
    <meta name="keywords" content="Handlebars, Sline, 模板转换, SHOPLINE, 语法转换, 前端工具, 模板引擎">
    <meta name="author" content="Sline.dev">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="HBS2Sline - Handlebars to Sline 语法转换工具">
    <meta property="og:description" content="专业的 Handlebars 模板语法转换工具，支持 33+ 转换规则">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://hbs2sline.sline.dev">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="HBS2Sline - Handlebars to Sline 语法转换工具">
    <meta name="twitter:description" content="专业的 Handlebars 模板语法转换工具，支持 33+ 转换规则">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://hbs2sline.sline.dev">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">

    <!-- JSON-LD 结构化数据 -->
    <script type="application/ld+json" id="software-application-schema">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "HBS2Sline - Handlebars to Sline 语法转换工具",
      "description": "专业的 Handlebars 模板语法转换工具，支持将 Handlebars 语法自动转换为 Shopline Sline 语法，包含 33+ 转换规则",
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "author": {
        "@type": "Organization",
        "name": "Shopline"
      },
      "featureList": [
        "支持 33+ 转换规则",
        "实时在线转换",
        "智能过滤器转换",
        "运算符自动转换",
        "批量文件处理",
        "转换质量检查"
      ]
    }
    </script>
    
    <script type="application/ld+json" id="howto-schema">
    {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "如何使用 HBS2Sline 转换 Handlebars to Sline 语法",
      "description": "详细的 Handlebars to Sline 语法转换规则和使用指南",
      "totalTime": "PT5M",
      "tool": [
        {
          "@type": "HowToTool",
          "name": "HBS2Sline 转换工具"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "position": 1,
          "name": "条件标签转换",
          "text": "转换 条件标签转换：包含 7 个转换规则",
          "url": "#category-conditional",
          "tool": [
            {"@type": "HowToTool", "name": "if 标签转换 - 带复杂条件"},
            {"@type": "HowToTool", "name": "if 标签转换 - 基础"},
            {"@type": "HowToTool", "name": "unless 标签转换 - 复杂条件"},
            {"@type": "HowToTool", "name": "unless 标签转换 - 基础"},
            {"@type": "HowToTool", "name": "else 标签转换"},
            {"@type": "HowToTool", "name": "unless 结束标签"},
            {"@type": "HowToTool", "name": "elseif 标签转换"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 2,
          "name": "循环标签转换",
          "text": "转换 循环标签转换：包含 9 个转换规则",
          "url": "#category-iteration",
          "tool": [
            {"@type": "HowToTool", "name": "each 循环转换 - 带别名和索引"},
            {"@type": "HowToTool", "name": "each 循环转换 - 带别名"},
            {"@type": "HowToTool", "name": "each 循环转换 - 基础"},
            {"@type": "HowToTool", "name": "each 结束标签"},
            {"@type": "HowToTool", "name": "循环索引变量转换"},
            {"@type": "HowToTool", "name": "循环第一项变量转换"},
            {"@type": "HowToTool", "name": "循环最后项变量转换"},
            {"@type": "HowToTool", "name": "循环键值变量转换"},
            {"@type": "HowToTool", "name": "当前项变量转换"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 3,
          "name": "过滤器转换",
          "text": "转换 过滤器转换：包含 3 个转换规则",
          "url": "#category-filter",
          "tool": [
            {"@type": "HowToTool", "name": "单参数过滤器转换"},
            {"@type": "HowToTool", "name": "多参数过滤器转换"},
            {"@type": "HowToTool", "name": "链式过滤器支持"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 4,
          "name": "运算符转换",
          "text": "转换 运算符转换：包含 7 个转换规则",
          "url": "#category-operator",
          "tool": [
            {"@type": "HowToTool", "name": "逻辑与运算符转换"},
            {"@type": "HowToTool", "name": "逻辑或运算符转换"},
            {"@type": "HowToTool", "name": "严格相等运算符转换"},
            {"@type": "HowToTool", "name": "严格不等运算符转换"},
            {"@type": "HowToTool", "name": "Handlebars gt 辅助函数转换"},
            {"@type": "HowToTool", "name": "Handlebars lt 辅助函数转换"},
            {"@type": "HowToTool", "name": "Handlebars eq 辅助函数转换"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 5,
          "name": "上下文标签",
          "text": "转换 上下文标签：包含 2 个转换规则",
          "url": "#category-context",
          "tool": [
            {"@type": "HowToTool", "name": "with 上下文转换"},
            {"@type": "HowToTool", "name": "with 结束标签"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 6,
          "name": "输出标签",
          "text": "转换 输出标签：包含 3 个转换规则",
          "url": "#category-output",
          "tool": [
            {"@type": "HowToTool", "name": "原始输出转换"},
            {"@type": "HowToTool", "name": "注释转换"},
            {"@type": "HowToTool", "name": "简单注释转换"}
          ]
        },
        {
          "@type": "HowToStep",
          "position": 7,
          "name": "部分模板",
          "text": "转换 部分模板：包含 2 个转换规则",
          "url": "#category-partial",
          "tool": [
            {"@type": "HowToTool", "name": "部分模板转换 - 带上下文"},
            {"@type": "HowToTool", "name": "部分模板转换 - 基础"}
          ]
        }
      ]
    }
    </script>
    
    <script type="application/ld+json" id="tech-article-schema">
    {
      "@context": "https://schema.org",
      "@type": "TechArticle",
      "headline": "Handlebars to Sline 语法转换规则大全",
      "description": "完整的 Handlebars 到 Shopline Sline 语法转换规则文档，包含条件标签、循环标签、过滤器转换等 7 大分类 33+ 规则",
      "author": {
        "@type": "Organization",
        "name": "Shopline"
      },
      "datePublished": "2025-01-27T00:00:00.000Z",
      "dateModified": "2025-01-27T00:00:00.000Z",
      "articleSection": [
        "条件标签转换",
        "循环标签转换",
        "过滤器转换",
        "运算符转换",
        "上下文标签",
        "输出标签",
        "部分模板"
      ],
      "about": {
        "@type": "Thing",
        "name": "模板引擎语法转换"
      }
    }
    </script>
</head>
<body>
    <div class="container">
        <!-- Header with sticky navigation -->
        <header class="header">
            <div class="header-content">
                <div class="hero-section">
                    <div class="hero-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h1 class="hero-title">HBS2Sline</h1>
                    <p class="hero-subtitle">Professional Handlebars to Sline template conversion tool</p>

                    <!-- 统计信息 -->
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">33+</span>
                            <span class="stat-label">转换规则</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">7</span>
                            <span class="stat-label">主要分类</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">语法覆盖</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">实时</span>
                            <span class="stat-label">在线转换</span>
                        </div>
                    </div>

                    <div class="cta-buttons">
                        <button id="convertBtn" class="btn btn-primary">
                            <i class="fas fa-magic"></i>
                            Convert
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i>
                            Clear
                        </button>
                        <button id="downloadBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i>
                            Download
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Status bar -->
        <div class="status-bar">
            <div class="status-info">
                <div class="status-item">
                    <span id="statusText">Ready to convert</span>
                    <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                        <div class="spinner-small"></div>
                    </div>
                </div>
                <div class="status-item" id="statsInfo" style="display: none;">
                    <span id="statsText"></span>
                </div>
                <div class="status-item" id="engineStatus" style="display: none;">
                    <!-- Engine status will be populated by JavaScript -->
                </div>
            </div>
            <div class="status-links">
                <a href="rules" class="link-button">
                    <i class="fas fa-book"></i>
                    View Rules
                </a>
                <a href="https://github.com" class="link-button" target="_blank">
                    <i class="fab fa-github"></i>
                    GitHub
                </a>
            </div>
        </div>
        <!-- Main content area -->
        <main class="main-content">
            <div class="conversion-grid">
                <!-- Input panel -->
                <div class="editor-card">
                    <div class="card-header">
                        <h3>
                            <span class="handlebars-badge">Handlebars</span>
                            Handlebars Template
                        </h3>
                        <div class="card-actions">
                            <button id="loadExampleBtn" class="btn btn-small">
                                <i class="fas fa-file-code"></i>
                                Load Example
                            </button>
                            <label for="fileInput" class="btn btn-small file-upload-btn">
                                <i class="fas fa-upload"></i>
                                Upload File
                            </label>
                            <input type="file" id="fileInput" accept=".hbs,.handlebars,.html" style="display: none;">
                        </div>
                    </div>
                    
                    <!-- File drop zone -->
                    <div id="dropZone" class="drop-zone">
                        <div class="drop-zone-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drop .hbs, .handlebars or .html files here</p>
                            <p class="drop-zone-hint">or click upload button to select files</p>
                        </div>
                    </div>
                    
                    <!-- Code editor -->
                    <div class="editor-container">
                        <div id="handlebarsEditor" class="shiki-editor" data-lang="handlebars"></div>
                        <textarea id="handlebarsTextarea" class="editor-textarea" placeholder="Enter or paste your Handlebars template code here..."></textarea>
                    </div>
                </div>

                <!-- Output panel -->
                <div class="editor-card">
                    <div class="card-header">
                        <h3>
                            <span class="sline-badge">Sline</span>
                            Sline Template
                        </h3>
                        <div class="card-actions">

                            <button id="copyBtn" class="btn btn-small" disabled>
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                            <button id="formatBtn" class="btn btn-small" disabled>
                                <i class="fas fa-indent"></i>
                                Format
                            </button>
                        </div>
                    </div>
                    
                    <!-- Code output -->
                    <div class="editor-container">
                        <div id="slineEditor" class="shiki-editor" data-lang="go"></div>
                        <textarea id="slineTextarea" class="editor-textarea" placeholder="Converted Sline code will appear here..." readonly></textarea>
                    </div>
                </div>
            </div>
        </main>

        <!-- Features Section -->
        <section id="features" class="features-section">
            <h2 style="text-align: center; margin-bottom: 48px; font-size: 32px; font-weight: 600; color: #111827; line-height: 1.25;">
                <i class="fas fa-star" style="color: #111827; margin-right: 12px;"></i>
                核心特性
            </h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code-branch"></i>
                    </div>
                    <div class="feature-title">条件标签转换</div>
                    <div class="feature-desc">支持 if/else、unless 等条件语句的智能转换，包含 7 个精准转换规则</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="feature-title">循环标签转换</div>
                    <div class="feature-desc">完整支持 each 循环、with 上下文等循环语句，包含 9 个转换规则</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="feature-title">过滤器转换</div>
                    <div class="feature-desc">智能转换 Handlebars 过滤器为 Sline 过滤器，支持复杂的过滤器链</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="feature-title">运算符转换</div>
                    <div class="feature-desc">支持数学运算符、比较运算符、逻辑运算符的自动转换</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="feature-title">部分模板转换</div>
                    <div class="feature-desc">支持 partial 模板包含语句的转换，保持模板结构的完整性</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="feature-title">质量验证</div>
                    <div class="feature-desc">内置语法验证、语义检查和质量评估，确保转换结果的正确性</div>
                </div>
            </div>
        </section>



    </div>
        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-content">
                <h2 class="cta-title">开始使用 HBS2Sline</h2>
                <p class="cta-subtitle">立即体验专业的 Handlebars to Sline 模板语法转换工具</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary cta-btn" onclick="document.getElementById('handlebarsTextarea').focus()">
                        <i class="fas fa-rocket"></i>
                        立即开始转换
                    </button>
                    <a href="rules" class="btn btn-primary cta-btn">
                        <i class="fas fa-book"></i>
                        查看转换规则
                    </a>
                    <a href="liquid-to-sline.html" class="btn btn-primary cta-btn">
                        <i class="fas fa-book"></i>
                        Liquid to Sline
                    </a>
            </div>
        </section>
    <!-- Shiki 3.7 -->
    <script type="module">
        import { createHighlighter } from 'https://esm.sh/shiki@3.7.0'
        window.createHighlighter = createHighlighter;
    </script>
    <!-- WASM 转换器 -->
    <script src="wasm-converter.js?v=2.0.3"></script>
    <script src="app.js?v=2.0.3"></script>
</body>
</html>
