<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid2Sline - Liquid to Sline 语法转换工具 | Sline.dev</title>
    <meta name="description" content="专业的 Liquid 模板语法转换工具，支持将 Liquid 语法自动转换为 SHOPLINE Sline 语法。提供条件标签、循环标签、过滤器转换、变量赋值、模板包含等 7 大分类 68+ 精准转换规则，实时在线转换，支持批量文件处理，内置转换质量检查功能，为前端开发者提供高效的模板迁移解决方案。">
    <meta name="keywords" content="Liquid, Sline, 模板转换, SHOPLINE, 语法转换, 前端工具, 模板引擎, Shopify">
    <meta name="author" content="Sline.dev">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Liquid2Sline - Liquid to Sline 语法转换工具">
    <meta property="og:description" content="专业的 Liquid 模板语法转换工具，支持 68+ 转换规则">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://liquid2sline.sline.dev">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Liquid2Sline - Liquid to Sline 语法转换工具">
    <meta name="twitter:description" content="专业的 Liquid 模板语法转换工具，支持 68+ 转换规则">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://liquid2sline.sline.dev">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Liquid 特定样式 */
        .liquid-badge {
            background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sline-badge {
            background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin: 48px 0;
        }

        .feature-card {
            background: #FFFFFF;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: all 200ms ease-in-out;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
            color: #111827;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #111827;
            line-height: 1.4;
        }

        .feature-desc {
            font-size: 16px;
            color: #6B7280;
            line-height: 1.5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin: 32px 0;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #6B7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }
        
        .conversion-examples {
            margin: 32px 0;
        }
        
        .example-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .example-header {
            background: #f8fafc;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #1a202c;
        }
        
        .example-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
        }
        
        .example-side {
            padding: 20px;
        }
        
        .example-side:first-child {
            border-right: 1px solid #e2e8f0;
        }
        
        .example-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .example-code {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
        }

        /* Section spacing */
        .features-section {
            padding: 80px 0;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
            margin: 80px -24px -24px -24px;
            padding: 80px 24px;
            text-align: center;
            color: white;
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
            line-height: 1.25;
        }

        .cta-subtitle {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .cta-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .cta-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }


    </style>
    
    <!-- JSON-LD 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Liquid2Sline - Liquid to Sline 语法转换工具",
      "description": "专业的 Liquid 模板语法转换工具，支持将 Liquid 语法自动转换为 Shopline Sline 语法，包含 68+ 转换规则",
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "author": {
        "@type": "Organization",
        "name": "Shopline"
      },
      "featureList": [
        "支持 68+ 转换规则",
        "实时在线转换",
        "智能过滤器转换",
        "变量赋值转换",
        "模板包含转换",
        "转换质量检查",
        "186个对象映射",
        "147个过滤器映射"
      ]
    }
    </script>
</head>
<body>
    <div class="container">
        <!-- Header with sticky navigation -->
        <header class="header">
            <div class="header-content">
                <div class="hero-section">
                    <div class="hero-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h1 class="hero-title">Liquid2Sline</h1>
                    <p class="hero-subtitle">Professional Liquid to Sline template conversion tool</p>
                    
                    <!-- 统计信息 -->
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">68+</span>
                            <span class="stat-label">转换规则</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">186</span>
                            <span class="stat-label">对象映射</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">147</span>
                            <span class="stat-label">过滤器映射</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">7</span>
                            <span class="stat-label">主要分类</span>
                        </div>
                    </div>
                    
                    <div class="cta-buttons">
                        <button id="convertBtn" class="btn btn-primary">
                            <i class="fas fa-magic"></i>
                            Convert
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i>
                            Clear
                        </button>
                        <button id="downloadBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i>
                            Download
                        </button>
                        <button id="validateBtn" class="btn btn-secondary" disabled>
                            <i class="fas fa-check-circle"></i>
                            Validate
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Status bar -->
        <div class="status-bar">
            <div class="status-info">
                <div class="status-item">
                    <span id="statusText">Ready to convert Liquid templates</span>
                    <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                        <div class="spinner-small"></div>
                    </div>
                </div>
                <div class="status-item" id="statsInfo" style="display: none;">
                    <span id="statsText"></span>
                </div>
                <div class="status-item" id="qualityInfo" style="display: none;">
                    <span id="qualityText"></span>
                </div>
            </div>
            <div class="status-links">
                <a href="#examples" class="link-button">
                    <i class="fas fa-code"></i>
                    Examples
                </a>
                <a href="#features" class="link-button">
                    <i class="fas fa-star"></i>
                    Features
                </a>
                <a href="https://github.com" class="link-button" target="_blank">
                    <i class="fab fa-github"></i>
                    GitHub
                </a>
            </div>
        </div>
        
        <!-- Main content area -->
        <main class="main-content">
            <div class="conversion-grid">
                <!-- Input panel -->
                <div class="editor-card">
                    <div class="card-header">
                        <h3>
                            <span class="liquid-badge">Liquid</span>
                            Liquid Template
                        </h3>
                        <div class="card-actions">
                            <button id="loadExampleBtn" class="btn btn-small">
                                <i class="fas fa-file-code"></i>
                                Load Example
                            </button>
                            <label for="fileInput" class="btn btn-small file-upload-btn">
                                <i class="fas fa-upload"></i>
                                Upload File
                            </label>
                            <input type="file" id="fileInput" accept=".liquid,.html" style="display: none;">
                        </div>
                    </div>
                    
                    <!-- File drop zone -->
                    <div id="dropZone" class="drop-zone">
                        <div class="drop-zone-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drop .liquid or .html files here</p>
                            <p class="drop-zone-hint">or click upload button to select files</p>
                        </div>
                    </div>
                    
                    <!-- Code editor -->
                    <div class="editor-container">
                        <div id="liquidEditor" class="shiki-editor" data-lang="liquid"></div>
                        <textarea id="liquidTextarea" class="editor-textarea" placeholder="Enter or paste your Liquid template code here..."></textarea>
                    </div>
                </div>

                <!-- Output panel -->
                <div class="editor-card">
                    <div class="card-header">
                        <h3>
                            <span class="sline-badge">Sline</span>
                            Sline Template
                        </h3>
                        <div class="card-actions">

                            <button id="copyBtn" class="btn btn-small" disabled>
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                            <button id="formatBtn" class="btn btn-small" disabled>
                                <i class="fas fa-indent"></i>
                                Format
                            </button>
                        </div>
                    </div>
                    
                    <!-- Code output -->
                    <div class="editor-container">
                        <div id="slineEditor" class="shiki-editor" data-lang="go"></div>
                        <textarea id="slineTextarea" class="editor-textarea" placeholder="Converted Sline code will appear here..." readonly></textarea>
                    </div>
                </div>
            </div>
        </main>

        <!-- Features Section -->
        <section id="features" class="features-section">
            <h2 style="text-align: center; margin-bottom: 48px; font-size: 32px; font-weight: 600; color: #111827; line-height: 1.25;">
                <i class="fas fa-star" style="color: #111827; margin-right: 12px;"></i>
                核心特性
            </h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code-branch"></i>
                    </div>
                    <div class="feature-title">条件标签转换</div>
                    <div class="feature-desc">支持 if/endif、unless/endunless、case/when/endcase 等条件语句的智能转换</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="feature-title">循环标签转换</div>
                    <div class="feature-desc">完整支持 for/endfor、tablerow/endtablerow、break/continue 等循环语句</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-equals"></i>
                    </div>
                    <div class="feature-title">变量赋值转换</div>
                    <div class="feature-desc">智能转换 assign、capture/endcapture、increment/decrement 等赋值语句</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="feature-title">过滤器链转换</div>
                    <div class="feature-desc">支持 147+ 个过滤器的转换，包括字符串、数字、数组、日期等过滤器</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="feature-title">模板包含转换</div>
                    <div class="feature-desc">支持 include、render、section、layout 等模板包含语句的转换</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="feature-title">质量验证</div>
                    <div class="feature-desc">内置语法验证、语义检查和质量评估，确保转换结果的正确性</div>
                </div>
            </div>
        </section>



    </div>
        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-content">
                <h2 class="cta-title">开始使用 Liquid2Sline</h2>
                <p class="cta-subtitle">立即体验专业的 Liquid to Sline 模板语法转换工具</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary cta-btn" onclick="document.getElementById('liquidTextarea').focus()">
                        <i class="fas fa-rocket"></i>
                        立即开始转换
                    </button>
                    <a href="https://github.com" class="btn btn-primary cta-btn" target="_blank">
                        <i class="fab fa-github"></i>
                        查看源码
                    </a>
                </div>
            </div>
        </section>
    <!-- Shiki 3.7 -->
    <script type="module">
        import { createHighlighter } from 'https://esm.sh/shiki@3.7.0'
        window.createHighlighter = createHighlighter;
    </script>
    <script src="wasm-converter.js"></script>
    <script src="liquid2sline-app.js"></script>
</body>
</html>
