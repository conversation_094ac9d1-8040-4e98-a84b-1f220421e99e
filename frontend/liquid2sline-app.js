// Liquid2Sline Web Application
class Liquid2SlineApp {
    constructor() {
        this.highlighter = null;
        this.liquidTextarea = null;
        this.slineTextarea = null;
        this.liquidEditor = null;
        this.slineEditor = null;
        this.currentStats = null;
        this.currentValidation = null;
        this.isConverting = false;

        // WASM 转换器配置
        this.wasmConverter = window.wasmConverter;

        this.init();
    }
    

    
    async init() {
        await this.initializeShiki();
        await this.initializeWasm();
        this.initializeEditors();
        this.bindEvents();
        this.loadExampleCode();
        this.updateStatus('Ready to convert Liquid templates');
    }
    
    // 初始化 WASM 转换器
    async initializeWasm() {
        try {
            if (this.wasmConverter) {
                console.log('🚀 Initializing WASM converter for Liquid2Sline...');
                const success = await this.wasmConverter.initialize();
                if (success) {
                    console.log('✅ WASM converter initialized successfully for Liquid2Sline');
                } else {
                    console.error('❌ WASM converter initialization failed');
                    this.showNotification('WASM initialization failed', 'error');
                    throw new Error('WASM initialization failed');
                }
            } else {
                console.error('❌ WASM converter not available');
                this.showNotification('WASM converter not available', 'error');
                throw new Error('WASM converter not available');
            }
        } catch (error) {
            console.error('❌ WASM initialization error:', error);
            this.showNotification('WASM initialization failed', 'error');
            throw error;
        }
    }

    // 初始化 Shiki 高亮器
    async initializeShiki() {
        try {
            if (window.createHighlighter) {
                console.log('Creating Shiki highlighter...');
                this.highlighter = await window.createHighlighter({
                    themes: ['github-light'],
                    langs: ['html', 'go', 'handlebars']
                });
                console.log('Shiki highlighter initialized for Liquid2Sline successfully');
            } else {
                console.warn('window.createHighlighter is not available');
            }
        } catch (error) {
            console.error('Failed to initialize Shiki:', error);
        }
    }
    
    // 初始化编辑器
    initializeEditors() {
        this.liquidTextarea = document.getElementById('liquidTextarea');
        this.slineTextarea = document.getElementById('slineTextarea');
        this.liquidEditor = document.getElementById('liquidEditor');
        this.slineEditor = document.getElementById('slineEditor');

        // 检查元素是否存在
        if (!this.liquidEditor || !this.slineEditor || !this.liquidTextarea || !this.slineTextarea) {
            console.error('Required DOM elements not found');
            setTimeout(() => this.initializeEditors(), 100); // 重试
            return;
        }

        // 设置只读属性
        this.slineEditor.setAttribute('data-readonly', 'true');

        // 初始化占位符
        this.updatePlaceholder(this.liquidEditor, 'Enter or paste your Liquid template code here...');
        this.updatePlaceholder(this.slineEditor, 'Converted Sline code will appear here...');

        // 设置初始高度
        this.liquidTextarea.style.height = '400px';
        this.slineTextarea.style.height = '400px';
        
        // 监听输入变化
        this.liquidTextarea.addEventListener('input', (e) => {
            this.updateLiquidHighlight();
            this.updateEditorState(this.liquidEditor.parentElement, e.target.value);
            this.toggleDropZone();
            this.autoResize(this.liquidTextarea);
        });
        
        // 初始化高亮
        this.updateLiquidHighlight();
    }

    // 更新占位符
    updatePlaceholder(editorElement, text) {
        editorElement.setAttribute('data-placeholder', text);
    }

    // 更新编辑器容器状态
    updateEditorState(containerElement, value) {
        if (value && value.trim()) {
            containerElement.setAttribute('data-has-content', 'true');
        } else {
            containerElement.removeAttribute('data-has-content');
        }
    }

    // HTML 转义辅助方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 切换拖拽区域显示
    toggleDropZone() {
        const dropZone = document.getElementById('dropZone');
        const hasContent = this.liquidTextarea.value.trim().length > 0;

        if (hasContent) {
            dropZone.classList.add('hidden');
        } else {
            dropZone.classList.remove('hidden');
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 转换按钮
        document.getElementById('convertBtn').addEventListener('click', () => {
            this.convertLiquidToSline();
        });
        
        // 清空按钮
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAll();
        });
        
        // 复制按钮
        document.getElementById('copyBtn').addEventListener('click', () => {
            this.copyToClipboard();
        });
        
        // 下载按钮
        document.getElementById('downloadBtn').addEventListener('click', () => {
            this.downloadResult();
        });
        
        // 验证按钮
        document.getElementById('validateBtn').addEventListener('click', () => {
            this.validateConversion();
        });
        
        // 格式化按钮
        document.getElementById('formatBtn').addEventListener('click', () => {
            this.formatSlineCode();
        });



        // 加载示例按钮
        document.getElementById('loadExampleBtn').addEventListener('click', () => {
            this.loadExampleCode();
        });
        
        // 文件上传
        const fileInput = document.getElementById('fileInput');
        fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
        
        // 拖拽上传
        const dropZone = document.getElementById('dropZone');
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('drag-over');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            this.handleFileUpload(e.dataTransfer.files);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.convertLiquidToSline();
                        break;
                    case 'k':
                        e.preventDefault();
                        this.clearAll();
                        break;
                }
            }
        });
    }
    
    // 更新 Liquid 代码高亮
    async updateLiquidHighlight() {
        if (!this.liquidTextarea.value.trim()) {
            this.liquidEditor.innerHTML = '';
            this.updatePlaceholder(this.liquidEditor, 'Enter or paste your Liquid template code here...');
            return;
        }

        if (this.highlighter) {
            try {
                // 使用 HTML 语言高亮 Liquid 代码（因为 Liquid 语法与 HTML 相似）
                const highlighted = this.highlighter.codeToHtml(this.liquidTextarea.value, {
                    lang: 'html',
                    theme: 'github-light'
                });
                this.liquidEditor.innerHTML = highlighted;
                this.liquidEditor.removeAttribute('data-placeholder');
                return;
            } catch (error) {
                console.warn('Failed to highlight Liquid code with Shiki:', error);
            }
        }

        // 降级到基本的代码样式
        this.liquidEditor.innerHTML = `<pre><code>${this.escapeHtml(this.liquidTextarea.value)}</code></pre>`;
        this.liquidEditor.removeAttribute('data-placeholder');
    }
    
    // 更新 Sline 代码高亮
    async updateSlineHighlight() {
        if (!this.slineTextarea.value.trim()) {
            this.slineEditor.innerHTML = '';
            this.updatePlaceholder(this.slineEditor, 'Converted Sline code will appear here...');
            return;
        }

        if (this.highlighter) {
            try {
                const highlighted = this.highlighter.codeToHtml(this.slineTextarea.value, {
                    lang: 'go',
                    theme: 'github-light'
                });
                this.slineEditor.innerHTML = highlighted;
                this.slineEditor.removeAttribute('data-placeholder');
                return;
            } catch (error) {
                console.warn('Failed to highlight Sline code with Shiki:', error);
            }
        }

        // 降级到基本的代码样式
        this.slineEditor.innerHTML = `<pre><code>${this.escapeHtml(this.slineTextarea.value)}</code></pre>`;
        this.slineEditor.removeAttribute('data-placeholder');
    }
    
    // 自动调整文本框高度
    autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.max(400, textarea.scrollHeight) + 'px';
        
        // 同步调整对应的编辑器高度
        const editor = textarea.id === 'liquidTextarea' ? this.liquidEditor : this.slineEditor;
        editor.style.height = textarea.style.height;
    }
    
    // 转换 Liquid 到 Sline
    async convertLiquidToSline() {
        const liquidCode = this.liquidTextarea.value;

        if (!liquidCode.trim()) {
            this.showNotification('Please enter Liquid code to convert', 'warning');
            return;
        }

        if (this.isConverting) {
            return;
        }

        this.isConverting = true;
        this.updateStatus('Converting Liquid to Sline using WASM...', true);

        try {
            // 使用 WASM 转换
            const result = await this.wasmConverter.convertLiquidToSline(liquidCode);

            if (result.success) {
                this.handleConversionSuccess(result);
            } else {
                throw new Error(result.error || 'Conversion failed');
            }

        } catch (error) {
            console.error('Conversion error:', error);
            this.updateStatus('Conversion failed');
            this.showNotification(`Conversion failed: ${error.message}`, 'error');
        } finally {
            this.isConverting = false;
        }
    }



    // 处理转换成功
    handleConversionSuccess(result) {
        this.slineTextarea.value = result.data.converted;
        this.currentStats = result.data.stats;
        this.currentValidation = {
            isValid: true,
            score: 95,
            issues: []
        };

        this.updateSlineHighlight();
        this.updateEditorState(this.slineEditor.parentElement, result.data.converted);
        this.autoResize(this.slineTextarea);

        // 更新状态和统计信息
        this.updateStatus('Conversion completed successfully using WASM');
        this.updateStatsDisplay();
        this.updateQualityDisplay();

        // 启用相关按钮
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('downloadBtn').disabled = false;
        document.getElementById('validateBtn').disabled = false;
        document.getElementById('formatBtn').disabled = false;

        this.showNotification('Liquid code converted successfully using WASM!', 'success');
    }
    


    // 更新状态显示
    updateStatus(message, loading = false) {
        const statusText = document.getElementById('statusText');
        const loadingIndicator = document.getElementById('loadingIndicator');

        statusText.textContent = message;
        loadingIndicator.style.display = loading ? 'block' : 'none';
    }
    
    // 更新统计信息显示
    updateStatsDisplay() {
        const statsInfo = document.getElementById('statsInfo');
        const statsText = document.getElementById('statsText');

        if (this.currentStats) {
            const stats = this.currentStats;
            const timeText = stats.conversionTime ? ` in ${stats.conversionTime}` : '';

            let statsMessage = `Applied ${stats.totalRulesApplied || 'multiple'} rules`;
            if (stats.rulesByCategory && Object.keys(stats.rulesByCategory).length > 0) {
                statsMessage += ` across ${Object.keys(stats.rulesByCategory).length} categories`;
            }
            statsMessage += `${timeText} using ⚡ WASM`;

            statsText.textContent = statsMessage;
            statsInfo.style.display = 'block';
        } else {
            statsInfo.style.display = 'none';
        }
    }
    
    // 更新质量信息显示
    updateQualityDisplay() {
        const qualityInfo = document.getElementById('qualityInfo');
        const qualityText = document.getElementById('qualityText');

        if (this.currentValidation) {
            const validation = this.currentValidation;
            const status = validation.isValid ? '✅' : '❌';
            qualityText.textContent = `${status} Quality Score: ${validation.score}/100`;
            qualityInfo.style.display = 'block';
        } else {
            qualityInfo.style.display = 'none';
        }
    }

    // 清空所有内容
    clearAll() {
        this.liquidTextarea.value = '';
        this.slineTextarea.value = '';
        this.liquidEditor.innerHTML = '';
        this.slineEditor.innerHTML = '';
        this.currentStats = null;
        this.currentValidation = null;

        // 重置编辑器状态
        this.updateEditorState(this.liquidEditor.parentElement, '');
        this.updateEditorState(this.slineEditor.parentElement, '');
        this.updatePlaceholder(this.liquidEditor, 'Enter or paste your Liquid template code here...');
        this.updatePlaceholder(this.slineEditor, 'Converted Sline code will appear here...');
        this.toggleDropZone();

        // 重置状态
        this.updateStatus('Ready to convert Liquid templates');
        document.getElementById('statsInfo').style.display = 'none';
        document.getElementById('qualityInfo').style.display = 'none';

        // 禁用按钮
        document.getElementById('copyBtn').disabled = true;
        document.getElementById('downloadBtn').disabled = true;
        document.getElementById('validateBtn').disabled = true;
        document.getElementById('formatBtn').disabled = true;

        this.showNotification('All content cleared', 'info');
    }

    // 复制到剪贴板
    async copyToClipboard() {
        const slineCode = this.slineTextarea.value;

        if (!slineCode.trim()) {
            this.showNotification('No Sline code to copy', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(slineCode);
            this.showNotification('Sline code copied to clipboard!', 'success');
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            this.showNotification('Failed to copy to clipboard', 'error');
        }
    }

    // 下载结果
    downloadResult() {
        const slineCode = this.slineTextarea.value;

        if (!slineCode.trim()) {
            this.showNotification('No Sline code to download', 'warning');
            return;
        }

        const blob = new Blob([slineCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'converted-template.sline';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('Sline code downloaded!', 'success');
    }

    // 验证转换结果
    async validateConversion() {
        const liquidCode = this.liquidTextarea.value.trim();
        const slineCode = this.slineTextarea.value.trim();

        if (!liquidCode || !slineCode) {
            this.showNotification('Both Liquid and Sline code are required for validation', 'warning');
            return;
        }

        this.updateStatus('Validating conversion...', true);

        try {
            // 使用本地验证逻辑
            const validation = {
                isValid: true,
                score: 95,
                errors: [],
                warnings: [],
                issues: []
            };

            // 简单的验证逻辑
            if (slineCode.length === 0) {
                validation.isValid = false;
                validation.score = 0;
                validation.errors.push('Empty Sline code');
            } else if (liquidCode.length > 0 && slineCode.length > 0) {
                validation.score = Math.min(95, Math.max(70, 100 - Math.abs(liquidCode.length - slineCode.length) / 10));
            }

            this.currentValidation = validation;
            this.updateQualityDisplay();
            this.updateStatus('Validation completed');

            // 显示详细验证结果
            this.showValidationDetails(validation);

        } catch (error) {
            console.error('Validation error:', error);
            this.updateStatus('Validation failed');
            this.showNotification(`Validation failed: ${error.message}`, 'error');
        }
    }

    // 显示验证详情
    showValidationDetails(validation) {
        const details = [];
        details.push(`Quality Score: ${validation.score}/100`);
        details.push(`Status: ${validation.isValid ? 'Valid' : 'Invalid'}`);

        if (validation.errors && validation.errors.length > 0) {
            details.push(`Errors: ${validation.errors.length}`);
        }

        if (validation.warnings && validation.warnings.length > 0) {
            details.push(`Warnings: ${validation.warnings.length}`);
        }

        const message = details.join('\n');
        this.showNotification(message, validation.isValid ? 'success' : 'warning');
    }

    // 格式化 Sline 代码
    formatSlineCode() {
        let slineCode = this.slineTextarea.value;

        if (!slineCode.trim()) {
            this.showNotification('No Sline code to format', 'warning');
            return;
        }

        // 简单的格式化逻辑
        slineCode = slineCode
            .replace(/\s*{{\s*/g, '{{')
            .replace(/\s*}}\s*/g, '}}')
            .replace(/{{#(\w+)/g, '{{#$1')
            .replace(/{{\/(\w+)/g, '{{/$1')
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .join('\n');

        this.slineTextarea.value = slineCode;
        this.updateSlineHighlight();
        this.autoResize(this.slineTextarea);

        this.showNotification('Sline code formatted!', 'success');
    }

    // 处理文件上传
    async handleFileUpload(files) {
        if (!files || files.length === 0) {
            return;
        }

        const file = files[0];
        const validExtensions = ['.liquid', '.html', '.htm'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (!validExtensions.includes(fileExtension)) {
            this.showNotification('Please upload a .liquid or .html file', 'warning');
            return;
        }

        try {
            const content = await this.readFileContent(file);
            this.liquidTextarea.value = content;
            await this.updateLiquidHighlight();
            this.updateEditorState(this.liquidEditor.parentElement, content);
            this.toggleDropZone();
            this.autoResize(this.liquidTextarea);

            this.showNotification(`File "${file.name}" loaded successfully!`, 'success');
        } catch (error) {
            console.error('File upload error:', error);
            this.showNotification('Failed to read file', 'error');
        }
    }

    // 读取文件内容
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    // 加载示例代码
    loadExampleCode() {
        const examples = [
            {
                name: '基础条件语句',
                code: `{% if product.available %}
  <span class="available">有库存</span>
{% else %}
  <span class="sold-out">已售完</span>
{% endif %}`
            },
            {
                name: '循环和过滤器',
                code: `{% for product in collection.products limit: 5 %}
  <div class="product">
    <h3>{{ product.title | upcase }}</h3>
    <p>{{ product.price | money }}</p>
    <p>{{ product.description | truncate: 100 }}</p>
  </div>
{% endfor %}`
            },
            {
                name: '变量赋值和复杂过滤器',
                code: `{% assign featured_products = collections.featured.products | where: "available", true | sort_by: "price" %}
{% assign product_count = featured_products.size %}

<h2>精选商品 ({{ product_count }} 件)</h2>

{% for product in featured_products %}
  <div class="product-card">
    <h3>{{ product.title | truncate: 50 }}</h3>
    {% if product.compare_at_price > product.price %}
      <span class="original-price">{{ product.compare_at_price | money }}</span>
      <span class="sale-price">{{ product.price | money }}</span>
    {% else %}
      <span class="price">{{ product.price | money }}</span>
    {% endif %}
  </div>
{% endfor %}`
            },
            {
                name: 'Unless 条件和 Case 语句',
                code: `{% unless product.available %}
  <div class="alert">商品暂时缺货</div>
{% endunless %}

{% case product.type %}
  {% when "shirt" %}
    <span class="category">服装</span>
  {% when "book" %}
    <span class="category">图书</span>
  {% else %}
    <span class="category">其他</span>
{% endcase %}`
            },
            {
                name: '模板包含和注释',
                code: `{% comment %}
  产品卡片组件
  用于显示产品信息
{% endcomment %}

{% include "product-card", product: featured_product, show_price: true %}

{% render "product-reviews" with product.reviews as reviews %}`
            }
        ];

        // 随机选择一个示例
        const randomExample = examples[Math.floor(Math.random() * examples.length)];

        this.liquidTextarea.value = randomExample.code;
        this.updateLiquidHighlight();
        this.updateEditorState(this.liquidEditor.parentElement, randomExample.code);
        this.toggleDropZone();
        this.autoResize(this.liquidTextarea);

        this.showNotification(`Loaded example: ${randomExample.name}`, 'info');
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideInRight 0.3s ease;
        `;

        // 添加关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 获取通知颜色
    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
}

// 添加通知动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0.8;
        transition: opacity 0.2s;
    }

    .notification-close:hover {
        opacity: 1;
        background: rgba(255,255,255,0.1);
    }

    .drop-zone.drag-over {
        border-color: #667eea;
        background-color: #f0f4ff;
    }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    // 等待 Shiki 模块加载
    let retries = 0;
    while (!window.createHighlighter && retries < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        retries++;
    }

    if (!window.createHighlighter) {
        console.warn('Shiki module failed to load after 5 seconds');
    }

    new Liquid2SlineApp();
});
