#!/bin/bash

# 快速 WASM 构建脚本
# 用于快速重新构建和部署 WASM 模块

set -e

echo "⚡ 快速 WASM 构建"
echo "================"

# 检查是否在正确的目录
if [[ ! -f "wasm-converter.js" ]]; then
    echo "❌ 错误：请在 frontend 目录中运行此脚本"
    exit 1
fi

# 1. 构建 WASM
echo "🔨 构建 WASM 模块..."
cd ../wasm
npm run build:release

# 2. 复制到前端
echo "📦 复制文件..."
cd ../frontend
cp ../wasm/build/* wasm/build/

# 3. 显示结果
echo "✅ 构建完成！"
echo ""
echo "文件信息:"
ls -la wasm/build/
echo ""
echo "🚀 可以开始测试了："
echo "   python3 -m http.server 8080"
echo "   访问: http://localhost:8080/verify-wasm.html"
