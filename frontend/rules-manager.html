<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转换规则管理器 - HBS2Sline</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .rules-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .rules-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .rules-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.9em;
        }
        
        .rules-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .rules-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .rules-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .rule-category {
            margin-bottom: 15px;
            padding: 10px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .rule-category-name {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .rule-category-info {
            font-size: 0.9em;
            color: #6b7280;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .rule-details {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .rule-details.show {
            display: block;
        }
        
        .toggle-details {
            background: none;
            border: none;
            color: #3b82f6;
            cursor: pointer;
            font-size: 0.8em;
            text-decoration: underline;
        }
        
        .rule-example {
            background: #f8fafc;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            margin: 5px 0;
        }

        .function-name {
            display: inline-block;
            background: #e0f2fe;
            color: #0277bd;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="rules-container">
        <div class="rules-header">
            <h1>🔧 转换规则管理器</h1>
            <p>管理和查看 Handlebars 和 Liquid 到 Sline 的转换规则</p>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载转换规则...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>
        <div id="success" class="success" style="display: none;"></div>

        <div id="rulesContent" style="display: none;">
            <div class="rules-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalRules">0</div>
                    <div class="stat-label">总规则数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="handlebarsCount">0</div>
                    <div class="stat-label">Handlebars 规则</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="liquidCount">0</div>
                    <div class="stat-label">Liquid 规则</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="wasmStatus">❌</div>
                    <div class="stat-label">WASM 状态</div>
                </div>
            </div>

            <div class="rules-grid">
                <div class="rules-section">
                    <h3>🔧 Handlebars 规则</h3>
                    <div id="handlebarsRules"></div>
                </div>
                
                <div class="rules-section">
                    <h3>💧 Liquid 规则</h3>
                    <div id="liquidRules"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="wasm-converter.js"></script>
    <script>
        class RulesManager {
            constructor() {
                this.converter = window.wasmConverter;
            }

            async initialize() {
                try {
                    // 初始化 WASM 转换器（规则已内置）
                    const success = await this.converter.initialize();

                    if (success) {
                        this.showSuccess('✅ WASM 转换器初始化成功！规则已内置到模块中。');
                        this.displayModuleInfo();
                    } else {
                        this.showError('❌ 转换器初始化失败');
                    }
                } catch (error) {
                    this.showError(`❌ 初始化错误: ${error.message}`);
                }
                
                this.hideLoading();
            }

            displayModuleInfo() {
                const moduleInfo = this.converter.getModuleInfo();

                if (!moduleInfo.available) {
                    this.showError('❌ WASM 模块不可用');
                    return;
                }

                // 更新统计信息
                document.getElementById('totalRules').textContent = '内置到 WASM';
                document.getElementById('handlebarsCount').textContent = '内置';
                document.getElementById('liquidCount').textContent = '内置';
                document.getElementById('wasmStatus').textContent = '✅';

                // 显示模块信息
                this.displayWasmModuleInfo(moduleInfo);

                document.getElementById('rulesContent').style.display = 'block';
            }

            displayWasmModuleInfo(moduleInfo) {
                const handlebarsContainer = document.getElementById('handlebarsRules');
                const liquidContainer = document.getElementById('liquidRules');

                handlebarsContainer.innerHTML = `
                    <div class="rule-category">
                        <h4>📦 WASM 模块信息</h4>
                        <div class="rule-item">
                            <strong>版本:</strong> ${moduleInfo.version}<br>
                            <strong>状态:</strong> ${moduleInfo.available ? '✅ 可用' : '❌ 不可用'}<br>
                            <strong>规则:</strong> ${moduleInfo.rulesBuiltIn ? '✅ 已内置' : '❌ 未内置'}<br>
                            <strong>函数数量:</strong> ${moduleInfo.functions.length}<br>
                            <strong>说明:</strong> ${moduleInfo.note}
                        </div>
                    </div>
                `;

                liquidContainer.innerHTML = `
                    <div class="rule-category">
                        <h4>🔧 可用函数</h4>
                        <div class="rule-item">
                            ${moduleInfo.functions.map(func => `<span class="function-name">${func}</span>`).join('<br>')}
                        </div>
                    </div>
                `;
            }

            displayRuleCategory(containerId, rules, type) {
                const container = document.getElementById(containerId);
                container.innerHTML = '';

                Object.entries(rules).forEach(([category, ruleData]) => {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'rule-category';
                    
                    const categoryName = document.createElement('div');
                    categoryName.className = 'rule-category-name';
                    categoryName.textContent = category;
                    
                    const categoryInfo = document.createElement('div');
                    categoryInfo.className = 'rule-category-info';
                    
                    // 计算规则数量
                    let ruleCount = 0;
                    if (ruleData && typeof ruleData === 'object') {
                        if (Array.isArray(ruleData)) {
                            ruleCount = ruleData.length;
                        } else if (ruleData.OBJECT_MAPPINGS) {
                            ruleCount = Object.keys(ruleData.OBJECT_MAPPINGS).length;
                        } else if (ruleData.FILTER_MAPPINGS) {
                            ruleCount = Object.keys(ruleData.FILTER_MAPPINGS).length;
                        } else {
                            ruleCount = Object.keys(ruleData).length;
                        }
                    }
                    
                    categoryInfo.innerHTML = `
                        ${ruleCount} 个规则
                        <button class="toggle-details" onclick="toggleDetails('${type}-${category}')">
                            查看详情
                        </button>
                    `;
                    
                    const detailsDiv = document.createElement('div');
                    detailsDiv.className = 'rule-details';
                    detailsDiv.id = `${type}-${category}`;
                    detailsDiv.innerHTML = `<pre>${JSON.stringify(ruleData, null, 2)}</pre>`;
                    
                    categoryDiv.appendChild(categoryName);
                    categoryDiv.appendChild(categoryInfo);
                    categoryDiv.appendChild(detailsDiv);
                    container.appendChild(categoryDiv);
                });
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            showError(message) {
                const errorDiv = document.getElementById('error');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }

            showSuccess(message) {
                const successDiv = document.getElementById('success');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
        }

        // 切换详情显示
        function toggleDetails(id) {
            const details = document.getElementById(id);
            details.classList.toggle('show');
        }

        // 初始化
        const rulesManager = new RulesManager();
        rulesManager.initialize();
    </script>
</body>
</html>
