<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversion Rules - HBS2Sline | Handlebars to Sline 转换规则大全</title>
    <meta name="description" content="完整的 Handlebars 到 Shopline Sline 语法转换规则文档，包含条件标签、循环标签、过滤器转换等 7 大分类 33+ 规则，详细的转换示例和说明。">
    <meta name="keywords" content="Handlebars, Sline, 转换规则, SHOPLINE, 语法转换, 前端工具, 模板引擎, 转换文档">
    <meta name="author" content="Sline.dev">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Conversion Rules - HBS2Sline | Handlebars to Sline 转换规则大全">
    <meta property="og:description" content="完整的 Handlebars 到 Shopline Sline 语法转换规则文档，包含 33+ 转换规则">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://hbs2sline.sline.dev/rules.html">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Conversion Rules - HBS2Sline">
    <meta name="twitter:description" content="完整的 Handlebars 到 Shopline Sline 语法转换规则文档">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://hbs2sline.sline.dev/rules.html">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">

    <!-- JSON-LD 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "TechArticle",
      "headline": "Handlebars to Sline 语法转换规则大全",
      "description": "完整的 Handlebars 到 Shopline Sline 语法转换规则文档，包含条件标签、循环标签、过滤器转换等 7 大分类 33+ 规则",
      "author": {
        "@type": "Organization",
        "name": "Shopline"
      },
      "datePublished": "2025-01-27T00:00:00.000Z",
      "dateModified": "2025-01-27T00:00:00.000Z",
      "articleSection": [
        "条件标签转换",
        "循环标签转换",
        "过滤器转换",
        "运算符转换",
        "上下文标签",
        "输出标签",
        "部分模板"
      ],
      "about": {
        "@type": "Thing",
        "name": "模板引擎语法转换"
      },
      "mainEntity": {
        "@type": "SoftwareApplication",
        "name": "HBS2Sline",
        "applicationCategory": "DeveloperApplication"
      }
    }
    </script>
</head>
<body>
    <div class="container">
        <!-- Header with navigation -->
        <header class="header">
            <div class="header-content">
                <div class="hero-section">
                    <div class="hero-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h1 class="hero-title">Conversion Rules</h1>
                    <p class="hero-subtitle">Complete Handlebars to Sline conversion rules documentation</p>
                    <div class="cta-buttons">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i>
                            Back to Converter
                        </a>
                        <a href="https://github.com" class="btn btn-secondary" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Status bar -->
        <div class="status-bar">
            <div class="status-info">
                <div class="status-item">
                    <span id="statusText">Loading rules...</span>
                </div>
            </div>
            <div class="status-links">
                <a href="/" class="link-button">
                    <i class="fas fa-exchange-alt"></i>
                    Converter Tool
                </a>
            </div>
        </div>

        <!-- Main content area -->
        <main class="main-content">
            <!-- Conversion rules section -->
            <section class="rules-section">
                <div class="rules-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-book"></i>
                            Conversion Rules
                        </h3>
                        <nav class="breadcrumb" role="navigation" aria-label="Breadcrumb navigation">
                            <ol itemscope itemtype="https://schema.org/BreadcrumbList">
                                <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                                    <a href="/" itemprop="item">
                                        <span itemprop="name">Home</span>
                                    </a>
                                    <meta itemprop="position" content="1" />
                                </li>
                                <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                                    <span itemprop="name">Conversion Rules</span>
                                    <meta itemprop="position" content="2" />
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <div class="rules-content">
                        <!-- Dynamic content container -->
                        <div id="rulesContent">
                            <div class="loading-content">
                                <div class="spinner"></div>
                                <p>Loading rules...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="rules.js"></script>
</body>
</html>