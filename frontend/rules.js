// HBS2Sline Rules Page Application
class RulesApp {
    constructor() {
        this.rulesData = null;
        
        // 规则数据基础路径配置
        this.rulesBasePath = this.getRulesBasePath();
        
        this.init();
    }
    
    // 获取规则数据基础路径
    getRulesBasePath() {
        // 使用相对路径访问静态规则文件
        return './rules';
    }
    
    async init() {
        this.updateStatus('Loading rules...');
        await this.loadRulesSection();
        this.updateStatus('Rules loaded successfully');
    }
    
    async loadRulesSection() {
        const content = document.getElementById('rulesContent');

        try {
            // 加载规则索引
            const indexResponse = await fetch(`${this.rulesBasePath}/index.json`);
            if (!indexResponse.ok) {
                throw new Error(`Failed to load index.json: ${indexResponse.status} ${indexResponse.statusText}`);
            }
            const indexData = await indexResponse.json();

            // 加载所有 handlebars 规则分类
            const categories = {};
            const stats = {
                totalRules: 0,
                mappings: {
                    filters: 0,
                    objects: 0
                }
            };

            for (const categoryName of indexData.handlebars.categories) {
                try {
                    const categoryResponse = await fetch(`${this.rulesBasePath}/handlebars/${categoryName}.json`);
                    if (!categoryResponse.ok) {
                        console.warn(`Failed to load category ${categoryName}: ${categoryResponse.status} ${categoryResponse.statusText}`);
                        categories[categoryName] = [];
                        continue;
                    }
                    const categoryData = await categoryResponse.json();

                    // 处理不同的数据结构
                    let rules = [];

                    // 处理直接包含规则数组的数据结构
                    if (Array.isArray(categoryData)) {
                        rules = categoryData;
                    } else if (categoryData.data) {
                        // 处理包含 data 属性的数据结构
                        const dataKeys = Object.keys(categoryData.data);
                        for (const key of dataKeys) {
                            if (Array.isArray(categoryData.data[key])) {
                                rules = rules.concat(categoryData.data[key]);
                            }
                        }
                    } else if (categoryData.rules && Array.isArray(categoryData.rules)) {
                        // 处理包含 rules 属性的数据结构
                        rules = categoryData.rules;
                    }

                    // 特殊处理 filters 和 objects 分类
                    if (categoryName === 'filters' && categoryData.data?.FILTER_MAPPINGS) {
                        // FILTER_MAPPINGS 是对象，转换为数组
                        const filterMappings = Object.keys(categoryData.data.FILTER_MAPPINGS);
                        rules = [{
                            name: '过滤器映射',
                            description: 'Handlebars 到 Sline 过滤器映射表',
                            type: 'filter-mapping',
                            filters: filterMappings
                        }];
                        stats.mappings.filters = filterMappings.length;
                    } else if (categoryName === 'objects' && categoryData.data?.OBJECT_MAPPINGS) {
                        // 处理对象映射（如果是对象则转换为数组）
                        let objectMappings = categoryData.data.OBJECT_MAPPINGS;
                        if (typeof objectMappings === 'object' && !Array.isArray(objectMappings)) {
                            objectMappings = Object.keys(objectMappings);
                        }
                        rules = [{
                            name: '对象映射',
                            description: 'Handlebars 到 Sline 对象映射表',
                            type: 'object-mapping',
                            objects: objectMappings
                        }];
                        stats.mappings.objects = Array.isArray(objectMappings) ? objectMappings.length : Object.keys(objectMappings).length;
                    }

                    categories[categoryName] = rules;
                    stats.totalRules += rules.length;

                    console.log(`Loaded category ${categoryName}: ${rules.length} rules`);
                } catch (error) {
                    console.warn(`Failed to load category ${categoryName}:`, error);
                    categories[categoryName] = [];
                }
            }

            // 构建结果数据
            const result = {
                success: true,
                data: {
                    stats: stats,
                    categories: categories
                }
            };

            // 保存规则数据供标签切换使用
            this.rulesData = result.data;
            content.innerHTML = this.generateRulesHTML(result);

            console.log('Rules loaded successfully:', this.rulesData);

        } catch (error) {
            console.error('Error loading rules:', error);
            content.innerHTML = `
                <div class="error-message">
                    <h4>加载规则失败</h4>
                    <p>错误信息: ${error.message}</p>
                    <p>请确保规则文件存在并且格式正确。</p>
                    <details>
                        <summary>技术详情</summary>
                        <pre>${error.stack}</pre>
                    </details>
                </div>
            `;
            this.updateStatus('Failed to load rules');
        }
    }
    
    // 生成规则说明 HTML
    generateRulesHTML(result) {
        const data = result.data || result;
        const stats = data.stats || {};
        const categories = data.categories || {};
        
        return `
            <div class="rules-overview">
                <h4>规则系统统计</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">${stats.totalRules || 0}</span>
                        <span class="stat-label">总规则数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${stats.mappings?.filters || 0}</span>
                        <span class="stat-label">过滤器数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${stats.mappings?.objects || 0}</span>
                        <span class="stat-label">对象数</span>
                    </div>
                </div>
            </div>

            <div class="rules-categories">
                <h4>转换规则分类</h4>
                <div class="category-tabs">
                    <button class="tab-button active" onclick="rulesApp.showCategory('conditional')">条件标签</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('iteration')">循环标签</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('context')">上下文标签</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('output')">输出标签</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('partial')">部分模板</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('filter')">过滤器转换</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('operator')">运算符转换</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('filters')">过滤器映射</button>
                    <button class="tab-button" onclick="rulesApp.showCategory('objects')">对象映射</button>
                </div>
                
                <div id="category-content">
                    ${this.renderCategoryContent('conditional', categories.conditional)}
                </div>
            </div>
        `;
    }
    
    // 显示特定分类
    showCategory(categoryName) {
        // 更新标签状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[onclick="rulesApp.showCategory('${categoryName}')"]`).classList.add('active');
        
        // 显示对应内容
        const content = document.getElementById('category-content');
        
        // 统一使用 renderCategoryContent 方法
        const categoryData = this.getCategoryData(categoryName);
        content.innerHTML = this.renderCategoryContent(categoryName, categoryData);
    }

    // 获取分类数据（统一数据结构）
    getCategoryData(categoryName) {
        // 所有分类都从后端数据获取，包括 filters 和 objects
        if (this.rulesData?.categories?.[categoryName]) {
            return this.rulesData.categories[categoryName];
        }
        
        return [];
    }

    // 渲染分类内容
    renderCategoryContent(categoryName, rules) {
        if (!rules || rules.length === 0) {
            return `<p>暂无${categoryName}规则</p>`;
        }
        
        const categoryNames = {
            conditional: '条件标签',
            iteration: '循环标签', 
            context: '上下文标签',
            output: '输出标签',
            partial: '部分模板',
            filter: '过滤器转换',
            operator: '运算符转换',
            filters: '过滤器映射',
            objects: '对象映射'
        };
        
        const displayName = categoryNames[categoryName] || categoryName;
        
        return `
            <h5>${displayName} (${rules.length} 个规则)</h5>
            <div class="rules-list">
                ${rules.map(rule => this.renderRuleItem(rule)).join('')}
            </div>
        `;
    }

    // 渲染单个规则项
    renderRuleItem(rule) {
        // 处理普通规则（包含 examples）
        if (rule.examples) {
            return `
                <div class="rule-item">
                    <h6>${rule.name}</h6>
                    <p class="rule-description">${rule.description}</p>
                    <div class="rule-examples">
                        <div class="example-row">
                            <span class="example-label">Handlebars:</span>
                            <code>${rule.examples.handlebars}</code>
                        </div>
                        <div class="example-row">
                            <span class="example-label">Sline:</span>
                            <code>${rule.examples.sline}</code>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 处理过滤器映射
        if (rule.type === 'filter-mapping' && rule.filters) {
            return `
                <div class="rule-item">
                    <h6>${rule.name}</h6>
                    <p class="rule-description">${rule.description}</p>
                    <div class="filter-list">
                        ${rule.filters.map(filter => 
                            `<span class="filter-tag">${filter}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        // 处理对象映射
        if (rule.type === 'object-mapping' && rule.objects) {
            return `
                <div class="rule-item">
                    <h6>${rule.name}</h6>
                    <p class="rule-description">${rule.description}</p>
                    <div class="object-list">
                        ${rule.objects.map(obj => 
                            `<span class="object-tag">${obj}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        // 处理只有名称和描述的规则
        return `
            <div class="rule-item">
                <h6>${rule.name}</h6>
                <p class="rule-description">${rule.description}</p>
            </div>
        `;
    }
    
    // 更新状态文本
    updateStatus(message) {
        const statusElement = document.getElementById('statusText');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }
}

// 初始化规则应用
function initializeRulesApp() {
    // 确保DOM已加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeRulesApp);
        return;
    }
    
    // 检查关键元素是否存在
    const requiredElements = ['rulesContent'];
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        console.warn('Missing elements:', missingElements);
        // 延迟重试
        setTimeout(initializeRulesApp, 100);
        return;
    }
    
    // 所有元素都存在，初始化应用
    window.rulesApp = new RulesApp();
}

// 开始初始化
initializeRulesApp();
