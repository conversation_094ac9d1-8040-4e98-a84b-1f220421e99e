{"version": "1.0.0", "source": "backend/handlebars-to-sline-rules", "category": "assignment", "lastUpdated": "2025-08-06T00:47:00.000Z", "data": {"HANDLEBARS_ASSIGNMENT_RULES": [{"name": "Handlebars 变量赋值转换", "category": "assignment", "description": "转换 Handlebars 变量赋值到 Sline var 标签", "pattern": {}, "examples": {"handlebars": "{{#assign price = product.price}}", "sline": "{{#var price = product.price /}}"}}, {"name": "Handlebars 变量赋值转换 - 带过滤器", "category": "assignment", "description": "转换带过滤器的 Handlebars 变量赋值", "pattern": {}, "examples": {"handlebars": "{{#assign title = (upcase product.title)}}", "sline": "{{#var title = product.title|upcase() /}}"}}, {"name": "Handlebars with 语句转换", "category": "assignment", "description": "转换 Handlebars with 语句到 Sline var 标签", "pattern": {}, "examples": {"handlebars": "{{#with product as |p|}}", "sline": "{{#var p = product}}{{#with p}}"}}, {"name": "Handlebars let 语句转换", "category": "assignment", "description": "转换 Handlebars let 语句到 Sline var 标签", "pattern": {}, "examples": {"handlebars": "{{#let price=product.price}}", "sline": "{{#var price = product.price /}}"}}, {"name": "Handlebars 局部变量转换", "category": "assignment", "description": "转换 Handlebars 局部变量定义", "pattern": {}, "examples": {"handlebars": "{{#set title product.title}}", "sline": "{{#var title = product.title /}}"}}]}}