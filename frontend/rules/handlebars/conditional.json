{"version": "1.0.0", "source": "backend/rules", "category": "conditional", "lastUpdated": "2025-07-18T13:28:24.346Z", "data": {"CONDITIONAL_TAG_RULES": [{"name": "case/when 语句转换", "category": "conditional", "description": "转换 Handlebars case/when 到 Sline switch/case", "pattern": {}, "replacement": "{{#switch $1}}", "examples": {"handlebars": "{{#case product.type}}", "sline": "{{#switch product.type}}"}}, {"name": "when 分支转换 - 带引号字符串", "category": "conditional", "description": "转换 when 分支到 case 自闭合标签（字符串值保持引号）", "pattern": {}, "replacement": "{{#case \"$1\" /}}", "examples": {"handlebars": "{{when \"shirt\"}}", "sline": "{{#case \"shirt\" /}}"}}, {"name": "when 分支转换 - 变量", "category": "conditional", "description": "转换 when 分支到 case 自闭合标签（变量不加引号）", "pattern": {}, "replacement": "{{#case $1 /}}", "examples": {"handlebars": "{{when shirt}}", "sline": "{{#case shirt /}}"}}, {"name": "case 结束标签", "category": "conditional", "description": "转换 case 结束标签", "pattern": {}, "replacement": "{{/switch}}", "examples": {"handlebars": "{{/case}}", "sline": "{{/switch}}"}}, {"name": "if 标签转换 - 带复杂条件", "category": "conditional", "description": "转换 Handlebars 复杂条件表达式到 Sline", "pattern": {}, "replacement": "{{#if $1}}", "examples": {"handlebars": "{{#if (and product.available (gt product.price 100))}}", "sline": "{{#if product.available and product.price > 100}}"}}, {"name": "if 标签转换 - 基础", "category": "conditional", "description": "转换 Handlebars if 到 Sline if", "pattern": {}, "replacement": "{{#if $1}}", "examples": {"handlebars": "{{#if product.available}}", "sline": "{{#if product.available}}"}}, {"name": "unless 标签转换 - 复杂条件", "category": "conditional", "description": "转换 unless 复杂条件到 if 否定形式", "pattern": {}, "replacement": "{{#if !($1)}}", "examples": {"handlebars": "{{#unless (and product.available (eq product.stock 0))}}", "sline": "{{#if !(product.available and product.stock == 0)}}"}}, {"name": "unless 标签转换 - 基础", "category": "conditional", "description": "转换 unless 到 if 否定形式", "pattern": {}, "replacement": "{{#if !($1)}}", "examples": {"handlebars": "{{#unless product.available}}", "sline": "{{#if !(product.available)}}"}}, {"name": "else 标签转换", "category": "conditional", "description": "转换 else 到 Sline 自闭合格式", "pattern": {}, "replacement": "{{#else /}}", "examples": {"handlebars": "{{else}}", "sline": "{{#else /}}"}}, {"name": "unless 结束标签", "category": "conditional", "description": "转换 unless 结束标签", "pattern": {}, "replacement": "{{/if}}", "examples": {"handlebars": "{{/unless}}", "sline": "{{/if}}"}}, {"name": "elseif 标签转换", "category": "conditional", "description": "转换 elseif 到 Sline 格式", "pattern": {}, "replacement": "{{#else if $1 /}}", "examples": {"handlebars": "{{else if product.sale}}", "sline": "{{#else if product.sale /}}"}}]}}