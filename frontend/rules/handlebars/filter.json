{"version": "1.0.0", "source": "backend/rules", "category": "filter", "lastUpdated": "2025-07-18T13:28:24.348Z", "data": {"FILTER_TAG_RULES": [{"name": "size 过滤器转换", "category": "filter", "description": "转换 .size 属性到 size() 过滤器", "pattern": {}, "replacement": "$1|size()", "examples": {"handlebars": "{{items.size}}", "sline": "{{items|size()}}"}}, {"name": "contains 过滤器转换", "category": "filter", "description": "转换 contains 操作到过滤器语法", "pattern": {}, "replacement": "$1|contains(\"$2\")", "examples": {"handlebars": "{{tags contains \"sale\"}}", "sline": "{{tags|contains(\"sale\")}}"}}, {"name": "单参数过滤器转换", "category": "filter", "description": "转换 Handlebars 单参数辅助函数到 Sline 过滤器（带括号）", "examples": {"handlebars": "{{capitalize product.title}}", "sline": "{{product.title|capitalize()}}"}}, {"name": "多参数过滤器转换", "category": "filter", "description": "转换 Handlebars 多参数辅助函数到 Sline 过滤器（带括号）", "examples": {"handlebars": "{{truncate product.description 100}}", "sline": "{{product.description|truncate(100)}}"}}]}}