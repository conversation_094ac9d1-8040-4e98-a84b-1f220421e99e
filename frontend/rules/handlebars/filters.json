{"version": "1.0.0", "source": "backend/rules", "category": "filters", "lastUpdated": "2025-07-18T13:28:24.346Z", "data": {"FILTER_MAPPINGS": {"capitalize": "capitalize", "downcase": "downcase", "upcase": "upcase", "truncate": "truncate", "strip_html": "strip_html", "strip_newlines": "strip_newlines", "newline_to_br": "newline_to_br", "escape": "escape", "url_encode": "url_encode", "url_decode": "url_decode", "strip": "strip", "lstrip": "lstrip", "rstrip": "rstrip", "abs": "abs", "ceil": "ceil", "floor": "floor", "round": "round", "plus": "plus", "minus": "minus", "times": "times", "divided_by": "divided_by", "modulo": "modulo", "money": "money", "money_with_currency": "money_with_currency", "money_without_currency": "money_without_currency", "money_without_trailing_zeros": "money_without_trailing_zeros", "date": "date", "size": "size", "first": "first", "last": "last", "join": "join", "sort": "sort", "sort_natural": "sort_natural", "reverse": "reverse", "uniq": "uniq", "map": "map", "where": "where", "slice": "slice", "asset_url": "asset_url", "file_url": "file_url", "img_url": "img_url", "url": "url", "link_to": "link_to", "script_tag": "script_tag", "stylesheet_tag": "stylesheet_tag", "img_tag": "img_tag", "default": "default", "json": "json", "raw": "raw", "base64_encode": "base64_encode", "base64_decode": "base64_decode", "md5": "md5", "sha1": "sha1", "sha256": "sha256", "hmac_sha1": "hmac_sha1", "hmac_sha256": "hmac_sha256"}, "FILTER_CATEGORIES": [{"name": "字符串过滤器", "description": "用于处理字符串内容的过滤器", "filters": ["capitalize", "downcase", "upcase", "truncate", "strip_html", "strip_newlines", "newline_to_br", "escape", "url_encode", "url_decode", "strip", "lstrip", "rstrip"]}, {"name": "数字过滤器", "description": "用于数学运算和数字处理的过滤器", "filters": ["abs", "ceil", "floor", "round", "plus", "minus", "times", "divided_by", "modulo"]}, {"name": "货币过滤器", "description": "用于格式化货币显示的过滤器", "filters": ["money", "money_with_currency", "money_without_currency", "money_without_trailing_zeros"]}, {"name": "日期过滤器", "description": "用于处理日期和时间的过滤器", "filters": ["date"]}, {"name": "数组过滤器", "description": "用于处理数组和集合的过滤器", "filters": ["size", "first", "last", "join", "sort", "sort_natural", "reverse", "uniq", "map", "where", "slice"]}, {"name": "URL 过滤器", "description": "用于生成和处理 URL 的过滤器", "filters": ["asset_url", "file_url", "img_url", "url"]}, {"name": "HTML 过滤器", "description": "用于生成 HTML 标签的过滤器", "filters": ["link_to", "script_tag", "stylesheet_tag", "img_tag"]}, {"name": "编码过滤器", "description": "用于数据编码和解码的过滤器", "filters": ["base64_encode", "base64_decode", "md5", "sha1", "sha256", "hmac_sha1", "hmac_sha256"]}, {"name": "其他过滤器", "description": "其他实用的过滤器", "filters": ["default", "json", "raw"]}]}}