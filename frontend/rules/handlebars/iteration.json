{"version": "1.0.0", "source": "backend/rules", "category": "iteration", "lastUpdated": "2025-07-18T13:28:24.347Z", "data": {"ITERATION_TAG_RULES": [{"name": "each 循环转换 - 带别名和索引", "category": "iteration", "description": "转换 each 循环带别名和索引", "pattern": {}, "replacement": "{{#for $2 in $1}}", "examples": {"handlebars": "{{#each products as |product index|}}", "sline": "{{#for product in products}}"}}, {"name": "each 循环转换 - 带别名", "category": "iteration", "description": "转换 each 循环带别名", "pattern": {}, "replacement": "{{#for $2 in $1}}", "examples": {"handlebars": "{{#each products as |product|}}", "sline": "{{#for product in products}}"}}, {"name": "each 循环转换 - 基础", "category": "iteration", "description": "转换基础 each 循环", "pattern": {}, "replacement": "{{#for item in $1}}", "examples": {"handlebars": "{{#each products}}", "sline": "{{#for item in products}}"}}, {"name": "each 结束标签", "category": "iteration", "description": "转换 each 结束标签", "pattern": {}, "replacement": "{{/for}}", "examples": {"handlebars": "{{/each}}", "sline": "{{/for}}"}}, {"name": "循环索引变量转换", "category": "iteration", "description": "转换循环内的索引变量", "pattern": {}, "replacement": "{{forloop.index0}}", "examples": {"handlebars": "{{@index}}", "sline": "{{forloop.index0}}"}}, {"name": "循环第一项变量转换", "category": "iteration", "description": "转换循环内的第一项检查", "pattern": {}, "replacement": "{{forloop.first}}", "examples": {"handlebars": "{{@first}}", "sline": "{{forloop.first}}"}}, {"name": "循环最后项变量转换", "category": "iteration", "description": "转换循环内的最后项检查", "pattern": {}, "replacement": "{{forloop.last}}", "examples": {"handlebars": "{{@last}}", "sline": "{{forloop.last}}"}}, {"name": "循环键值变量转换", "category": "iteration", "description": "转换循环内的键值访问", "pattern": {}, "replacement": "{{forloop.key}}", "examples": {"handlebars": "{{@key}}", "sline": "{{forloop.key}}"}}, {"name": "当前项变量转换", "category": "iteration", "description": "转换循环内的当前项引用", "pattern": {}, "replacement": "{{item}}", "examples": {"handlebars": "{{this}}", "sline": "{{item}}"}}]}}