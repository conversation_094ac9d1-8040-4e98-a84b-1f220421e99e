{"version": "1.0.0", "source": "backend/rules", "category": "objects", "lastUpdated": "2025-07-18T13:28:24.345Z", "data": {"OBJECT_MAPPINGS": {"shop": "shop", "store": "shop", "product": "product", "products": "products", "product.title": "product.title", "product.price": "product.price", "product.compare_at_price": "product.compare_at_price", "product.description": "product.description", "product.images": "product.images", "product.variants": "product.variants", "product.tags": "product.tags", "product.handle": "product.handle", "product.available": "product.available", "product.vendor": "product.vendor", "product.type": "product.type", "collection": "collection", "collections": "collections", "collection.title": "collection.title", "collection.description": "collection.description", "collection.products": "collection.products", "collection.handle": "collection.handle", "page": "page", "pages": "pages", "page_title": "page.title", "page_description": "page.description", "customer": "customer", "customer.email": "customer.email", "customer.first_name": "customer.first_name", "customer.last_name": "customer.last_name", "cart": "cart", "cart.item_count": "cart.item_count", "cart.total_price": "cart.total_price", "cart.items": "cart.items", "settings": "settings", "template": "template", "content_for_header": "content_for_header", "content_for_layout": "content_for_layout", "content_for_footer": "content_for_footer", "request": "request", "request.locale": "request.locale.iso_code"}, "OBJECT_CATEGORIES": [{"name": "商店对象", "description": "商店基本信息和设置", "objects": ["shop", "store", "settings"]}, {"name": "产品对象", "description": "产品相关的所有数据", "objects": ["product", "products", "product.title", "product.price", "product.compare_at_price", "product.description", "product.images", "product.variants", "product.tags", "product.handle", "product.available", "product.vendor", "product.type"]}, {"name": "集合对象", "description": "产品集合相关数据", "objects": ["collection", "collections", "collection.title", "collection.description", "collection.products", "collection.handle"]}, {"name": "页面对象", "description": "页面和内容相关数据", "objects": ["page", "pages", "page_title", "page_description"]}, {"name": "客户对象", "description": "客户信息相关数据", "objects": ["customer", "customer.email", "customer.first_name", "customer.last_name"]}, {"name": "购物车对象", "description": "购物车和订单相关数据", "objects": ["cart", "cart.item_count", "cart.total_price", "cart.items"]}, {"name": "模板对象", "description": "模板系统相关对象", "objects": ["template", "content_for_header", "content_for_layout", "content_for_footer"]}, {"name": "请求对象", "description": "请求和上下文相关数据", "objects": ["request", "request.locale"]}]}}