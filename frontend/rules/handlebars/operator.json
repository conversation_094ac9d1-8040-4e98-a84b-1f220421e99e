{"version": "1.0.0", "source": "backend/rules", "category": "operator", "lastUpdated": "2025-07-18T13:28:24.348Z", "data": {"OPERATOR_TAG_RULES": [{"name": "Handlebars and 辅助函数转换", "category": "operator", "description": "转换 and 辅助函数到 && 运算符", "pattern": {}, "replacement": "$1 && $2", "examples": {"handlebars": "{{#if (and product.available product.price)}}", "sline": "{{#if product.available && product.price}}"}}, {"name": "Handlebars or 辅助函数转换", "category": "operator", "description": "转换 or 辅助函数到 || 运算符", "pattern": {}, "replacement": "$1 || $2", "examples": {"handlebars": "{{#if (or product.available product.preorder)}}", "sline": "{{#if product.available || product.preorder}}"}}, {"name": "Handlebars and 关键字转换", "category": "operator", "description": "转换 Handlebars and 关键字到 && 运算符（仅在条件中）", "pattern": {}, "replacement": "$1 && $2", "examples": {"handlebars": "{{#if product.available and product.price}}", "sline": "{{#if product.available && product.price}}"}}, {"name": "Handlebars or 关键字转换", "category": "operator", "description": "转换 Handlebars or 关键字到 || 运算符（仅在条件中）", "pattern": {}, "replacement": "$1 || $2", "examples": {"handlebars": "{{#if product.available or product.preorder}}", "sline": "{{#if product.available || product.preorder}}"}}, {"name": "严格相等运算符转换", "category": "operator", "description": "转换 === 到 ==", "pattern": {}, "replacement": "==", "examples": {"handlebars": "{{#if product.type === \"book\"}}", "sline": "{{#if product.type == \"book\"}}"}}, {"name": "严格不等运算符转换", "category": "operator", "description": "转换 !== 到 !=", "pattern": {}, "replacement": "!=", "examples": {"handlebars": "{{#if product.type !== \"book\"}}", "sline": "{{#if product.type != \"book\"}}"}}, {"name": "Handlebars gt 辅助函数转换", "category": "operator", "description": "转换 gt 辅助函数到 > 运算符", "pattern": {}, "replacement": "$1 > $2", "examples": {"handlebars": "{{#if (gt product.price 100)}}", "sline": "{{#if product.price > 100}}"}}, {"name": "Handlebars lt 辅助函数转换", "category": "operator", "description": "转换 lt 辅助函数到 < 运算符", "pattern": {}, "replacement": "$1 < $2", "examples": {"handlebars": "{{#if (lt product.price 50)}}", "sline": "{{#if product.price < 50}}"}}, {"name": "Handlebars eq 辅助函数转换", "category": "operator", "description": "转换 eq 辅助函数到 == 运算符", "pattern": {}, "replacement": "$1 == $2", "examples": {"handlebars": "{{#if (eq product.type \"book\")}}", "sline": "{{#if product.type == \"book\"}}"}}]}}