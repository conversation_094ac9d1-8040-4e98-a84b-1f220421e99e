{"version": "1.0.0", "source": "backend/rules", "category": "output", "lastUpdated": "2025-07-18T13:28:24.347Z", "data": {"OUTPUT_TAG_RULES": [{"name": "原始输出转换", "category": "output", "description": "转换三重花括号原始输出到 raw 过滤器", "pattern": {}, "replacement": "{{$1|raw()}}", "examples": {"handlebars": "{{{product.description}}}", "sline": "{{product.description|raw()}}"}}, {"name": "注释转换 - 块注释（已经是正确格式）", "category": "output", "description": "Handlebars 块注释已经是 Sline 兼容格式，无需转换", "pattern": {}, "replacement": "{{!-- $1 --}}", "examples": {"handlebars": "{{!-- This is a comment --}}", "sline": "{{!-- This is a comment --}}"}}, {"name": "注释转换 - 简单注释", "category": "output", "description": "转换简单注释格式到 Sline", "pattern": {}, "replacement": "{{!-- $1 --}}", "examples": {"handlebars": "{{! This is a comment}}", "sline": "{{!-- This is a comment --}}"}}]}}