{"version": "1.0.0", "source": "backend/rules", "category": "shopline", "lastUpdated": "2025-07-18T13:28:24.348Z", "data": {"SHOPLINE_TAG_RULES": [{"name": "Handlebars assign 变量赋值转换", "category": "shopline", "description": "转换 Handlebars assign 辅助函数到 Sline var 语法", "pattern": {}, "replacement": "{{#var $1 = $2 /}}", "examples": {"handlebars": "{{assign price = product.price}}", "sline": "{{#var price = product.price /}}"}}, {"name": "snippet 转换为 include 自闭合标签", "category": "shopline", "description": "转换 Handlebars snippet 辅助函数到 Sline include 自闭合标签", "pattern": {}, "replacement": "{{#include \"$1\" /}}", "examples": {"handlebars": "{{ snippet 'theme-css-var' }}", "sline": "{{#include \"theme-css-var\" /}}"}}, {"name": "snippet 带参数转换为 include", "category": "shopline", "description": "转换带参数的 snippet 到 include", "pattern": {}, "replacement": "{{#include \"$1\" $2 /}}", "examples": {"handlebars": "{{ snippet 'activity/promotion-tag' benefitCondition=this }}", "sline": "{{#include \"activity/promotion-tag\" benefitCondition=this /}}"}}, {"name": "content_for_header 转换", "category": "shopline", "description": "转换 content_for_header 到 include 自闭合标签", "pattern": {}, "replacement": "{{#include \"content_for_header\" /}}", "examples": {"handlebars": "{{ content_for_header }}", "sline": "{{#include \"content_for_header\" /}}"}}, {"name": "content_for_layout 转换", "category": "shopline", "description": "转换 content_for_layout 到 include 自闭合标签", "pattern": {}, "replacement": "{{#include \"content_for_layout\" /}}", "examples": {"handlebars": "{{ content_for_layout }}", "sline": "{{#include \"content_for_layout\" /}}"}}, {"name": "content_for_footer 转换", "category": "shopline", "description": "转换 content_for_footer 到 include 自闭合标签", "pattern": {}, "replacement": "{{#include \"content_for_footer\" /}}", "examples": {"handlebars": "{{ content_for_footer }}", "sline": "{{#include \"content_for_footer\" /}}"}}, {"name": "section 自闭合标签", "category": "shopline", "description": "转换 section 标签到 Sline 自闭合语法", "pattern": {}, "replacement": "{{#section \"$1\" /}}", "examples": {"handlebars": "{{section \"main-password-header\"}}", "sline": "{{#section \"main-password-header\" /}}"}}, {"name": "stylesheet 标签转换", "category": "shopline", "description": "转换样式表标签到 Sline component 语法", "pattern": {}, "replacement": "{{#component \"stylesheet\" src=\"$1\" | asset_url() /}}", "examples": {"handlebars": "{{ stylesheet src=\"base/index.css\" }}", "sline": "{{#component \"stylesheet\" src=\"base/index.css\" | asset_url() /}}"}}, {"name": "script 标签转换", "category": "shopline", "description": "转换脚本标签到 Sline component 语法", "pattern": {}, "replacement": "{{#component \"script\" src=\"$1\" | asset_url() /}}", "examples": {"handlebars": "{{ script src=\"base/index.js\" }}", "sline": "{{#component \"script\" src=\"base/index.js\" | asset_url() /}}"}}, {"name": "combine_asset_tag 智能转换", "category": "shopline", "description": "智能转换 combine_asset_tag，CSS 转为 stylesheet，JS 转为 script component", "examples": {"handlebars": "{{ combine_asset_tag 'commons/layout/theme/index.rtl.css' inline=true}}\n{{ combine_asset_tag 'vendors/swiper.min.js' type=\"text/javascript\" defer=true }}\n{{ combine_asset_tag 'theme-shared/utils/sectionsLoad/index.js' 'stage/slideshow/index.js' defer=true }}", "sline": "{{#component \"stylesheet\" src=\"commons/layout/theme/index.rtl.css\" | asset_url() /}}\n{{#component \"script\" src=\"vendors/swiper.min.js\" | asset_url() /}}\n{{#component \"script\" src=\"theme-shared/utils/sectionsLoad/index.js\" | asset_url() /}}\n{{#component \"script\" src=\"stage/slideshow/index.js\" | asset_url() /}}"}}, {"name": "preload_state 转换警告", "category": "shopline", "description": "preload_state 需要手动处理", "pattern": {}, "replacement": "{{!-- TODO: preload_state needs manual conversion: $1 --}}", "warning": "preload_state 需要根据 Sline 的状态管理机制手动转换", "examples": {"handlebars": "{{ preload_state 'storeInfo' 'cartInfo' }}", "sline": "{{!-- TODO: preload_state needs manual conversion --}}"}}, {"name": "Handlebars with 上下文转换警告", "category": "shopline", "description": "with 语法需要手动处理（Sline 不支持 with）", "pattern": {}, "replacement": "{{!-- TODO: with context needs manual conversion: $1 --}}", "warning": "Sline 不支持 with 语法，需要手动重构代码", "examples": {"handlebars": "{{#with product}}", "sline": "{{!-- TODO: with context needs manual conversion: product --}}"}}, {"name": "Handlebars range 辅助函数转换", "category": "shopline", "description": "转换 Handlebars range 到 Sline range 过滤器语法", "pattern": {}, "replacement": "{{#for $3 in $2|range($1)}}", "examples": {"handlebars": "{{#each (1..5) as |i|}}", "sline": "{{#for i in 5|range(1)}}"}}]}}