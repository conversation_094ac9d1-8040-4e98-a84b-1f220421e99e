{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "assignment", "lastUpdated": "2025-07-18T13:28:24.351Z", "data": {"LIQUID_ASSIGNMENT_TAG_RULES": [{"name": "Liquid assign 标签转换", "category": "assignment", "description": "转换 Liquid assign 标签到 Sline var 标签", "pattern": {}, "examples": {"liquid": "{% assign price = product.price %}", "sline": "{{#var price = product.price /}}"}}, {"name": "Liquid assign 标签转换 - 带过滤器", "category": "assignment", "description": "转换带过滤器的 Liquid assign 标签到 Sline var 标签", "pattern": {}, "examples": {"liquid": "{% assign title = product.title | upcase %}", "sline": "{{#var title = product.title|upcase() /}}"}}, {"name": "Liquid capture 标签转换", "category": "assignment", "description": "转换 Liquid capture 标签到 Sline capture 标签", "pattern": {}, "examples": {"liquid": "{% capture my_variable %}", "sline": "{{#capture my_variable}}"}}, {"name": "Liquid endcapture 标签转换", "category": "assignment", "description": "转换 Liquid endcapture 标签到 Sline /capture 标签", "pattern": {}, "examples": {"liquid": "{% endcapture %}", "sline": "{{/capture}}"}}, {"name": "Liquid increment 标签转换", "category": "assignment", "description": "转换 Liquid increment 标签到 Sline increment 标签", "pattern": {}, "examples": {"liquid": "{% increment counter %}", "sline": "{{#increment counter /}}"}}, {"name": "Liquid decrement 标签转换", "category": "assignment", "description": "转换 Liquid decrement 标签到 Sline decrement 标签", "pattern": {}, "examples": {"liquid": "{% decrement counter %}", "sline": "{{#decrement counter /}}"}}, {"name": "Liquid assign 标签转换 - 字符串拼接", "category": "assignment", "description": "转换包含字符串拼接的 Liquid assign 标签", "pattern": {}, "examples": {"liquid": "{% assign url = \"/products/\" | append: product.handle %}", "sline": "{{#var url = \"/products/\"|append(product.handle) /}}"}}, {"name": "Liquid assign 标签转换 - 数组操作", "category": "assignment", "description": "转换包含数组操作的 Liquid assign 标签", "pattern": {}, "examples": {"liquid": "{% assign product_titles = collection.products | map: \"title\" %}", "sline": "{{#var product_titles = collection.products|map(\"title\") /}}"}}, {"name": "Liquid assign 标签转换 - 条件赋值", "category": "assignment", "description": "转换包含条件的 Liquid assign 标签", "pattern": {}, "examples": {"liquid": "{% assign title = product.title | default: \"No Title\" %}", "sline": "{{#var title = product.title|default(\"No Title\") /}}"}}]}}