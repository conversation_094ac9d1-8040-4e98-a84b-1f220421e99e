{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "comment", "lastUpdated": "2025-07-18T13:28:24.351Z", "data": {"LIQUID_COMMENT_TAG_RULES": [{"name": "Liquid comment 标签转换", "category": "comment", "description": "转换 Liquid comment 标签到 Sline 注释语法", "pattern": {}, "examples": {"liquid": "{% comment %}", "sline": "{{!--"}}, {"name": "Liquid endcomment 标签转换", "category": "comment", "description": "转换 Liquid endcomment 标签到 Sline 注释结束语法", "pattern": {}, "examples": {"liquid": "{% endcomment %}", "sline": "--}}"}}, {"name": "Liquid 完整注释块转换", "category": "comment", "description": "转换完整的 Liquid 注释块到 Sline 注释语法", "pattern": {}, "examples": {"liquid": "{% comment %}This is a comment{% endcomment %}", "sline": "{{!--This is a comment--}}"}}, {"name": "Liquid raw 标签转换", "category": "comment", "description": "转换 Liquid raw 标签到 Sline raw 标签", "pattern": {}, "examples": {"liquid": "{% raw %}", "sline": "{{#raw}}"}}, {"name": "Liquid endraw 标签转换", "category": "comment", "description": "转换 Liquid endraw 标签到 Sline /raw 标签", "pattern": {}, "examples": {"liquid": "{% endraw %}", "sline": "{{/raw}}"}}, {"name": "Liquid 完整 raw 块转换", "category": "comment", "description": "转换完整的 Liquid raw 块到 Sline raw 语法", "pattern": {}, "examples": {"liquid": "{% raw %}{{ some_liquid_code }}{% endraw %}", "sline": "{{#raw}}{{ some_liquid_code }}{{/raw}}"}}, {"name": "Liquid 单行注释转换", "category": "comment", "description": "转换 Liquid 单行注释到 Sline 注释语法", "pattern": {}, "examples": {"liquid": "{% comment %} This is a single line comment {% endcomment %}", "sline": "{{!-- This is a single line comment --}}"}}, {"name": "Liquid 多行注释转换", "category": "comment", "description": "转换 Liquid 多行注释到 Sline 注释语法", "pattern": {}, "examples": {"liquid": "{% comment %}\n  This is a\n  multi-line comment\n{% endcomment %}", "sline": "{{!--\n   This is a\n   multi-line comment\n--}}"}}, {"name": "Liquid 注释中的 assign 转换", "category": "comment", "description": "转换注释中包含的 assign 语句（特殊情况）", "pattern": {}, "examples": {"liquid": "{% comment %}{% assign test = \"value\" %}{% endcomment %}", "sline": "{{!--{{#var test = \"value\" /}}--}}"}}, {"name": "Liquid 条件注释转换", "category": "comment", "description": "转换包含条件逻辑的 Liquid 注释", "pattern": {}, "examples": {"liquid": "{% comment %}{% if product.available %}Available{% endif %}{% endcomment %}", "sline": "{{!-- COMMENTED CODE:\n{% if product.available %}Available{% endif %}\n--}}"}}]}}