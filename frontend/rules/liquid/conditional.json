{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "conditional", "lastUpdated": "2025-07-18T13:28:24.350Z", "data": {"LIQUID_CONDITIONAL_TAG_RULES": [{"name": "Liquid if 标签转换", "category": "conditional", "description": "转换 Liquid if 标签到 Sline if 标签", "pattern": {}, "examples": {"liquid": "{% if product.available %}", "sline": "{{#if product.available}}"}}, {"name": "Liquid elsif 标签转换", "category": "conditional", "description": "转换 Liquid elsif 标签到 Sline elsif 标签", "pattern": {}, "examples": {"liquid": "{% elsif product.price > 100 %}", "sline": "{{#elsif product.price > 100}}"}}, {"name": "Liquid else 标签转换", "category": "conditional", "description": "转换 Liquid else 标签到 Sline else 标签", "pattern": {}, "examples": {"liquid": "{% else %}", "sline": "{{#else /}}"}}, {"name": "Liquid endif 标签转换", "category": "conditional", "description": "转换 Liquid endif 标签到 Sline /if 标签", "pattern": {}, "examples": {"liquid": "{% endif %}", "sline": "{{/if}}"}}, {"name": "Liquid unless 标签转换", "category": "conditional", "description": "转换 Liquid unless 标签到 Sline if 标签（否定条件）", "pattern": {}, "examples": {"liquid": "{% unless product.available %}", "sline": "{{#if !product.available}}"}}, {"name": "Liquid endunless 标签转换", "category": "conditional", "description": "转换 Liquid endunless 标签到 Sline /if 标签", "pattern": {}, "examples": {"liquid": "{% endunless %}", "sline": "{{/if}}"}}, {"name": "Liquid case 标签转换", "category": "conditional", "description": "转换 Liquid case 标签到 Sline switch 标签", "pattern": {}, "examples": {"liquid": "{% case product.type %}", "sline": "{{#switch product.type}}"}}, {"name": "Liquid when 标签转换 - 字符串值", "category": "conditional", "description": "转换 Liquid when 标签到 Sline case 自闭合标签（字符串值）", "pattern": {}, "examples": {"liquid": "{% when \"shirt\" %}", "sline": "{{#case \"shirt\" /}}"}}, {"name": "Liquid when 标签转换 - 变量值", "category": "conditional", "description": "转换 Liquid when 标签到 Sline case 自闭合标签（变量值）", "pattern": {}, "examples": {"liquid": "{% when shirt %}", "sline": "{{#case shirt /}}"}}, {"name": "Liquid when 标签转换 - 多个值", "category": "conditional", "description": "转换 Liquid when 标签到 Sline case 自闭合标签（多个值）", "pattern": {}, "examples": {"liquid": "{% when \"shirt\", \"pants\" %}", "sline": "{{#case \"shirt\", \"pants\" /}}"}}, {"name": "Liquid endcase 标签转换", "category": "conditional", "description": "转换 Liquid endcase 标签到 Sline /switch 标签", "pattern": {}, "examples": {"liquid": "{% endcase %}", "sline": "{{/switch}}"}}, {"name": "Liquid 复杂条件表达式转换", "category": "conditional", "description": "转换包含 contains、size 等复杂条件的表达式", "pattern": {}, "examples": {"liquid": "{% if product.tags contains \"sale\" and product.available %}", "sline": "{{#if product.tags|contains(\"sale\") && product.available}}"}}]}}