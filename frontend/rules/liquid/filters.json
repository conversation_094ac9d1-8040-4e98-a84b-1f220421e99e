{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "filters", "lastUpdated": "2025-07-18T13:28:24.349Z", "data": {"LIQUID_FILTER_MAPPINGS": {"capitalize": "capitalize", "downcase": "downcase", "upcase": "upcase", "escape": "escape", "escape_once": "escape_once", "newline_to_br": "newline_to_br", "strip_html": "strip_html", "strip_newlines": "strip_newlines", "truncate": "truncate", "truncatewords": "truncatewords", "lstrip": "trim_left", "rstrip": "trim_right", "strip": "trim", "replace": "replace", "replace_first": "replace_first", "remove": "remove", "remove_first": "remove_first", "append": "append", "prepend": "prepend", "split": "split", "slice": "slice", "abs": "abs", "ceil": "ceil", "floor": "floor", "round": "round", "plus": "plus", "minus": "minus", "times": "times", "divided_by": "divided_by", "modulo": "modulo", "at_most": "at_most", "at_least": "at_least", "join": "join", "first": "first", "last": "last", "concat": "concat", "index": "index", "map": "map", "reverse": "reverse", "size": "size", "sort": "sort", "sort_natural": "sort_natural", "uniq": "uniq", "where": "where", "group_by": "group_by", "compact": "compact", "flatten": "flatten", "date": "date", "strftime": "strftime", "url_encode": "url_encode", "url_decode": "url_decode", "url_escape": "url_escape", "url_param_escape": "url_param_escape", "money": "money", "money_with_currency": "money_with_currency", "money_without_currency": "money_without_currency", "money_without_trailing_zeros": "money_without_trailing_zeros", "img_url": "img_url", "img_tag": "img_tag", "asset_url": "asset_url", "asset_img_url": "asset_img_url", "file_url": "file_url", "file_img_url": "file_img_url", "product_img_url": "product_img_url", "collection_img_url": "collection_img_url", "color_to_rgb": "color_to_rgb", "color_to_hsl": "color_to_hsl", "color_to_hex": "color_to_hex", "color_extract": "color_extract", "color_brightness": "color_brightness", "color_modify": "color_modify", "color_lighten": "color_lighten", "color_darken": "color_darken", "color_saturate": "color_saturate", "color_desaturate": "color_desaturate", "color_mix": "color_mix", "color_difference": "color_difference", "font_face": "font_face", "font_url": "font_url", "font_modify": "font_modify", "weight_with_unit": "weight_with_unit", "default": "default", "json": "json", "base64_encode": "base64_encode", "base64_decode": "base64_decode", "md5": "md5", "sha1": "sha1", "sha256": "sha256", "hmac_sha1": "hmac_sha1", "hmac_sha256": "hmac_sha256", "highlight": "highlight", "highlight_active_tag": "highlight_active_tag", "link_to": "link_to", "link_to_vendor": "link_to_vendor", "link_to_type": "link_to_type", "link_to_tag": "link_to_tag", "link_to_add_tag": "link_to_add_tag", "link_to_remove_tag": "link_to_remove_tag", "payment_type_img_url": "payment_type_img_url", "shopify_asset_url": "shopify_asset_url", "global_asset_url": "global_asset_url", "script_tag": "script_tag", "stylesheet_tag": "stylesheet_tag", "placeholder_svg_tag": "placeholder_svg_tag", "customer_login_link": "customer_login_link", "customer_logout_link": "customer_logout_link", "customer_register_link": "customer_register_link", "form": "form", "endform": "endform", "paginate": "paginate", "endpaginate": "endpaginate", "cycle": "cycle", "tablerow": "tablerow", "endtablerow": "endtablerow", "if": "if", "unless": "unless", "elsif": "<PERSON><PERSON><PERSON>", "else": "else", "endif": "endif", "endunless": "endunless", "case": "case", "when": "when", "endcase": "endcase", "for": "for", "endfor": "endfor", "break": "break", "continue": "continue", "assign": "assign", "capture": "capture", "endcapture": "endcapture", "increment": "increment", "decrement": "decrement", "include": "include", "render": "render", "section": "section", "comment": "comment", "endcomment": "endcomment", "raw": "raw", "endraw": "endraw", "layout": "layout", "content_for": "content_for", "yield": "yield", "t": "t", "translate": "translate", "pluralize": "pluralize", "within": "within", "contains": "contains"}, "LIQUID_FILTER_CATEGORIES": [{"name": "string", "description": "字符串处理过滤器", "filters": ["capitalize", "downcase", "upcase", "escape", "escape_once", "newline_to_br", "strip_html", "strip_newlines", "truncate", "truncatewords", "lstrip", "rstrip", "strip", "replace", "replace_first", "remove", "remove_first", "append", "prepend", "split", "slice"]}, {"name": "number", "description": "数字处理过滤器", "filters": ["abs", "ceil", "floor", "round", "plus", "minus", "times", "divided_by", "modulo", "at_most", "at_least"]}, {"name": "array", "description": "数组处理过滤器", "filters": ["join", "first", "last", "concat", "index", "map", "reverse", "size", "sort", "sort_natural", "uniq", "where", "group_by", "compact", "flatten"]}, {"name": "date", "description": "日期处理过滤器", "filters": ["date", "strftime"]}, {"name": "url", "description": "URL 处理过滤器", "filters": ["url_encode", "url_decode", "url_escape", "url_param_escape"]}, {"name": "money", "description": "货币处理过滤器", "filters": ["money", "money_with_currency", "money_without_currency", "money_without_trailing_zeros"]}, {"name": "image", "description": "图片处理过滤器", "filters": ["img_url", "img_tag", "asset_url", "asset_img_url", "file_url", "file_img_url", "product_img_url", "collection_img_url"]}, {"name": "color", "description": "颜色处理过滤器", "filters": ["color_to_rgb", "color_to_hsl", "color_to_hex", "color_extract", "color_brightness", "color_modify", "color_lighten", "color_darken", "color_saturate", "color_desaturate", "color_mix", "color_difference"]}, {"name": "format", "description": "格式化过滤器", "filters": ["default", "json", "base64_encode", "base64_decode", "md5", "sha1", "sha256", "hmac_sha1", "hmac_sha256"]}, {"name": "advanced", "description": "高级过滤器", "filters": ["highlight", "highlight_active_tag", "link_to", "link_to_vendor", "link_to_type", "link_to_tag", "link_to_add_tag", "link_to_remove_tag", "payment_type_img_url", "shopify_asset_url", "global_asset_url", "script_tag", "stylesheet_tag", "placeholder_svg_tag", "customer_login_link", "customer_logout_link", "customer_register_link"]}]}}