{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "include", "lastUpdated": "2025-07-18T13:28:24.352Z", "data": {"LIQUID_INCLUDE_TAG_RULES": [{"name": "Liquid include 标签转换", "category": "include", "description": "转换 Liquid include 标签到 Sline include 标签", "pattern": {}, "examples": {"liquid": "{% include \"product-card.liquid\" %}", "sline": "{{#include \"product-card\" /}}"}}, {"name": "Liquid include 标签转换 - 带参数", "category": "include", "description": "转换带参数的 Liquid include 标签到 Sline include 标签", "pattern": {}, "examples": {"liquid": "{% include \"product-card.liquid\", product: featured_product %}", "sline": "{{#include \"product-card\" product=featured_product /}}"}}, {"name": "Liquid render 标签转换", "category": "include", "description": "转换 Liquid render 标签到 Sline include 标签", "pattern": {}, "examples": {"liquid": "{% render \"product-card\" %}", "sline": "{{#include \"product-card\" /}}"}}, {"name": "Liquid render 标签转换 - 带参数", "category": "include", "description": "转换带参数的 Liquid render 标签到 Sline include 标签", "pattern": {}, "examples": {"liquid": "{% render \"product-card\", product: featured_product %}", "sline": "{{#include \"product-card\" product=featured_product /}}"}}, {"name": "Liquid render 标签转换 - with 语法", "category": "include", "description": "转换使用 with 语法的 Liquid render 标签", "pattern": {}, "examples": {"liquid": "{% render \"product-card\" with featured_product %}", "sline": "{{#include \"product-card\" with=featured_product /}}"}}, {"name": "Liquid render 标签转换 - as 语法", "category": "include", "description": "转换使用 as 语法的 Liquid render 标签", "pattern": {}, "examples": {"liquid": "{% render \"product-card\" with featured_product as product %}", "sline": "{{#include \"product-card\" product=featured_product /}}"}}, {"name": "Liquid section 标签转换", "category": "include", "description": "转换 Liquid section 标签到 Sline include 标签", "pattern": {}, "examples": {"liquid": "{% section \"header\" %}", "sline": "{{#include \"header\" /}}"}}, {"name": "Liquid 特殊包含标签转换", "category": "include", "description": "转换特殊的 Liquid 包含标签（如 render_section）", "pattern": {}, "examples": {"liquid": "{% render_footer %}", "sline": "{{#include \"render_footer\" /}}"}}, {"name": "Liquid layout 标签转换", "category": "include", "description": "转换 Liquid layout 标签到 Sline layout 标签", "pattern": {}, "examples": {"liquid": "{% layout \"theme\" %}", "sline": "{{#layout \"theme\" /}}"}}, {"name": "Liquid content_for 标签转换", "category": "include", "description": "转换 Liquid content_for 标签到 Sline content_for 标签", "pattern": {}, "examples": {"liquid": "{% content_for \"header\" %}", "sline": "{{#content_for \"header\"}}"}}, {"name": "Liquid endcontent_for 标签转换", "category": "include", "description": "转换 Liquid endcontent_for 标签到 Sline /content_for 标签", "pattern": {}, "examples": {"liquid": "{% endcontent_for %}", "sline": "{{/content_for}}"}}, {"name": "Liquid yield 标签转换", "category": "include", "description": "转换 Liquid yield 标签到 Sline yield 标签", "pattern": {}, "examples": {"liquid": "{% yield %}", "sline": "{{#yield /}}"}}]}}