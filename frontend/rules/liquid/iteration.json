{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "iteration", "lastUpdated": "2025-07-18T13:28:24.350Z", "data": {"LIQUID_ITERATION_TAG_RULES": [{"name": "Liquid for 标签转换 - 基础循环", "category": "iteration", "description": "转换 Liquid for 标签到 Sline for 标签", "pattern": {}, "examples": {"liquid": "{% for product in collection.products %}", "sline": "{{#for product in collection.products}}"}}, {"name": "Liquid for 标签转换 - 带过滤器", "category": "iteration", "description": "转换带过滤器的 Liquid for 标签到 Sline for 标签", "pattern": {}, "examples": {"liquid": "{% for product in collection.products limit: 5 %}", "sline": "{{#for product in collection.products|limit(5)}}"}}, {"name": "Liquid for 标签转换 - 范围循环", "category": "iteration", "description": "转换 Liquid 范围循环到 Sline for 标签", "pattern": {}, "examples": {"liquid": "{% for i in (1..5) %}", "sline": "{{#for i in 5|range(1)}}"}}, {"name": "Liquid endfor 标签转换", "category": "iteration", "description": "转换 Liquid endfor 标签到 Sline /for 标签", "pattern": {}, "examples": {"liquid": "{% endfor %}", "sline": "{{/for}}"}}, {"name": "Liquid for-else 标签转换", "category": "iteration", "description": "转换 Liquid for 循环中的 else 标签到 Sline else 标签", "pattern": {}, "examples": {"liquid": "{% else %}", "sline": "{{#else /}}"}}, {"name": "Liquid break 标签转换", "category": "iteration", "description": "转换 Liquid break 标签到 Sline break 标签", "pattern": {}, "examples": {"liquid": "{% break %}", "sline": "{{#break /}}"}}, {"name": "Liquid continue 标签转换", "category": "iteration", "description": "转换 Liquid continue 标签到 Sline continue 标签", "pattern": {}, "examples": {"liquid": "{% continue %}", "sline": "{{#continue /}}"}}, {"name": "Liquid tablerow 标签转换", "category": "iteration", "description": "转换 Liquid tablerow 标签到 Sline tablerow 标签", "pattern": {}, "examples": {"liquid": "{% tablerow product in collection.products cols: 3 %}", "sline": "{{#tablerow product in collection.products cols=\"3\"}}"}}, {"name": "Liquid endtablerow 标签转换", "category": "iteration", "description": "转换 Liquid endtablerow 标签到 Sline /tablerow 标签", "pattern": {}, "examples": {"liquid": "{% endtablerow %}", "sline": "{{/tablerow}}"}}, {"name": "Liquid cycle 标签转换", "category": "iteration", "description": "转换 Liquid cycle 标签到 Sline cycle 标签", "pattern": {}, "examples": {"liquid": "{% cycle \"odd\", \"even\" %}", "sline": "{{#cycle \"odd\", \"even\"}}"}}, {"name": "Liquid forloop 变量转换", "category": "iteration", "description": "转换 Liquid forloop 变量到 Sline 循环变量", "pattern": {}, "examples": {"liquid": "{{ forloop.index }}", "sline": "{{@index}}"}}, {"name": "Liquid tablerowloop 变量转换", "category": "iteration", "description": "转换 Liquid tablerowloop 变量到 Sline 循环变量", "pattern": {}, "examples": {"liquid": "{{ tablerowloop.index }}", "sline": "{{@index}}"}}]}}