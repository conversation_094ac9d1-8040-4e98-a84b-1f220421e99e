{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "output", "lastUpdated": "2025-07-18T13:28:24.352Z", "data": {"LIQUID_OUTPUT_TAG_RULES": [{"name": "Liquid 变量输出转换", "category": "output", "description": "转换 Liquid 变量输出到 Sline 变量输出", "pattern": {}, "examples": {"liquid": "{{ product.title | upcase }}", "sline": "{{ product.title|upcase() }}"}}, {"name": "Liquid 数学表达式输出转换", "category": "output", "description": "转换包含数学运算的 Liquid 变量输出", "pattern": {}, "examples": {"liquid": "{{ product.price | plus: 10 }}", "sline": "{{ product.price|plus(10) }}"}}, {"name": "Liquid 字符串操作输出转换", "category": "output", "description": "转换包含字符串操作的 Liquid 变量输出", "pattern": {}, "examples": {"liquid": "{{ product.title | replace: \"old\", \"new\" }}", "sline": "{{ product.title|replace(\"old\", \"new\") }}"}}]}}