{"version": "1.0.0", "source": "backend/liquid-to-sline-rules", "category": "variables", "lastUpdated": "2025-07-18T13:28:24.350Z", "data": {"LIQUID_VARIABLE_MAPPINGS": {"forloop.index": "@index", "forloop.index0": "@index0", "forloop.rindex": "@rindex", "forloop.rindex0": "@rindex0", "forloop.first": "@first", "forloop.last": "@last", "forloop.length": "@length", "tablerowloop.index": "@index", "tablerowloop.index0": "@index0", "tablerowloop.rindex": "@rindex", "tablerowloop.rindex0": "@rindex0", "tablerowloop.first": "@first", "tablerowloop.last": "@last", "tablerowloop.length": "@length", "tablerowloop.col": "@col", "tablerowloop.col0": "@col0", "tablerowloop.col_first": "@col_first", "tablerowloop.col_last": "@col_last", "tablerowloop.row": "@row", "paginate.current_page": "paginate.current_page", "paginate.current_offset": "paginate.current_offset", "paginate.items": "paginate.items", "paginate.parts": "paginate.parts", "paginate.pages": "paginate.pages", "paginate.previous": "paginate.previous", "paginate.next": "paginate.next", "blank": "null", "empty": "empty", "nil": "null", "null": "null", "true": "true", "false": "false"}, "LIQUID_OPERATOR_MAPPINGS": {"and": "&&", "or": "||", "==": "==", "!=": "!=", "<>": "!=", ">": ">", "<": "<", ">=": ">=", "<=": "<=", "contains": "contains", "=": "="}, "LIQUID_SPECIAL_OPERATORS": {"size": "size()", "..": "range", ".": ".", "[": "[", "]": "]"}, "LIQUID_CONDITION_TRANSFORMS": [{"pattern": {}, "replacement": "$1|size()", "description": "转换 .size 属性为 size() 过滤器"}, {"pattern": {}, "replacement": "$1|contains(\"$2\")", "description": "转换 contains 操作为过滤器"}, {"pattern": {}, "replacement": " || ", "description": "转换 or 为 ||"}, {"pattern": {}, "replacement": " && ", "description": "转换 and 为 &&"}, {"pattern": {}, "replacement": " $1 ", "description": "标准化比较运算符空格"}, {"pattern": {}, "replacement": "\"$1\"", "description": "将单引号转换为双引号"}]}}