#!/bin/bash

# WASM 模块设置脚本
# 将构建好的 WASM 文件复制到前端目录

echo "🚀 Setting up WASM module for frontend..."

# 创建 wasm 目录
mkdir -p wasm/build

# 检查 WASM 构建文件是否存在
if [ ! -f "../wasm/build/converter.wasm" ]; then
    echo "❌ WASM build files not found. Please build the WASM module first:"
    echo "   cd ../wasm && npm run build"
    exit 1
fi

# 复制 WASM 文件
echo "📁 Copying WASM files..."
cp ../wasm/build/converter.wasm wasm/build/
cp ../wasm/build/converter.js wasm/build/
cp ../wasm/build/converter.d.ts wasm/build/

# 检查文件是否复制成功
if [ -f "wasm/build/converter.wasm" ]; then
    echo "✅ WASM files copied successfully!"
    echo "📊 File sizes:"
    ls -lh wasm/build/
else
    echo "❌ Failed to copy WASM files"
    exit 1
fi

echo "🎉 WASM setup completed!"
echo ""
echo "📝 Next steps:"
echo "1. Start your web server"
echo "2. Open handlebars-to-sline.html in browser"
echo "3. The page will automatically use WASM for high-performance conversion"
