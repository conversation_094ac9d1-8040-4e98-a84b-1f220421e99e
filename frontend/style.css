/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #FFFFFF;
    color: #111827;
    line-height: 1.5;
    font-size: 16px;
    font-weight: 400;
}

/* Container and layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Header with hero section */
.header {
    /* position: sticky; */
    top: 0;
    z-index: 100;
    background: #FFFFFF;
    /* border-bottom: 1px solid #E5E7EB; */
    margin: -24px -24px 0 -24px;
    padding: 24px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.hero-section {
    text-align: center;
    padding: 64px 0 32px;
}

.hero-icon {
    margin-bottom: 24px;
}

.hero-icon i {
    font-size: 48px;
    color: #111827;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #111827;
    margin-bottom: 16px;
    letter-spacing: -0.025em;
}

.hero-subtitle {
    font-size: 20px;
    font-weight: 400;
    color: #6B7280;
    margin-bottom: 32px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 200ms ease-in-out;
    text-decoration: none;
    min-height: 44px;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: #000000;
    color: #FFFFFF;
}

.btn-primary:hover:not(:disabled) {
    background: #1F2937;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background: #FFFFFF;
    color: #000000;
    border: 1px solid #D1D5DB;
}

.btn-secondary:hover:not(:disabled) {
    background: #F3F4F6;
    border-color: #9CA3AF;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
}

/* Status bar */
.status-bar {
    background: #F8F9FA;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 24px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.engine-status-item {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    margin: 0 4px;
    background: #E0F2FE;
    color: #0277BD;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'Courier New', monospace;
}

.status-links {
    display: flex;
    gap: 16px;
}

.link-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    background: none;
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 200ms ease-in-out;
    min-height: 32px;
}

.link-button:hover {
    color: #111827;
    background: #FFFFFF;
}

/* Loading indicators */
.loading-indicator {
    display: flex;
    align-items: center;
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #E5E7EB;
    border-top: 2px solid #6B7280;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #E5E7EB;
    border-top: 3px solid #6B7280;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Main content */
.main-content {
    flex: 1;
}

.conversion-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

/* Editor cards */
.editor-card {
    background: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 500px;
    transition: all 200ms ease-in-out;
}

.editor-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.card-header {
    padding: 24px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.card-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header i {
    font-size: 24px;
    color: #6B7280;
}

.card-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Drop zone */
.drop-zone {
    margin: 24px;
    border: 2px dashed #D1D5DB;
    border-radius: 8px;
    padding: 32px 24px;
    text-align: center;
    background: #F8F9FA;
    transition: all 200ms ease-in-out;
    cursor: pointer;
}

.drop-zone.drag-over {
    border-color: #111827;
    background: #F3F4F6;
}

.drop-zone.hidden {
    display: none;
}

.drop-zone-content i {
    font-size: 48px;
    color: #9CA3AF;
    margin-bottom: 16px;
    display: block;
}

.drop-zone-content p {
    color: #6B7280;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.drop-zone-hint {
    color: #9CA3AF;
    font-size: 14px !important;
    font-weight: 400 !important;
}

/* Editor container */
.editor-container {
    flex: 1;
    /* margin: 24px; */
    position: relative;
}

/* Shiki editor styles */
.shiki-editor {
    width: 100%;
    min-height: 350px;
    border: 1px solid transparent;
    /* border-radius: 8px; */
    padding: 16px;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    background: #FFFFFF;
    color: #111827;
    transition: all 200ms ease-in-out;
    overflow: auto;
    position: relative;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.shiki-editor[data-readonly="true"] {
    background: #F8F9FA;
    color: #6B7280;
    opacity: 1;
    z-index: 3;
    pointer-events: none;
}

/* Focus state when textarea is focused */
.editor-container:focus-within .editor-textarea {
    /* border: 1px solid #111827; */
    box-shadow: 0 0 0 2px rgba(17, 24, 39, 0.1);
    /* border-radius: 8px; */
}

/* Show highlighted content when there is content */
.editor-container[data-has-content] .shiki-editor {
    opacity: 1;
    z-index: 2;
    pointer-events: none;
}

.editor-container[data-has-content] .editor-textarea {
    color: transparent;
}


/* Interactive textarea for functionality */
.editor-textarea {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    resize: none;
    border: none;
    outline: none;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    padding: 16px;
    z-index: 3;
    color: #111827;
    background: transparent;
    cursor: text;
    caret-color: #111827;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.editor-textarea:focus {
    outline: none;
}

.editor-textarea[readonly] {
    cursor: default;
    z-index: 1;
    opacity: 0;
}

/* Shiki code block styling */
.shiki {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.shiki code {
    background: transparent !important;
    padding: 0 !important;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;
    font-size: 12px !important;
    line-height: 1.5 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/* Shiki theme overrides for clean appearance */
.shiki .line {
    min-height: 1.5em;
}

/* Placeholder styling when editor is empty */
.shiki-editor[data-placeholder]::before {
    content: attr(data-placeholder);
    color: #9CA3AF;
    position: absolute;
    top: 16px;
    left: 16px;
    pointer-events: none;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    white-space: pre-wrap;
}

.shiki-editor[data-has-content] .shiki-placeholder {
    display: none;
}

/* Rules section */
.rules-section {
    animation: slideDown 300ms ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.rules-card {
    background: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    min-height: 400px;
}

.rules-content {
    padding: 24px;
    background: #F8F9FA;
    border-radius: 0 0 12px 12px;
    min-height: 300px;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 24px;
    color: #6B7280;
}

.loading-content p {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

/* File upload button */
.file-upload-btn {
    cursor: pointer;
}

/* Breadcrumb */
.breadcrumb {
    margin-bottom: 16px;
}

.breadcrumb ol {
    list-style: none;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #6B7280;
}

.breadcrumb li {
    display: flex;
    align-items: center;
}

.breadcrumb li:not(:first-child)::before {
    content: '/';
    margin: 0 8px;
    color: #D1D5DB;
}

.breadcrumb a {
    color: #111827;
    text-decoration: none;
    transition: color 200ms ease-in-out;
}

.breadcrumb a:hover {
    color: #6B7280;
}

/* Rules display styles */
.rules-overview {
    margin-bottom: 32px;
    padding: 24px;
    background: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
}

.rules-overview h4 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #111827;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.stat-item {
    text-align: center;
    padding: 24px 16px;
    background: #F8F9FA;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    transition: all 200ms ease-in-out;
}

.stat-item:hover {
    background: #FFFFFF;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 8px;
    line-height: 1.2;
}

.stat-label {
    color: #6B7280;
    font-weight: 500;
    font-size: 14px;
}

.category-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 24px 0;
    border-bottom: 1px solid #E5E7EB;
    padding-bottom: 16px;
}

.tab-button {
    padding: 8px 16px;
    border: 1px solid #E5E7EB;
    background: #FFFFFF;
    color: #6B7280;
    border-radius: 6px;
    cursor: pointer;
    transition: all 200ms ease-in-out;
    font-size: 14px;
    font-weight: 500;
    min-height: 36px;
}

.tab-button:hover {
    background: #F8F9FA;
    color: #111827;
}

.tab-button.active {
    background: #111827;
    color: #FFFFFF;
    border-color: #111827;
}

.rule-item {
    padding: 24px;
    border-radius: 8px;
    background: #FFFFFF;
    margin-bottom: 16px;
    border: 1px solid #E5E7EB;
    transition: all 200ms ease-in-out;
}

.rule-item:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.rule-item h6 {
    margin: 0 0 8px 0;
    color: #111827;
    font-size: 16px;
    font-weight: 600;
}

.rule-description {
    color: #6B7280;
    font-size: 14px;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.rule-examples {
    background: #F8F9FA;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #E5E7EB;
}

.example-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 12px;
}

.example-row:last-child {
    margin-bottom: 0;
}

.example-label {
    font-weight: 500;
    font-size: 12px;
    color: #6B7280;
    min-width: 80px;
    /* text-transform: uppercase; */
    letter-spacing: 0.5px;
}

.example-row code {
    background: #FFFFFF;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: SF Mono, Monaco, Consolas, monospace;
    font-size: 13px;
    color: #111827;
    border: 1px solid #E5E7EB;
}

/* Filter and Object mapping styles */
.filter-categories,
.object-categories {
    display: grid;
    gap: 24px;
    margin-top: 16px;
}

.filter-category,
.object-category {
    padding: 24px;
    background: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    transition: all 200ms ease-in-out;
}

.filter-category:hover,
.object-category:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.filter-category h6,
.object-category h6 {
    margin: 0 0 16px 0;
    color: #111827;
    font-size: 16px;
    font-weight: 600;
}

.filter-list,
.object-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-tag,
.object-tag {
    display: inline-block;
    padding: 6px 12px;
    background: #F8F9FA;
    color: #6B7280;
    border: 1px solid #E5E7EB;
    border-radius: 6px;
    font-size: 12px;
    font-family: SF Mono, Monaco, Consolas, monospace;
    font-weight: 500;
    transition: all 200ms ease-in-out;
}

.filter-tag:hover,
.object-tag:hover {
    background: #FFFFFF;
    color: #111827;
    border-color: #D1D5DB;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Responsive design */
@media (max-width: 1024px) {
    .conversion-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
        gap: 24px;
    }
    
    .header {
        margin: -16px -16px 0 -16px;
        padding: 16px;
    }
    
    .hero-section {
        padding: 48px 0;
    }
    
    .hero-title {
        font-size: 32px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
    }
    
    .status-bar {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .card-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .card-actions {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .category-tabs {
        flex-direction: column;
    }
    
    .tab-button {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 640px) {
    .hero-title {
        font-size: 24px;
    }
    
    .editor-container {
        margin: 16px;
    }
    
    .card-header {
        padding: 16px;
    }
    
    .rules-content {
        padding: 16px;
    }
}

/* Utility classes */
.success {
    color: #10B981;
}

.error {
    color: #EF4444;
}

.warning {
    color: #F59E0B;
}

/* Accessibility improvements */
.btn:focus,
.link-button:focus,
.tab-button:focus {
    outline: 2px solid #111827;
    outline-offset: 2px;
}

.editor-container textarea:focus {
    outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-secondary {
        border-width: 2px;
    }
    
    .editor-card {
        border-width: 2px;
    }
    
    .drop-zone {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .header,
    .status-bar,
    .cta-buttons,
    .card-actions {
        display: none;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .conversion-grid {
        display: block;
    }
    
    .editor-card {
        border: 1px solid #000;
        break-inside: avoid;
        margin-bottom: 16px;
    }
}

/* Rules table and related styles */
.rules-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
}

.rules-table th,
.rules-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #E5E7EB;
}

.rules-table th {
    background: #F8F9FA;
    font-weight: 600;
    color: #6B7280;
}

.rules-table code {
    background: #F8F9FA;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 0.9em;
}

.rules-section {
    margin-bottom: 24px;
}

.rules-section h4 {
    color: #6B7280;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.helper-mappings {
    display: grid;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.mapping-item {
    padding: 8px 12px;
    background: #F8F9FA;
    border-radius: 6px;
    font-size: 0.9rem;
}

.mapping-item code {
    background: transparent;
    padding: 0;
}

.rules-section ul {
    padding-left: 20px;
}

.rules-section li {
    margin-bottom: 8px;
    color: #9CA3AF;
}

/* Handlebars 特定样式 */
.handlebars-badge {
    background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sline-badge {
    background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin: 48px 0;
}

.feature-card {
    background: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 200ms ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 16px;
    color: #111827;
}

.feature-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #111827;
    line-height: 1.4;
}

.feature-desc {
    font-size: 16px;
    color: #6B7280;
    line-height: 1.5;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin: 32px 0;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #F8F9FA;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #6B7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Section spacing */
.features-section {
    padding: 80px 0;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
    margin: 80px -24px -24px -24px;
    padding: 80px 24px;
    text-align: center;
    color: white;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    line-height: 1.25;
}

.cta-subtitle {
    font-size: 18px;
    margin-bottom: 32px;
    opacity: 0.9;
    line-height: 1.5;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.cta-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Engine toggle button styles */
#engineToggleBtn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    font-weight: 600;
    min-width: 80px;
}

#engineToggleBtn.btn-success {
    background: linear-gradient(135deg, #10B981, #059669);
    border-color: #059669;
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

#engineToggleBtn.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    transform: translateY(-1px);
}

#engineToggleBtn.btn-secondary {
    background: linear-gradient(135deg, #6B7280, #4B5563);
    border-color: #4B5563;
    color: white;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

#engineToggleBtn.btn-secondary:hover {
    background: linear-gradient(135deg, #4B5563, #374151);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    transform: translateY(-1px);
}

#engineToggleBtn i {
    margin-right: 6px;
    font-size: 14px;
}

#engineStatus {
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* WASM performance indicator */
#engineToggleBtn.btn-success::after {
    content: '⚡';
    position: absolute;
    top: -2px;
    right: -2px;
    font-size: 10px;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}