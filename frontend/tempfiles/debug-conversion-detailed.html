<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细转换调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        .input { background: #f0f8ff; padding: 10px; margin: 5px 0; }
        .output { background: #f0fff0; padding: 10px; margin: 5px 0; }
        .error { background: #fff0f0; padding: 10px; margin: 5px 0; color: red; }
        .success { background: #f0fff0; padding: 10px; margin: 5px 0; color: green; }
        .warning { background: #fffacd; padding: 10px; margin: 5px 0; color: orange; }
        pre { margin: 0; font-family: 'Courier New', monospace; white-space: pre-wrap; }
        button { background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #2563eb; }
        .details { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 详细转换调试</h1>
        <p>深入分析 WASM 转换器的行为</p>
        
        <button onclick="runDetailedTests()">🚀 运行详细测试</button>
        <button onclick="testSpecificCases()">🎯 测试特定用例</button>
        
        <div id="results"></div>
    </div>

    <script src="wasm-converter.js"></script>
    <script>
        async function runDetailedTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-case">正在运行详细测试...</div>';

            try {
                // 初始化
                const success = await window.wasmConverter.initialize();
                if (!success) {
                    throw new Error('WASM 转换器初始化失败');
                }

                results.innerHTML = '<div class="success">✅ WASM 转换器初始化成功</div>';

                // 测试各种输入
                const testCases = [
                    // 基础测试
                    { name: '空字符串', input: '', type: 'both' },
                    { name: '纯文本', input: 'Hello World', type: 'both' },
                    { name: '数字', input: '123', type: 'both' },
                    
                    // Handlebars 测试
                    { name: 'HBS 基本变量', input: '{{product.title}}', type: 'handlebars' },
                    { name: 'HBS 带空格变量', input: '{{ product.title }}', type: 'handlebars' },
                    { name: 'HBS 条件', input: '{{#if condition}}Yes{{/if}}', type: 'handlebars' },
                    { name: 'HBS 循环', input: '{{#each items}}{{this}}{{/each}}', type: 'handlebars' },
                    { name: 'HBS 过滤器', input: '{{product.title | upcase}}', type: 'handlebars' },
                    
                    // Liquid 测试
                    { name: 'Liquid 基本变量', input: '{{ product.title }}', type: 'liquid' },
                    { name: 'Liquid 条件', input: '{% if condition %}Yes{% endif %}', type: 'liquid' },
                    { name: 'Liquid 循环', input: '{% for item in items %}{{item}}{% endfor %}', type: 'liquid' },
                    { name: 'Liquid 赋值', input: '{% assign name = "value" %}', type: 'liquid' },
                    { name: 'Liquid 注释', input: '{% comment %}This is a comment{% endcomment %}', type: 'liquid' },
                    { name: 'Liquid 包含', input: '{% include "template" %}', type: 'liquid' },
                    
                    // 复杂测试
                    { name: '混合内容', input: 'Hello {{name}}, welcome!', type: 'both' },
                    { name: '嵌套结构', input: '{{#if user}}{{#each posts}}{{title}}{{/each}}{{/if}}', type: 'handlebars' },
                    { name: 'Liquid 嵌套', input: '{% if user %}{% for post in posts %}{{ post.title }}{% endfor %}{% endif %}', type: 'liquid' }
                ];

                for (const testCase of testCases) {
                    await runSingleTest(testCase, results);
                }

                // 显示总结
                const summary = document.createElement('div');
                summary.className = 'test-case success';
                summary.innerHTML = '<h3>🎉 详细测试完成</h3><p>请查看上述结果分析转换行为</p>';
                results.appendChild(summary);

            } catch (error) {
                results.innerHTML += `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        async function runSingleTest(testCase, container) {
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            
            let html = `<h3>${testCase.name}</h3>`;
            html += `<div class="input"><strong>输入:</strong><br><pre>${testCase.input}</pre></div>`;

            try {
                if (testCase.type === 'handlebars' || testCase.type === 'both') {
                    const hbsResult = await window.wasmConverter.convertHandlebarsToSline(testCase.input);
                    html += `<div class="output"><strong>Handlebars 转换:</strong><br>`;
                    html += `<pre>成功: ${hbsResult.success}</pre>`;
                    if (hbsResult.success) {
                        html += `<pre>输出: ${hbsResult.data.converted}</pre>`;
                        if (hbsResult.data.stats) {
                            html += `<pre>统计: ${JSON.stringify(hbsResult.data.stats, null, 2)}</pre>`;
                        }
                    } else {
                        html += `<pre>错误: ${hbsResult.error}</pre>`;
                    }
                    html += `</div>`;
                }

                if (testCase.type === 'liquid' || testCase.type === 'both') {
                    const liquidResult = await window.wasmConverter.convertLiquidToSline(testCase.input);
                    html += `<div class="output"><strong>Liquid 转换:</strong><br>`;
                    html += `<pre>成功: ${liquidResult.success}</pre>`;
                    if (liquidResult.success) {
                        html += `<pre>输出: ${liquidResult.data.converted}</pre>`;
                        if (liquidResult.data.stats) {
                            html += `<pre>统计: ${JSON.stringify(liquidResult.data.stats, null, 2)}</pre>`;
                        }
                    } else {
                        html += `<pre>错误: ${liquidResult.error}</pre>`;
                    }
                    html += `</div>`;
                }

                // 分析结果
                html += analyzeConversion(testCase);

            } catch (error) {
                html += `<div class="error"><strong>异常:</strong><br><pre>${error.message}</pre></div>`;
            }

            testDiv.innerHTML = html;
            container.appendChild(testDiv);
        }

        function analyzeConversion(testCase) {
            let analysis = '<div class="details"><strong>分析:</strong><br>';
            
            if (testCase.input === '') {
                analysis += '空字符串应该返回空字符串<br>';
            } else if (!testCase.input.includes('{{') && !testCase.input.includes('{%')) {
                analysis += '纯文本应该保持不变<br>';
            } else if (testCase.input.includes('{%')) {
                analysis += 'Liquid 语法应该被转换为 Sline 格式<br>';
                analysis += '- {% if %} 应该变成 {{#if}}<br>';
                analysis += '- {% for %} 应该变成 {{#for}}<br>';
                analysis += '- {% endif %} 应该变成 {{/if}}<br>';
                analysis += '- {% endfor %} 应该变成 {{/for}}<br>';
            } else if (testCase.input.includes('{{')) {
                analysis += 'Handlebars 语法应该保持或优化<br>';
                analysis += '- 去除多余空格<br>';
                analysis += '- 保持基本结构<br>';
            }
            
            analysis += '</div>';
            return analysis;
        }

        async function testSpecificCases() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-case">正在测试特定问题用例...</div>';

            try {
                const success = await window.wasmConverter.initialize();
                if (!success) {
                    throw new Error('初始化失败');
                }

                // 专门测试失败的用例
                const problemCases = [
                    '{% if condition %}Yes{% endif %}',
                    '{% for item in items %}{{item}}{% endfor %}',
                    '{{ product.title }}',
                    'Hello World'
                ];

                for (const input of problemCases) {
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    
                    const liquidResult = await window.wasmConverter.convertLiquidToSline(input);
                    
                    let html = `<h3>问题用例: ${input}</h3>`;
                    html += `<div class="input">输入: <pre>${input}</pre></div>`;
                    html += `<div class="output">输出: <pre>${liquidResult.success ? liquidResult.data.converted : liquidResult.error}</pre></div>`;
                    
                    // 检查是否真的转换了
                    if (liquidResult.success) {
                        const converted = liquidResult.data.converted;
                        if (input === converted) {
                            html += `<div class="warning">⚠️ 没有转换 - 输入和输出相同</div>`;
                        } else {
                            html += `<div class="success">✅ 已转换 - 输入和输出不同</div>`;
                        }
                        
                        // 检查 Liquid 语法是否被转换
                        if (input.includes('{%') && converted.includes('{%')) {
                            html += `<div class="error">❌ Liquid 语法未转换</div>`;
                        } else if (input.includes('{%') && !converted.includes('{%')) {
                            html += `<div class="success">✅ Liquid 语法已转换</div>`;
                        }
                    }
                    
                    testDiv.innerHTML = html;
                    results.appendChild(testDiv);
                }

            } catch (error) {
                results.innerHTML += `<div class="error">❌ 特定测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动运行
        window.addEventListener('load', () => {
            setTimeout(runDetailedTests, 1000);
        });
    </script>
</body>
</html>
