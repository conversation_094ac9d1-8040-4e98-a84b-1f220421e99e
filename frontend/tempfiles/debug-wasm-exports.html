<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 导出调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 WASM 模块导出调试</h1>
    
    <button onclick="debugWasmExports()">调试 WASM 导出</button>
    <button onclick="testStringFunctions()">测试字符串函数</button>
    <button onclick="clearResults()">清空结果</button>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addSection(title, content) {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(section);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function debugWasmExports() {
            clearResults();
            
            try {
                // 直接加载 WASM 文件
                const wasmResponse = await fetch('./wasm/build/converter.wasm');
                const wasmBytes = await wasmResponse.arrayBuffer();
                
                const imports = {
                    env: {
                        abort: (message, fileName, lineNumber, columnNumber) => {
                            console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
                        }
                    }
                };
                
                const wasmInstance = await WebAssembly.instantiate(wasmBytes, imports);
                const exports = wasmInstance.instance.exports;
                
                // 显示所有导出
                const exportNames = Object.keys(exports);
                addSection('所有导出 (' + exportNames.length + ' 个)', exportNames.join('\n'));
                
                // 分类显示
                const functions = [];
                const memory = [];
                const other = [];
                
                for (const name of exportNames) {
                    const item = exports[name];
                    if (typeof item === 'function') {
                        functions.push(name);
                    } else if (item instanceof WebAssembly.Memory) {
                        memory.push(name + ' (Memory)');
                    } else {
                        other.push(name + ' (' + typeof item + ')');
                    }
                }
                
                addSection('函数导出 (' + functions.length + ' 个)', functions.join('\n'));
                addSection('内存导出 (' + memory.length + ' 个)', memory.join('\n'));
                addSection('其他导出 (' + other.length + ' 个)', other.join('\n'));
                
                // 检查内存
                if (exports.memory) {
                    const memInfo = `
内存大小: ${exports.memory.buffer.byteLength} 字节
页数: ${exports.memory.buffer.byteLength / 65536}
                    `;
                    addSection('内存信息', memInfo);
                }
                
                // 测试基本函数
                try {
                    exports.initializeConverter();
                    addSection('初始化', '✅ initializeConverter() 成功');
                } catch (e) {
                    addSection('初始化', '❌ initializeConverter() 失败: ' + e.message);
                }
                
                try {
                    const version = exports.getVersion();
                    addSection('版本测试', `getVersion() 返回: ${version} (类型: ${typeof version})`);
                } catch (e) {
                    addSection('版本测试', '❌ getVersion() 失败: ' + e.message);
                }
                
                try {
                    const mathResult = exports.add(5, 3);
                    addSection('数学测试', `add(5, 3) 返回: ${mathResult} (类型: ${typeof mathResult})`);
                } catch (e) {
                    addSection('数学测试', '❌ add(5, 3) 失败: ' + e.message);
                }
                
            } catch (error) {
                addSection('错误', error.message + '\n\n' + error.stack);
            }
        }
        
        async function testStringFunctions() {
            try {
                // 加载 WASM
                const wasmResponse = await fetch('./wasm/build/converter.wasm');
                const wasmBytes = await wasmResponse.arrayBuffer();
                
                const imports = {
                    env: {
                        abort: (message, fileName, lineNumber, columnNumber) => {
                            console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
                        }
                    }
                };
                
                const wasmInstance = await WebAssembly.instantiate(wasmBytes, imports);
                const exports = wasmInstance.instance.exports;
                
                // 初始化
                exports.initializeConverter();
                
                // 测试字符串函数
                const testInput = 'Hello {{name}}!';
                
                try {
                    const result = exports.convertHbsToSline(testInput);
                    
                    const info = `
输入: "${testInput}"
返回值: ${result}
返回类型: ${typeof result}
                    `;
                    
                    if (typeof result === 'number') {
                        // 尝试读取内存
                        const memory = new Uint8Array(exports.memory.buffer);
                        
                        // 显示指针周围的内存内容
                        const start = Math.max(0, result - 20);
                        const end = Math.min(memory.length, result + 100);
                        const memorySlice = Array.from(memory.slice(start, end))
                            .map((b, i) => `${(start + i).toString(16).padStart(4, '0')}: ${b.toString(16).padStart(2, '0')} (${b >= 32 && b <= 126 ? String.fromCharCode(b) : '.'})`)
                            .join('\n');
                        
                        addSection('字符串函数测试', info + '\n\n内存内容 (指针周围):\n' + memorySlice);
                    } else {
                        addSection('字符串函数测试', info);
                    }
                    
                } catch (e) {
                    addSection('字符串函数测试', '❌ convertHbsToSline() 失败: ' + e.message);
                }
                
            } catch (error) {
                addSection('字符串测试错误', error.message + '\n\n' + error.stack);
            }
        }
        
        // 页面加载时自动运行
        window.addEventListener('load', () => {
            setTimeout(debugWasmExports, 500);
        });
    </script>
</body>
</html>
