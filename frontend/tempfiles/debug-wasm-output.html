<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 输出调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        .input { background: #f0f8ff; padding: 10px; margin: 5px 0; }
        .output { background: #f0fff0; padding: 10px; margin: 5px 0; }
        .error { background: #fff0f0; padding: 10px; margin: 5px 0; color: red; }
        pre { margin: 0; font-family: 'Courier New', monospace; }
    </style>
</head>
<body>
    <h1>🔍 WASM 转换器输出调试</h1>
    <button onclick="runTests()">运行测试</button>
    <div id="results"></div>

    <script src="wasm-converter.js"></script>
    <script>
        async function runTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>正在初始化 WASM 转换器...</p>';

            try {
                // 初始化转换器
                const success = await window.wasmConverter.initialize();
                if (!success) {
                    throw new Error('WASM 转换器初始化失败');
                }

                results.innerHTML = '<p>✅ WASM 转换器初始化成功</p>';

                // 测试用例
                const testCases = [
                    // Handlebars 测试
                    { type: 'handlebars', input: '{{product.title}}', name: 'Handlebars 基本变量' },
                    { type: 'handlebars', input: '{{#if product.available}}Available{{/if}}', name: 'Handlebars 条件判断' },
                    { type: 'handlebars', input: '{{#each products}}{{title}}{{/each}}', name: 'Handlebars 循环' },
                    { type: 'handlebars', input: '{{product.title | upcase}}', name: 'Handlebars 过滤器' },
                    
                    // Liquid 测试
                    { type: 'liquid', input: '{{ product.title }}', name: 'Liquid 基本变量' },
                    { type: 'liquid', input: '{% if product.available %}Available{% endif %}', name: 'Liquid 条件判断' },
                    { type: 'liquid', input: '{% for product in products %}{{ product.title }}{% endfor %}', name: 'Liquid 循环' },
                    { type: 'liquid', input: '{{ product.title | upcase }}', name: 'Liquid 过滤器' }
                ];

                // 运行测试
                for (const testCase of testCases) {
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    
                    try {
                        let result;
                        if (testCase.type === 'handlebars') {
                            result = await window.wasmConverter.convertHandlebarsToSline(testCase.input);
                        } else {
                            result = await window.wasmConverter.convertLiquidToSline(testCase.input);
                        }

                        testDiv.innerHTML = `
                            <h3>${testCase.name}</h3>
                            <div class="input"><strong>输入:</strong><br><pre>${testCase.input}</pre></div>
                            <div class="output"><strong>输出:</strong><br><pre>${result.success ? result.data.converted : 'ERROR'}</pre></div>
                            ${result.success ? '' : `<div class="error"><strong>错误:</strong><br><pre>${result.error}</pre></div>`}
                            ${result.success && result.data.stats ? `<div><strong>统计:</strong> ${JSON.stringify(result.data.stats)}</div>` : ''}
                        `;
                    } catch (error) {
                        testDiv.innerHTML = `
                            <h3>${testCase.name}</h3>
                            <div class="input"><strong>输入:</strong><br><pre>${testCase.input}</pre></div>
                            <div class="error"><strong>异常:</strong><br><pre>${error.message}</pre></div>
                        `;
                    }
                    
                    results.appendChild(testDiv);
                }

                // 显示规则加载状态
                const rulesDiv = document.createElement('div');
                rulesDiv.className = 'test-case';
                const rules = window.wasmConverter.getRules();
                const moduleInfo = window.wasmConverter.getModuleInfo();
                
                rulesDiv.innerHTML = `
                    <h3>📋 规则加载状态</h3>
                    <div class="output">
                        <strong>规则加载:</strong> ${rules ? '✅ 成功' : '❌ 失败'}<br>
                        <strong>WASM 状态:</strong> ${moduleInfo.available ? '✅ 可用' : '❌ 不可用'}<br>
                        <strong>规则统计:</strong><br>
                        <pre>${JSON.stringify(moduleInfo.rulesCount, null, 2)}</pre>
                    </div>
                `;
                results.appendChild(rulesDiv);

            } catch (error) {
                results.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动运行
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
