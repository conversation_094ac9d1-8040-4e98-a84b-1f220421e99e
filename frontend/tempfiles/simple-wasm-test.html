<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 WASM 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #f0fdf4; border-color: #bbf7d0; }
        .error { background: #fef2f2; border-color: #fecaca; }
        .warning { background: #fffbeb; border-color: #fed7aa; }
        .code { background: #f8fafc; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        button { background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2563eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单 WASM 转换器测试</h1>
        <p>验证 WASM 转换器的基本功能是否正常</p>
        
        <button onclick="runSimpleTests()">🚀 运行测试</button>
        
        <div id="results"></div>
    </div>

    <script src="wasm-converter.js"></script>
    <script>
        async function runSimpleTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-section">正在运行测试...</div>';

            try {
                // 1. 初始化测试
                results.innerHTML += '<div class="test-section"><h3>1. 初始化 WASM 转换器</h3><div>正在初始化...</div></div>';
                
                const success = await window.wasmConverter.initialize();
                
                if (success) {
                    updateLastSection('✅ WASM 转换器初始化成功', 'success');
                } else {
                    updateLastSection('❌ WASM 转换器初始化失败', 'error');
                    return;
                }

                // 2. 规则加载测试
                results.innerHTML += '<div class="test-section"><h3>2. 规则加载状态</h3><div>检查规则加载...</div></div>';
                
                const rules = window.wasmConverter.getRules();
                const moduleInfo = window.wasmConverter.getModuleInfo();
                
                let rulesStatus = '';
                if (rules) {
                    rulesStatus = `✅ 规则加载成功<br>
                        - Handlebars 规则: ${Object.keys(rules.handlebars || {}).length} 个分类<br>
                        - Liquid 规则: ${Object.keys(rules.liquid || {}).length} 个分类<br>
                        - WASM 状态: ${moduleInfo.available ? '可用' : '不可用'}`;
                } else {
                    rulesStatus = '⚠️ 规则未加载，但 WASM 可能仍然工作';
                }
                
                updateLastSection(rulesStatus, rules ? 'success' : 'warning');

                // 3. Handlebars 转换测试
                results.innerHTML += '<div class="test-section"><h3>3. Handlebars 转换测试</h3><div>测试基本转换...</div></div>';
                
                const hbsTests = [
                    { input: 'Hello World', name: '纯文本' },
                    { input: '{{product.title}}', name: '基本变量' },
                    { input: '{{#if condition}}Yes{{/if}}', name: '条件判断' }
                ];
                
                let hbsResults = '';
                for (const test of hbsTests) {
                    try {
                        const result = await window.wasmConverter.convertHandlebarsToSline(test.input);
                        if (result.success) {
                            hbsResults += `✅ ${test.name}: <div class="code">输入: ${test.input}<br>输出: ${result.data.converted}</div>`;
                        } else {
                            hbsResults += `❌ ${test.name}: ${result.error}<br>`;
                        }
                    } catch (error) {
                        hbsResults += `❌ ${test.name}: 异常 - ${error.message}<br>`;
                    }
                }
                
                updateLastSection(hbsResults, 'success');

                // 4. Liquid 转换测试
                results.innerHTML += '<div class="test-section"><h3>4. Liquid 转换测试</h3><div>测试基本转换...</div></div>';
                
                const liquidTests = [
                    { input: 'Hello World', name: '纯文本' },
                    { input: '{{ product.title }}', name: '基本变量' },
                    { input: '{% if condition %}Yes{% endif %}', name: '条件判断' }
                ];
                
                let liquidResults = '';
                for (const test of liquidTests) {
                    try {
                        const result = await window.wasmConverter.convertLiquidToSline(test.input);
                        if (result.success) {
                            liquidResults += `✅ ${test.name}: <div class="code">输入: ${test.input}<br>输出: ${result.data.converted}</div>`;
                        } else {
                            liquidResults += `❌ ${test.name}: ${result.error}<br>`;
                        }
                    } catch (error) {
                        liquidResults += `❌ ${test.name}: 异常 - ${error.message}<br>`;
                    }
                }
                
                updateLastSection(liquidResults, 'success');

                // 5. 性能测试
                results.innerHTML += '<div class="test-section"><h3>5. 性能测试</h3><div>测试转换性能...</div></div>';
                
                const perfTest = '{{#each products}}{{title}} - {{price}}{{/each}}';
                const iterations = 100;
                
                const startTime = performance.now();
                for (let i = 0; i < iterations; i++) {
                    await window.wasmConverter.convertHandlebarsToSline(perfTest);
                }
                const endTime = performance.now();
                
                const avgTime = (endTime - startTime) / iterations;
                const perfResult = `✅ 性能测试完成<br>
                    - 测试次数: ${iterations}<br>
                    - 平均转换时间: ${avgTime.toFixed(2)}ms<br>
                    - 总时间: ${(endTime - startTime).toFixed(2)}ms`;
                
                updateLastSection(perfResult, 'success');

                // 6. 总结
                results.innerHTML += '<div class="test-section success"><h3>🎉 测试完成</h3><div>所有基本功能测试通过！WASM 转换器工作正常。</div></div>';

            } catch (error) {
                results.innerHTML += `<div class="test-section error"><h3>❌ 测试失败</h3><div>错误: ${error.message}</div></div>`;
            }
        }

        function updateLastSection(content, className = '') {
            const sections = document.querySelectorAll('.test-section');
            const lastSection = sections[sections.length - 1];
            if (lastSection) {
                const contentDiv = lastSection.querySelector('div:last-child');
                contentDiv.innerHTML = content;
                if (className) {
                    lastSection.className = `test-section ${className}`;
                }
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runSimpleTests, 1000);
        });
    </script>
</body>
</html>
