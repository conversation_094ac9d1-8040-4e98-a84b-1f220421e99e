<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid WASM 转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        pre {
            background: white;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Liquid WASM 转换测试</h1>
        
        <div class="test-section">
            <h2>📋 系统状态</h2>
            <div id="systemStatus">正在检查系统状态...</div>
        </div>

        <div class="test-section">
            <h2>🔧 WASM 初始化</h2>
            <button onclick="testWasmInit()">初始化 WASM 模块</button>
            <div id="initStatus"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Liquid 转换测试</h2>
            <label for="testInput">输入 Liquid 代码:</label>
            <textarea id="testInput" placeholder="输入要转换的 Liquid 代码...">{% if user.active %}
  <div class="user-profile">
    <h2>{{ user.name }}</h2>
    <p>Email: {{ user.email }}</p>
    {% for tag in user.tags %}
      <span class="tag">{{ tag }}</span>
    {% endfor %}
    {% unless user.verified %}
      <div class="warning">Account not verified</div>
    {% endunless %}
  </div>
{% else %}
  <div class="inactive-user">
    <p>User account is inactive</p>
  </div>
{% endif %}</textarea>
            <br>
            <button onclick="testLiquidConversion()">🚀 WASM 转换</button>
            <button onclick="testApiConversion()">📡 API 转换</button>
            <div id="conversionResult"></div>
        </div>

        <div class="test-section">
            <h2>📊 性能对比</h2>
            <button onclick="runPerformanceComparison()">运行性能对比</button>
            <div id="performanceResult"></div>
        </div>

        <div class="test-section">
            <h2>🔍 引擎切换测试</h2>
            <button onclick="testEngineSwitch()">测试引擎切换</button>
            <div id="engineSwitchResult"></div>
        </div>
    </div>

    <script src="wasm-converter.js"></script>
    <script>
        let wasmConverter = window.wasmConverter;
        let isWasmReady = false;

        // 页面加载时检查系统状态
        window.addEventListener('load', checkSystemStatus);

        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            const checks = [
                { name: 'WebAssembly 支持', test: () => typeof WebAssembly !== 'undefined' },
                { name: 'Fetch API 支持', test: () => typeof fetch !== 'undefined' },
                { name: 'WASM 转换器', test: () => typeof wasmConverter !== 'undefined' }
            ];

            let html = '<div class="stats">';
            let allPassed = true;

            for (const check of checks) {
                const passed = check.test();
                allPassed = allPassed && passed;
                html += `
                    <div class="stat-item">
                        <div class="stat-value" style="color: ${passed ? '#28a745' : '#dc3545'}">
                            ${passed ? '✅' : '❌'}
                        </div>
                        <div class="stat-label">${check.name}</div>
                    </div>
                `;
            }
            html += '</div>';

            if (allPassed) {
                html += '<div class="success">✅ 系统环境检查通过，可以使用 WASM 功能</div>';
            } else {
                html += '<div class="error">❌ 系统环境检查失败，某些功能可能不可用</div>';
            }

            statusDiv.innerHTML = html;
        }

        async function testWasmInit() {
            const statusDiv = document.getElementById('initStatus');
            statusDiv.innerHTML = '<div class="info">正在初始化 WASM 模块...</div>';

            try {
                const startTime = performance.now();
                const success = await wasmConverter.initialize();
                const endTime = performance.now();
                const initTime = (endTime - startTime).toFixed(2);

                if (success) {
                    isWasmReady = true;
                    const moduleInfo = wasmConverter.getModuleInfo();
                    statusDiv.innerHTML = `
                        <div class="success">
                            ✅ WASM 模块初始化成功！
                            <div class="stats">
                                <div class="stat-item">
                                    <div class="stat-value">${initTime}ms</div>
                                    <div class="stat-label">初始化时间</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${moduleInfo.available ? '可用' : '不可用'}</div>
                                    <div class="stat-label">模块状态</div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ WASM 模块初始化失败</div>';
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ 初始化错误: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testLiquidConversion() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('conversionResult');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 请输入要转换的代码</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">正在使用 WASM 进行 Liquid 转换...</div>';

            try {
                if (!isWasmReady) {
                    await testWasmInit();
                }

                const startTime = performance.now();
                const result = await wasmConverter.convertLiquidToSline(input);
                const endTime = performance.now();
                const conversionTime = (endTime - startTime).toFixed(2);

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Liquid WASM 转换成功！
                            <div class="stats">
                                <div class="stat-item">
                                    <div class="stat-value">${conversionTime}ms</div>
                                    <div class="stat-label">转换时间</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${input.length}</div>
                                    <div class="stat-label">输入字符</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${result.data.converted.length}</div>
                                    <div class="stat-label">输出字符</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">WASM</div>
                                    <div class="stat-label">转换引擎</div>
                                </div>
                            </div>
                            <h4>转换结果:</h4>
                            <pre>${result.data.converted}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ WASM 转换失败: ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 转换错误: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testApiConversion() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('conversionResult');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 请输入要转换的代码</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">正在使用 API 进行 Liquid 转换...</div>';

            try {
                const startTime = performance.now();
                const response = await fetch('http://localhost:3000/api/liquid2sline', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ liquid: input })
                });
                const result = await response.json();
                const endTime = performance.now();
                const conversionTime = (endTime - startTime).toFixed(2);

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Liquid API 转换成功！
                            <div class="stats">
                                <div class="stat-item">
                                    <div class="stat-value">${conversionTime}ms</div>
                                    <div class="stat-label">转换时间</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${input.length}</div>
                                    <div class="stat-label">输入字符</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${result.sline.length}</div>
                                    <div class="stat-label">输出字符</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">API</div>
                                    <div class="stat-label">转换引擎</div>
                                </div>
                            </div>
                            <h4>转换结果:</h4>
                            <pre>${result.sline}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ API 转换失败: ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ API 转换错误: ${error.message}
                        <br>提示: 请确保后端服务器在 localhost:3000 运行
                    </div>
                `;
            }
        }

        async function runPerformanceComparison() {
            const resultDiv = document.getElementById('performanceResult');
            const input = document.getElementById('testInput').value || 'Hello {% if name %}{{ name }}{% endif %}!';
            const iterations = 20;

            resultDiv.innerHTML = '<div class="info">正在运行性能对比测试...</div>';

            try {
                if (!isWasmReady) {
                    await testWasmInit();
                }

                // WASM 性能测试
                const wasmTimes = [];
                for (let i = 0; i < iterations; i++) {
                    const start = performance.now();
                    await wasmConverter.convertLiquidToSline(input);
                    const end = performance.now();
                    wasmTimes.push(end - start);
                }

                const wasmAvg = wasmTimes.reduce((a, b) => a + b, 0) / wasmTimes.length;

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Liquid 性能对比测试完成 (${iterations} 次迭代)
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-value">${wasmAvg.toFixed(2)}ms</div>
                                <div class="stat-label">WASM 平均时间</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${Math.min(...wasmTimes).toFixed(2)}ms</div>
                                <div class="stat-label">WASM 最快时间</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${(1000 / wasmAvg).toFixed(0)}</div>
                                <div class="stat-label">转换/秒</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">Liquid</div>
                                <div class="stat-label">模板引擎</div>
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 性能测试错误: ${error.message}
                    </div>
                `;
            }
        }

        async function testEngineSwitch() {
            const resultDiv = document.getElementById('engineSwitchResult');
            
            try {
                if (!isWasmReady) {
                    await testWasmInit();
                }

                const testInput = 'Hello {% if user %}{{ user.name }}{% endif %}!';
                
                // 测试 WASM 转换
                const wasmResult = await wasmConverter.convertLiquidToSline(testInput);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 引擎切换测试完成
                        <h4>WASM 引擎结果:</h4>
                        <pre>${wasmResult.success ? wasmResult.data.converted : 'Failed: ' + wasmResult.error}</pre>
                        <div class="info">
                            💡 在主页面中，您可以点击输出面板中的引擎按钮来切换 WASM 和 API 引擎
                        </div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 引擎切换测试错误: ${error.message}
                    </div>
                `;
            }
        }

        // 页面加载时自动运行基本测试
        window.addEventListener('load', () => {
            setTimeout(testWasmInit, 1000);
        });
    </script>
</body>
</html>
