<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .metric {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ 性能测试</h1>
        
        <div class="test-section">
            <h3>1. WASM 加载性能</h3>
            <button onclick="testWasmLoadPerformance()">测试 WASM 加载</button>
            <div id="wasm-load-performance-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 转换性能测试</h3>
            <button onclick="testConversionPerformance()">测试转换性能</button>
            <div id="conversion-performance-result"></div>
            <div class="progress-bar">
                <div id="conversion-progress" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 批量转换测试</h3>
            <button onclick="testBatchConversion()">测试批量转换</button>
            <div id="batch-conversion-result"></div>
            <div class="progress-bar">
                <div id="batch-progress" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 内存使用测试</h3>
            <button onclick="testMemoryUsage()">测试内存使用</button>
            <div id="memory-usage-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 性能基准测试</h3>
            <button onclick="runBenchmark()">运行基准测试</button>
            <div id="benchmark-result"></div>
        </div>
    </div>

    <script>
        let wasmModule = null;

        // 测试 WASM 加载性能
        async function testWasmLoadPerformance() {
            const resultDiv = document.getElementById('wasm-load-performance-result');
            resultDiv.innerHTML = '<div class="info">正在测试 WASM 加载性能...</div>';

            try {
                const iterations = 5;
                const loadTimes = [];

                for (let i = 0; i < iterations; i++) {
                    const startTime = performance.now();
                    
                    // 重新加载 WASM 模块
                    const module = await import('./wasm/build/converter.js?' + Date.now());
                    
                    const endTime = performance.now();
                    loadTimes.push(endTime - startTime);
                    
                    if (i === 0) wasmModule = module; // 保存第一次加载的模块
                }

                const avgLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
                const minLoadTime = Math.min(...loadTimes);
                const maxLoadTime = Math.max(...loadTimes);

                resultDiv.innerHTML = `
                    <div class="success">✅ WASM 加载性能测试完成</div>
                    <div class="metric">平均加载时间: ${avgLoadTime.toFixed(2)} ms</div>
                    <div class="metric">最快加载时间: ${minLoadTime.toFixed(2)} ms</div>
                    <div class="metric">最慢加载时间: ${maxLoadTime.toFixed(2)} ms</div>
                    <div class="info">测试次数: ${iterations}</div>
                `;

                if (avgLoadTime > 1000) {
                    resultDiv.innerHTML += '<div class="warning">⚠️ 加载时间较长，可能需要优化</div>';
                } else if (avgLoadTime < 100) {
                    resultDiv.innerHTML += '<div class="success">🚀 加载速度优秀！</div>';
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ WASM 加载性能测试失败: ${error.message}</div>`;
            }
        }

        // 测试转换性能
        async function testConversionPerformance() {
            const resultDiv = document.getElementById('conversion-performance-result');
            const progressBar = document.getElementById('conversion-progress');
            
            if (!wasmModule) {
                wasmModule = await import('./wasm/build/converter.js');
            }

            resultDiv.innerHTML = '<div class="info">正在测试转换性能...</div>';

            try {
                const testCases = [
                    { name: 'Simple Variable', input: '{{user.name}}', type: 'handlebars' },
                    { name: 'Conditional', input: '{{#if user.active}}Active{{/if}}', type: 'handlebars' },
                    { name: 'Loop', input: '{{#each products}}{{this.name}}{{/each}}', type: 'handlebars' },
                    { name: 'Liquid Variable', input: '{{ user.name }}', type: 'liquid' },
                    { name: 'Liquid Conditional', input: '{% if user.active %}Active{% endif %}', type: 'liquid' },
                    { name: 'Liquid Loop', input: '{% for product in products %}{{ product.name }}{% endfor %}', type: 'liquid' }
                ];

                const iterations = 1000;
                const results = [];

                for (let i = 0; i < testCases.length; i++) {
                    const testCase = testCases[i];
                    const times = [];

                    for (let j = 0; j < iterations; j++) {
                        const startTime = performance.now();
                        
                        if (testCase.type === 'handlebars') {
                            wasmModule.convertHbsToSline(testCase.input);
                        } else {
                            wasmModule.convertLiquidToSline(testCase.input);
                        }
                        
                        const endTime = performance.now();
                        times.push(endTime - startTime);
                    }

                    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                    const minTime = Math.min(...times);
                    const maxTime = Math.max(...times);
                    const throughput = 1000 / avgTime;

                    results.push({
                        name: testCase.name,
                        avgTime,
                        minTime,
                        maxTime,
                        throughput
                    });

                    // 更新进度条
                    const progress = ((i + 1) / testCases.length) * 100;
                    progressBar.style.width = progress + '%';
                }

                let resultHtml = '<div class="success">✅ 转换性能测试完成</div>';
                
                for (const result of results) {
                    resultHtml += `
                        <div class="test-result info">
                            <strong>${result.name}</strong><br>
                            平均时间: ${result.avgTime.toFixed(4)} ms<br>
                            吞吐量: ${result.throughput.toFixed(0)} ops/sec<br>
                            范围: ${result.minTime.toFixed(4)} - ${result.maxTime.toFixed(4)} ms
                        </div>
                    `;
                }

                const totalAvgTime = results.reduce((sum, r) => sum + r.avgTime, 0) / results.length;
                const totalThroughput = results.reduce((sum, r) => sum + r.throughput, 0);

                resultHtml += `
                    <div class="metric">总体平均时间: ${totalAvgTime.toFixed(4)} ms</div>
                    <div class="metric">总体吞吐量: ${totalThroughput.toFixed(0)} ops/sec</div>
                `;

                resultDiv.innerHTML = resultHtml;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 转换性能测试失败: ${error.message}</div>`;
            }
        }

        // 测试批量转换
        async function testBatchConversion() {
            const resultDiv = document.getElementById('batch-conversion-result');
            const progressBar = document.getElementById('batch-progress');
            
            if (!wasmModule) {
                wasmModule = await import('./wasm/build/converter.js');
            }

            resultDiv.innerHTML = '<div class="info">正在测试批量转换...</div>';

            try {
                const batchSize = 100;
                const templates = [
                    '{{user.name}}',
                    '{{#if user.active}}{{user.email}}{{/if}}',
                    '{{#each products}}{{this.title}} - {{this.price}}{{/each}}',
                    '{{ user.name | upcase }}',
                    '{% if user.active %}{{ user.email }}{% endif %}',
                    '{% for product in products %}{{ product.title }} - {{ product.price }}{% endfor %}'
                ];

                const startTime = performance.now();
                
                for (let i = 0; i < batchSize; i++) {
                    const template = templates[i % templates.length];
                    
                    if (i % 2 === 0) {
                        wasmModule.convertHbsToSline(template);
                    } else {
                        wasmModule.convertLiquidToSline(template);
                    }

                    // 更新进度条
                    const progress = ((i + 1) / batchSize) * 100;
                    progressBar.style.width = progress + '%';
                    
                    // 让浏览器有机会更新 UI
                    if (i % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 0));
                    }
                }

                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const avgTimePerConversion = totalTime / batchSize;
                const conversionsPerSecond = 1000 / avgTimePerConversion;

                resultDiv.innerHTML = `
                    <div class="success">✅ 批量转换测试完成</div>
                    <div class="metric">批量大小: ${batchSize}</div>
                    <div class="metric">总时间: ${totalTime.toFixed(2)} ms</div>
                    <div class="metric">平均时间: ${avgTimePerConversion.toFixed(4)} ms/conversion</div>
                    <div class="metric">转换速度: ${conversionsPerSecond.toFixed(0)} conversions/sec</div>
                `;

                if (conversionsPerSecond > 1000) {
                    resultDiv.innerHTML += '<div class="success">🚀 批量转换性能优秀！</div>';
                } else if (conversionsPerSecond < 100) {
                    resultDiv.innerHTML += '<div class="warning">⚠️ 批量转换性能可能需要优化</div>';
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 批量转换测试失败: ${error.message}</div>`;
            }
        }

        // 测试内存使用
        async function testMemoryUsage() {
            const resultDiv = document.getElementById('memory-usage-result');
            resultDiv.innerHTML = '<div class="info">正在测试内存使用...</div>';

            try {
                if (!wasmModule) {
                    wasmModule = await import('./wasm/build/converter.js');
                }

                // 获取初始内存使用
                const initialMemory = performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null;

                // 执行大量转换操作
                const iterations = 1000;
                const template = '{{#each products}}{{this.title}} - {{this.price}}{{/each}}';
                
                for (let i = 0; i < iterations; i++) {
                    wasmModule.convertHbsToSline(template);
                }

                // 强制垃圾回收（如果可用）
                if (window.gc) {
                    window.gc();
                }

                // 获取最终内存使用
                const finalMemory = performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null;

                let resultHtml = '<div class="success">✅ 内存使用测试完成</div>';

                if (initialMemory && finalMemory) {
                    const memoryIncrease = finalMemory.used - initialMemory.used;
                    const memoryIncreasePerOp = memoryIncrease / iterations;

                    resultHtml += `
                        <div class="metric">初始内存: ${(initialMemory.used / 1024 / 1024).toFixed(2)} MB</div>
                        <div class="metric">最终内存: ${(finalMemory.used / 1024 / 1024).toFixed(2)} MB</div>
                        <div class="metric">内存增长: ${(memoryIncrease / 1024).toFixed(2)} KB</div>
                        <div class="metric">每次操作: ${memoryIncreasePerOp.toFixed(2)} bytes</div>
                        <div class="metric">内存限制: ${(finalMemory.limit / 1024 / 1024).toFixed(0)} MB</div>
                    `;

                    if (memoryIncreasePerOp < 100) {
                        resultHtml += '<div class="success">🚀 内存使用效率优秀！</div>';
                    } else if (memoryIncreasePerOp > 1000) {
                        resultHtml += '<div class="warning">⚠️ 可能存在内存泄漏</div>';
                    }
                } else {
                    resultHtml += '<div class="info">ℹ️ 浏览器不支持详细内存监控</div>';
                }

                resultDiv.innerHTML = resultHtml;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 内存使用测试失败: ${error.message}</div>`;
            }
        }

        // 运行基准测试
        async function runBenchmark() {
            const resultDiv = document.getElementById('benchmark-result');
            resultDiv.innerHTML = '<div class="info">正在运行基准测试...</div>';

            try {
                if (!wasmModule) {
                    wasmModule = await import('./wasm/build/converter.js');
                }

                const benchmarks = [
                    {
                        name: '简单变量转换',
                        template: '{{user.name}}',
                        type: 'handlebars',
                        iterations: 10000
                    },
                    {
                        name: '复杂模板转换',
                        template: '{{#if user.active}}{{#each products}}{{this.title}} - {{this.price}}{{/each}}{{/if}}',
                        type: 'handlebars',
                        iterations: 1000
                    },
                    {
                        name: 'Liquid 变量转换',
                        template: '{{ user.name | upcase }}',
                        type: 'liquid',
                        iterations: 10000
                    },
                    {
                        name: 'Liquid 复杂模板',
                        template: '{% if user.active %}{% for product in products %}{{ product.title }} - {{ product.price }}{% endfor %}{% endif %}',
                        type: 'liquid',
                        iterations: 1000
                    }
                ];

                let resultHtml = '<div class="success">✅ 基准测试完成</div>';

                for (const benchmark of benchmarks) {
                    const startTime = performance.now();
                    
                    for (let i = 0; i < benchmark.iterations; i++) {
                        if (benchmark.type === 'handlebars') {
                            wasmModule.convertHbsToSline(benchmark.template);
                        } else {
                            wasmModule.convertLiquidToSline(benchmark.template);
                        }
                    }
                    
                    const endTime = performance.now();
                    const totalTime = endTime - startTime;
                    const avgTime = totalTime / benchmark.iterations;
                    const opsPerSecond = 1000 / avgTime;

                    resultHtml += `
                        <div class="test-result info">
                            <strong>${benchmark.name}</strong><br>
                            迭代次数: ${benchmark.iterations}<br>
                            总时间: ${totalTime.toFixed(2)} ms<br>
                            平均时间: ${avgTime.toFixed(4)} ms<br>
                            性能: ${opsPerSecond.toFixed(0)} ops/sec
                        </div>
                    `;
                }

                resultDiv.innerHTML = resultHtml;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 基准测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动测试 WASM 加载性能
        window.addEventListener('load', () => {
            setTimeout(testWasmLoadPerformance, 500);
        });
    </script>
</body>
</html>
