/**
 * 测试 rules.html 页面功能
 * 验证规则数据加载和显示功能
 */

// 模拟 DOM 环境
const mockDOM = {
    getElementById: (id) => {
        const elements = {
            'rulesContent': {
                innerHTML: '',
                textContent: ''
            },
            'statusText': {
                textContent: ''
            },
            'category-content': {
                innerHTML: ''
            }
        };
        return elements[id] || null;
    },
    
    querySelectorAll: (selector) => {
        if (selector === '.tab-button') {
            return [
                { classList: { remove: () => {}, add: () => {} } },
                { classList: { remove: () => {}, add: () => {} } }
            ];
        }
        return [];
    },
    
    querySelector: (selector) => {
        return { classList: { add: () => {} } };
    }
};

// 模拟 fetch API
const mockFetch = (url) => {
    const responses = {
        './rules/index.json': {
            version: "1.0.0",
            handlebars: {
                categories: ["conditional", "filters", "objects"],
                total: 3
            }
        },
        './rules/handlebars/conditional.json': {
            data: {
                CONDITIONAL_TAG_RULES: [
                    {
                        name: "if 标签转换",
                        description: "转换 if 标签",
                        examples: {
                            handlebars: "{{#if condition}}",
                            sline: "{{#if condition}}"
                        }
                    }
                ]
            }
        },
        './rules/handlebars/filters.json': {
            data: {
                FILTER_MAPPINGS: {
                    "capitalize": "capitalize",
                    "downcase": "downcase"
                }
            }
        },
        './rules/handlebars/objects.json': {
            data: {
                OBJECT_MAPPINGS: {
                    "shop": "shop",
                    "product": "product"
                }
            }
        }
    };
    
    return Promise.resolve({
        json: () => Promise.resolve(responses[url] || {})
    });
};

// 测试 RulesApp 类
class TestRulesApp {
    constructor() {
        this.rulesData = null;
        this.rulesBasePath = './rules';
        
        // 模拟全局对象
        global.document = mockDOM;
        global.fetch = mockFetch;
    }
    
    getRulesBasePath() {
        return './rules';
    }
    
    async loadRulesSection() {
        const content = mockDOM.getElementById('rulesContent');
        
        try {
            // 加载规则索引
            const indexResponse = await fetch(`${this.rulesBasePath}/index.json`);
            const indexData = await indexResponse.json();
            
            // 加载所有 handlebars 规则分类
            const categories = {};
            const stats = {
                totalRules: 0,
                mappings: {
                    filters: 0,
                    objects: 0
                }
            };
            
            for (const categoryName of indexData.handlebars.categories) {
                try {
                    const categoryResponse = await fetch(`${this.rulesBasePath}/handlebars/${categoryName}.json`);
                    const categoryData = await categoryResponse.json();
                    
                    // 处理不同的数据结构
                    let rules = [];
                    if (categoryData.data) {
                        // 处理包含规则数组的数据
                        const dataKeys = Object.keys(categoryData.data);
                        for (const key of dataKeys) {
                            if (Array.isArray(categoryData.data[key])) {
                                rules = rules.concat(categoryData.data[key]);
                            }
                        }
                    }
                    
                    // 特殊处理 filters 和 objects 分类
                    if (categoryName === 'filters' && categoryData.data?.FILTER_MAPPINGS) {
                        // FILTER_MAPPINGS 是对象，转换为数组
                        const filterMappings = Object.keys(categoryData.data.FILTER_MAPPINGS);
                        rules = [{
                            name: '过滤器映射',
                            description: 'Handlebars 到 Sline 过滤器映射表',
                            type: 'filter-mapping',
                            filters: filterMappings
                        }];
                        stats.mappings.filters = filterMappings.length;
                    } else if (categoryName === 'objects' && categoryData.data?.OBJECT_MAPPINGS) {
                        // 处理对象映射（如果是对象则转换为数组）
                        let objectMappings = categoryData.data.OBJECT_MAPPINGS;
                        if (typeof objectMappings === 'object' && !Array.isArray(objectMappings)) {
                            objectMappings = Object.keys(objectMappings);
                        }
                        rules = [{
                            name: '对象映射',
                            description: 'Handlebars 到 Sline 对象映射表',
                            type: 'object-mapping',
                            objects: objectMappings
                        }];
                        stats.mappings.objects = Array.isArray(objectMappings) ? objectMappings.length : Object.keys(objectMappings).length;
                    }
                    
                    categories[categoryName] = rules;
                    stats.totalRules += rules.length;
                } catch (error) {
                    console.warn(`Failed to load category ${categoryName}:`, error);
                    categories[categoryName] = [];
                }
            }
            
            // 构建结果数据
            const result = {
                success: true,
                data: {
                    stats: stats,
                    categories: categories
                }
            };
            
            // 保存规则数据供标签切换使用
            this.rulesData = result.data;
            
            return result;
            
        } catch (error) {
            console.error('Error loading rules:', error);
            throw error;
        }
    }
    
    updateStatus(message) {
        const statusElement = mockDOM.getElementById('statusText');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }
}

// 运行测试
async function runTests() {
    console.log('🧪 开始测试 rules.html 页面功能...');
    
    try {
        const app = new TestRulesApp();
        
        // 测试 1: 加载规则数据
        console.log('📋 测试 1: 加载规则数据');
        const result = await app.loadRulesSection();
        
        console.log('✅ 规则数据加载成功');
        console.log(`📊 统计信息:`, result.data.stats);
        console.log(`📂 分类数量: ${Object.keys(result.data.categories).length}`);
        
        // 测试 2: 验证数据结构
        console.log('📋 测试 2: 验证数据结构');
        const { stats, categories } = result.data;
        
        // 验证统计数据
        if (stats.totalRules > 0) {
            console.log('✅ 总规则数正确');
        } else {
            throw new Error('总规则数为0');
        }
        
        if (stats.mappings.filters > 0) {
            console.log('✅ 过滤器映射数正确');
        } else {
            throw new Error('过滤器映射数为0');
        }
        
        if (stats.mappings.objects > 0) {
            console.log('✅ 对象映射数正确');
        } else {
            throw new Error('对象映射数为0');
        }
        
        // 验证分类数据
        if (categories.conditional && categories.conditional.length > 0) {
            console.log('✅ 条件标签规则加载正确');
        } else {
            throw new Error('条件标签规则加载失败');
        }
        
        if (categories.filters && categories.filters[0].type === 'filter-mapping') {
            console.log('✅ 过滤器映射结构正确');
        } else {
            throw new Error('过滤器映射结构错误');
        }
        
        if (categories.objects && categories.objects[0].type === 'object-mapping') {
            console.log('✅ 对象映射结构正确');
        } else {
            throw new Error('对象映射结构错误');
        }
        
        console.log('🎉 所有测试通过！');
        return true;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    }
}

// 如果在 Node.js 环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TestRulesApp, runTests };
    
    // 直接运行测试
    if (require.main === module) {
        runTests().then(success => {
            process.exit(success ? 0 : 1);
        });
    }
}
