<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则系统验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .rule-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .rule-category {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 规则系统验证</h1>
        
        <div class="test-section">
            <h3>1. 规则文件检查</h3>
            <button onclick="checkRuleFiles()">检查规则文件</button>
            <div id="rule-files-result"></div>
        </div>

        <div class="test-section">
            <h3>2. Handlebars 规则验证</h3>
            <button onclick="validateHandlebarsRules()">验证 Handlebars 规则</button>
            <div id="handlebars-rules-result"></div>
        </div>

        <div class="test-section">
            <h3>3. Liquid 规则验证</h3>
            <button onclick="validateLiquidRules()">验证 Liquid 规则</button>
            <div id="liquid-rules-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 规则完整性测试</h3>
            <button onclick="testRuleIntegrity()">测试规则完整性</button>
            <div id="rule-integrity-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 转换测试（使用规则）</h3>
            <button onclick="testConversionWithRules()">测试转换功能</button>
            <div id="conversion-test-result"></div>
        </div>
    </div>

    <script>
        // 检查规则文件
        async function checkRuleFiles() {
            const resultDiv = document.getElementById('rule-files-result');
            resultDiv.innerHTML = '<div class="info">正在检查规则文件...</div>';

            try {
                // 检查规则索引文件
                const indexResponse = await fetch('./rules/index.json');
                if (!indexResponse.ok) {
                    throw new Error(`规则索引文件不存在: ${indexResponse.status}`);
                }
                const index = await indexResponse.json();

                let result = `<div class="success">✅ 规则索引文件存在</div>`;
                result += `<div class="info">Handlebars 规则分类: ${index.handlebars.total} 个</div>`;
                result += `<div class="info">Liquid 规则分类: ${index.liquid.total} 个</div>`;

                // 检查具体规则文件
                const handlebarsFiles = index.handlebars.categories;
                const liquidFiles = index.liquid.categories;

                let handlebarsCount = 0;
                let liquidCount = 0;

                for (const category of handlebarsFiles) {
                    try {
                        const response = await fetch(`./rules/handlebars/${category}.json`);
                        if (response.ok) handlebarsCount++;
                    } catch (e) {}
                }

                for (const category of liquidFiles) {
                    try {
                        const response = await fetch(`./rules/liquid/${category}.json`);
                        if (response.ok) liquidCount++;
                    } catch (e) {}
                }

                result += `<div class="info">Handlebars 规则文件: ${handlebarsCount}/${handlebarsFiles.length} 可用</div>`;
                result += `<div class="info">Liquid 规则文件: ${liquidCount}/${liquidFiles.length} 可用</div>`;

                if (handlebarsCount === handlebarsFiles.length && liquidCount === liquidFiles.length) {
                    result += `<div class="success">✅ 所有规则文件都可访问</div>`;
                } else {
                    result += `<div class="error">❌ 部分规则文件不可访问</div>`;
                }

                resultDiv.innerHTML = result;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 规则文件检查失败: ${error.message}</div>`;
            }
        }

        // 验证 Handlebars 规则
        async function validateHandlebarsRules() {
            const resultDiv = document.getElementById('handlebars-rules-result');
            resultDiv.innerHTML = '<div class="info">正在验证 Handlebars 规则...</div>';

            try {
                const indexResponse = await fetch('./rules/index.json');
                const index = await indexResponse.json();
                
                let result = '';
                let totalRules = 0;

                for (const category of index.handlebars.categories) {
                    try {
                        const response = await fetch(`./rules/handlebars/${category}.json`);
                        if (response.ok) {
                            const ruleData = await response.json();
                            const ruleCount = Object.keys(ruleData.data || {}).length;
                            totalRules += ruleCount;
                            
                            result += `<div class="rule-item">`;
                            result += `<div class="rule-category">${category}</div>`;
                            result += `<div>规则数量: ${ruleCount}</div>`;
                            result += `<div>版本: ${ruleData.version || 'N/A'}</div>`;
                            result += `<div>更新时间: ${ruleData.lastUpdated || 'N/A'}</div>`;
                            result += `</div>`;
                        }
                    } catch (e) {
                        result += `<div class="error">❌ ${category}: ${e.message}</div>`;
                    }
                }

                result = `<div class="success">✅ Handlebars 规则验证完成</div>
                         <div class="info">总规则数: ${totalRules}</div>` + result;

                resultDiv.innerHTML = result;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Handlebars 规则验证失败: ${error.message}</div>`;
            }
        }

        // 验证 Liquid 规则
        async function validateLiquidRules() {
            const resultDiv = document.getElementById('liquid-rules-result');
            resultDiv.innerHTML = '<div class="info">正在验证 Liquid 规则...</div>';

            try {
                const indexResponse = await fetch('./rules/index.json');
                const index = await indexResponse.json();
                
                let result = '';
                let totalRules = 0;

                for (const category of index.liquid.categories) {
                    try {
                        const response = await fetch(`./rules/liquid/${category}.json`);
                        if (response.ok) {
                            const ruleData = await response.json();
                            const ruleCount = Object.keys(ruleData.data || {}).length;
                            totalRules += ruleCount;
                            
                            result += `<div class="rule-item">`;
                            result += `<div class="rule-category">${category}</div>`;
                            result += `<div>规则数量: ${ruleCount}</div>`;
                            result += `<div>版本: ${ruleData.version || 'N/A'}</div>`;
                            result += `<div>更新时间: ${ruleData.lastUpdated || 'N/A'}</div>`;
                            result += `</div>`;
                        }
                    } catch (e) {
                        result += `<div class="error">❌ ${category}: ${e.message}</div>`;
                    }
                }

                result = `<div class="success">✅ Liquid 规则验证完成</div>
                         <div class="info">总规则数: ${totalRules}</div>` + result;

                resultDiv.innerHTML = result;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Liquid 规则验证失败: ${error.message}</div>`;
            }
        }

        // 测试规则完整性
        async function testRuleIntegrity() {
            const resultDiv = document.getElementById('rule-integrity-result');
            resultDiv.innerHTML = '<div class="info">正在测试规则完整性...</div>';

            try {
                // 加载 WASM 模块进行统计
                const wasmModule = await import('./wasm/build/converter.js');
                
                const stats = wasmModule.getAllMappingStats();
                const statsObj = JSON.parse(stats);

                let result = `<div class="success">✅ 规则完整性测试完成</div>`;
                result += `<div class="info">WASM 内置规则统计:</div>`;
                result += `<div class="info">对象映射 - Handlebars: ${statsObj.objects.handlebars}, Liquid: ${statsObj.objects.liquid}</div>`;
                result += `<div class="info">过滤器映射 - Handlebars: ${statsObj.filters.handlebars}, Liquid: ${statsObj.filters.liquid}</div>`;
                result += `<div class="info">条件规则: ${statsObj.conditional_rules.handlebars + statsObj.conditional_rules.liquid}</div>`;
                result += `<div class="info">循环规则: ${statsObj.iteration_rules.handlebars + statsObj.iteration_rules.liquid}</div>`;

                resultDiv.innerHTML = result;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 规则完整性测试失败: ${error.message}</div>`;
            }
        }

        // 测试转换功能
        async function testConversionWithRules() {
            const resultDiv = document.getElementById('conversion-test-result');
            resultDiv.innerHTML = '<div class="info">正在测试转换功能...</div>';

            try {
                const wasmModule = await import('./wasm/build/converter.js');

                const testCases = [
                    {
                        name: 'Handlebars 变量',
                        input: '{{user.name}}',
                        type: 'handlebars'
                    },
                    {
                        name: 'Handlebars 条件',
                        input: '{{#if user.active}}Active{{/if}}',
                        type: 'handlebars'
                    },
                    {
                        name: 'Handlebars 循环',
                        input: '{{#each products}}{{this.name}}{{/each}}',
                        type: 'handlebars'
                    },
                    {
                        name: 'Liquid 变量',
                        input: '{{ user.name }}',
                        type: 'liquid'
                    },
                    {
                        name: 'Liquid 条件',
                        input: '{% if user.active %}Active{% endif %}',
                        type: 'liquid'
                    },
                    {
                        name: 'Liquid 循环',
                        input: '{% for product in products %}{{ product.name }}{% endfor %}',
                        type: 'liquid'
                    }
                ];

                let result = `<div class="success">✅ 转换功能测试完成</div>`;

                for (const testCase of testCases) {
                    try {
                        let output;
                        if (testCase.type === 'handlebars') {
                            output = wasmModule.convertHbsToSline(testCase.input);
                        } else {
                            output = wasmModule.convertLiquidToSline(testCase.input);
                        }

                        result += `<div class="rule-item">`;
                        result += `<div class="rule-category">${testCase.name}</div>`;
                        result += `<div>输入: ${testCase.input}</div>`;
                        result += `<div>输出: ${output}</div>`;
                        result += `</div>`;
                    } catch (e) {
                        result += `<div class="error">❌ ${testCase.name}: ${e.message}</div>`;
                    }
                }

                resultDiv.innerHTML = result;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 转换功能测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查规则文件
        window.addEventListener('load', () => {
            setTimeout(checkRuleFiles, 500);
        });
    </script>
</body>
</html>
