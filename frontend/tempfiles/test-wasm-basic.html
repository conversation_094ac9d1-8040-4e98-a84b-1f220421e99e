<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 基础功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WASM 基础功能测试</h1>
        
        <div class="test-section">
            <h3>1. WASM 模块加载测试</h3>
            <button onclick="testWasmLoad()">测试 WASM 加载</button>
            <div id="wasm-load-result"></div>
        </div>

        <div class="test-section">
            <h3>2. WASM 函数调用测试</h3>
            <button onclick="testWasmFunctions()">测试 WASM 函数</button>
            <div id="wasm-functions-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 基础转换测试（不依赖规则）</h3>
            <textarea id="test-input" placeholder="输入测试模板...">{{user.name}}</textarea>
            <button onclick="testBasicConversion()">测试基础转换</button>
            <div id="basic-conversion-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 内存操作测试</h3>
            <button onclick="testMemoryOperations()">测试内存操作</button>
            <div id="memory-operations-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 性能测试</h3>
            <button onclick="testPerformance()">测试性能</button>
            <div id="performance-result"></div>
        </div>
    </div>

    <script>
        let wasmModule = null;
        let wasmInstance = null;

        // 测试 WASM 加载
        async function testWasmLoad() {
            const resultDiv = document.getElementById('wasm-load-result');
            resultDiv.innerHTML = '<div class="info">正在加载 WASM 模块...</div>';

            try {
                // 直接导入 AssemblyScript 生成的模块
                wasmInstance = await import('./wasm/build/converter.js');

                resultDiv.innerHTML = `
                    <div class="success">✅ WASM 模块加载成功</div>
                    <div class="info">导出函数数量: ${Object.keys(wasmInstance).length}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ WASM 加载失败: ${error.message}</div>`;
            }
        }

        // 测试 WASM 函数
        async function testWasmFunctions() {
            const resultDiv = document.getElementById('wasm-functions-result');

            if (!wasmInstance) {
                resultDiv.innerHTML = '<div class="error">❌ 请先加载 WASM 模块</div>';
                return;
            }

            try {
                const functions = Object.keys(wasmInstance).filter(key => typeof wasmInstance[key] === 'function');

                resultDiv.innerHTML = `
                    <div class="success">✅ 发现 ${functions.length} 个导出函数</div>
                    <div class="info">函数列表: ${functions.slice(0, 10).join(', ')}${functions.length > 10 ? '...' : ''}</div>
                `;

                // 测试转换函数
                if (wasmInstance.convertHbsToSline) {
                    resultDiv.innerHTML += `<div class="success">✅ Handlebars 转换函数可用</div>`;
                }

                if (wasmInstance.convertLiquidToSline) {
                    resultDiv.innerHTML += `<div class="success">✅ Liquid 转换函数可用</div>`;
                }

                // 测试统计函数
                if (wasmInstance.getAllMappingStats) {
                    const stats = wasmInstance.getAllMappingStats();
                    resultDiv.innerHTML += `<div class="info">映射统计: ${stats}</div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 函数测试失败: ${error.message}</div>`;
            }
        }

        // 测试基础转换
        async function testBasicConversion() {
            const resultDiv = document.getElementById('basic-conversion-result');
            const input = document.getElementById('test-input').value;

            if (!wasmInstance) {
                resultDiv.innerHTML = '<div class="error">❌ 请先加载 WASM 模块</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 正在测试转换...</div>';

                // 测试 Handlebars 转换
                if (wasmInstance.convertHbsToSline) {
                    const hbsResult = wasmInstance.convertHbsToSline(input);
                    resultDiv.innerHTML = `
                        <div class="success">✅ Handlebars 转换成功</div>
                        <div class="info">输入: ${input}</div>
                        <div class="info">输出: ${hbsResult}</div>
                    `;
                }

                // 测试 Liquid 转换
                if (wasmInstance.convertLiquidToSline) {
                    const liquidResult = wasmInstance.convertLiquidToSline(input);
                    resultDiv.innerHTML += `
                        <div class="success">✅ Liquid 转换成功</div>
                        <div class="info">输入: ${input}</div>
                        <div class="info">输出: ${liquidResult}</div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 转换测试失败: ${error.message}</div>`;
            }
        }

        // 测试内存操作
        async function testMemoryOperations() {
            const resultDiv = document.getElementById('memory-operations-result');

            if (!wasmInstance) {
                resultDiv.innerHTML = '<div class="error">❌ 请先加载 WASM 模块</div>';
                return;
            }

            try {
                const exports = wasmInstance.exports;
                
                if (exports.memory) {
                    const memory = exports.memory;
                    const buffer = new Uint8Array(memory.buffer);
                    
                    // 测试内存读写
                    const testData = new TextEncoder().encode('Hello WASM!');
                    buffer.set(testData, 0);
                    
                    const readData = buffer.slice(0, testData.length);
                    const readString = new TextDecoder().decode(readData);
                    
                    resultDiv.innerHTML = `
                        <div class="success">✅ 内存操作成功</div>
                        <div class="info">写入数据: Hello WASM!</div>
                        <div class="info">读取数据: ${readString}</div>
                        <div class="info">内存页数: ${memory.buffer.byteLength / 65536}</div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 内存对象不可用</div>';
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 内存测试失败: ${error.message}</div>`;
            }
        }

        // 测试性能
        async function testPerformance() {
            const resultDiv = document.getElementById('performance-result');

            if (!wasmInstance) {
                resultDiv.innerHTML = '<div class="error">❌ 请先加载 WASM 模块</div>';
                return;
            }

            try {
                const exports = wasmInstance.exports;
                const iterations = 10000;
                
                // 测试函数调用性能
                const startTime = performance.now();
                
                for (let i = 0; i < iterations; i++) {
                    if (exports.getVersion) {
                        exports.getVersion();
                    }
                }
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const avgTime = totalTime / iterations;
                
                resultDiv.innerHTML = `
                    <div class="success">✅ 性能测试完成</div>
                    <div class="info">总时间: ${totalTime.toFixed(2)} ms</div>
                    <div class="info">平均时间: ${avgTime.toFixed(4)} ms/call</div>
                    <div class="info">调用次数: ${iterations}</div>
                    <div class="info">每秒调用: ${(1000 / avgTime).toFixed(0)} calls/sec</div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 性能测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动测试 WASM 加载
        window.addEventListener('load', () => {
            setTimeout(testWasmLoad, 500);
        });
    </script>
</body>
</html>
