<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: white;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🚀 WASM 修复测试</h1>
    
    <div class="test-section">
        <h2>1. 模块加载测试</h2>
        <button onclick="testModuleLoad()">测试模块加载</button>
        <div id="loadResult"></div>
    </div>

    <div class="test-section">
        <h2>2. 初始化测试</h2>
        <button onclick="testInitialization()">测试初始化</button>
        <div id="initResult"></div>
    </div>

    <div class="test-section">
        <h2>3. 转换测试</h2>
        <button onclick="testConversion()">测试转换功能</button>
        <div id="conversionResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 函数可用性测试</h2>
        <button onclick="testFunctions()">测试所有函数</button>
        <div id="functionsResult"></div>
    </div>

    <script src="wasm-converter.js"></script>
    <script>
        let wasmConverter = window.wasmConverter;

        async function testModuleLoad() {
            const resultDiv = document.getElementById('loadResult');
            resultDiv.innerHTML = '<div class="info">正在测试模块加载...</div>';

            try {
                // 直接测试 ES 模块导入
                const wasmModule = await import('./wasm/build/converter.js');
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ ES 模块加载成功！
                        <br>可用函数数量: ${Object.keys(wasmModule).length}
                        <pre>${Object.keys(wasmModule).slice(0, 10).join(', ')}...</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 模块加载失败: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testInitialization() {
            const resultDiv = document.getElementById('initResult');
            resultDiv.innerHTML = '<div class="info">正在测试初始化...</div>';

            try {
                const startTime = performance.now();
                const success = await wasmConverter.initialize();
                const endTime = performance.now();

                if (success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ WASM 转换器初始化成功！
                            <br>初始化时间: ${(endTime - startTime).toFixed(2)}ms
                            <br>模块可用: ${wasmConverter.isAvailable()}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ WASM 转换器初始化失败
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 初始化错误: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testConversion() {
            const resultDiv = document.getElementById('conversionResult');
            resultDiv.innerHTML = '<div class="info">正在测试转换功能...</div>';

            const testInput = `{{#if user.isActive}}
  <h1>{{user.name}}</h1>
  <p>{{user.email}}</p>
{{else}}
  <p>User not active</p>
{{/if}}`;

            try {
                const startTime = performance.now();
                const result = await wasmConverter.convertHandlebarsToSline(testInput);
                const endTime = performance.now();

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 转换成功！
                            <br>转换时间: ${(endTime - startTime).toFixed(2)}ms
                            <br>输入长度: ${testInput.length} 字符
                            <br>输出长度: ${result.data.converted.length} 字符
                            <h4>转换结果:</h4>
                            <pre>${result.data.converted}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 转换失败: ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 转换错误: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testFunctions() {
            const resultDiv = document.getElementById('functionsResult');
            resultDiv.innerHTML = '<div class="info">正在测试函数可用性...</div>';

            try {
                if (!wasmConverter.isAvailable()) {
                    await wasmConverter.initialize();
                }

                const moduleInfo = wasmConverter.getModuleInfo();
                
                // 测试一些关键函数
                const testResults = [];
                
                try {
                    const version = wasmConverter.wasmModule.getVersion();
                    testResults.push(`✅ getVersion(): ${version}`);
                } catch (e) {
                    testResults.push(`❌ getVersion(): ${e.message}`);
                }

                try {
                    const mathResult = wasmConverter.wasmModule.add(5, 3);
                    testResults.push(`✅ add(5, 3): ${mathResult}`);
                } catch (e) {
                    testResults.push(`❌ add(5, 3): ${e.message}`);
                }

                try {
                    wasmConverter.wasmModule.initializeConverter();
                    testResults.push(`✅ initializeConverter(): 成功`);
                } catch (e) {
                    testResults.push(`❌ initializeConverter(): ${e.message}`);
                }

                try {
                    const stats = wasmConverter.wasmModule.getAllMappingStats();
                    testResults.push(`✅ getAllMappingStats(): ${typeof stats} (${stats})`);
                } catch (e) {
                    testResults.push(`❌ getAllMappingStats(): ${e.message}`);
                }

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>模块信息:</h4>
                        <pre>${JSON.stringify(moduleInfo, null, 2)}</pre>
                        <h4>函数测试结果:</h4>
                        <pre>${testResults.join('\n')}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 函数测试错误: ${error.message}
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        // 页面加载时自动运行基本测试
        window.addEventListener('load', async () => {
            console.log('🚀 开始自动测试...');
            await testModuleLoad();
        });
    </script>
</body>
</html>
