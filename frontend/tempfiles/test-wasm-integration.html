<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 集成测试 - Handlebars to Sline</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 12px;
            border-radius: 8px;
            margin: 12px 0;
            font-family: monospace;
        }
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> WASM 集成测试</h1>
            <p>测试 WebAssembly 高性能转换模块的集成和功能</p>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-cog"></i> 初始化测试</h2>
            <button id="initTest" class="btn btn-primary">
                <i class="fas fa-play"></i> 开始初始化测试
            </button>
            <div id="initResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-exchange-alt"></i> 转换功能测试</h2>
            <div style="margin-bottom: 16px;">
                <label for="testInput">测试输入 (Handlebars):</label>
                <textarea id="testInput" rows="6" style="width: 100%; margin-top: 8px; padding: 12px; border: 1px solid #ddd; border-radius: 6px;">{{#if user.isActive}}
  <div class="user-profile">
    <h2>{{user.name}}</h2>
    <p>{{user.email}}</p>
    {{#each user.tags}}
      <span class="tag">{{this}}</span>
    {{/each}}
  </div>
{{else}}
  <p>User is not active</p>
{{/if}}</textarea>
            </div>
            <div style="margin-bottom: 16px;">
                <button id="wasmTest" class="btn btn-success">
                    <i class="fas fa-rocket"></i> WASM 转换测试
                </button>
                <button id="apiTest" class="btn btn-secondary">
                    <i class="fas fa-cloud"></i> API 转换测试
                </button>
                <button id="performanceTest" class="btn btn-warning">
                    <i class="fas fa-stopwatch"></i> 性能对比测试
                </button>
            </div>
            <div id="conversionResults"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> 性能统计</h2>
            <div id="performanceStats" class="performance-stats">
                <!-- 性能统计将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> 模块信息</h2>
            <button id="moduleInfoTest" class="btn btn-info">
                <i class="fas fa-info"></i> 获取模块信息
            </button>
            <div id="moduleInfoResults"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="wasm-converter.js"></script>
    <script>
        class WasmTester {
            constructor() {
                this.wasmConverter = window.wasmConverter;
                this.apiBaseUrl = 'http://localhost:3000';
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                document.getElementById('initTest').addEventListener('click', () => this.testInitialization());
                document.getElementById('wasmTest').addEventListener('click', () => this.testWasmConversion());
                document.getElementById('apiTest').addEventListener('click', () => this.testApiConversion());
                document.getElementById('performanceTest').addEventListener('click', () => this.testPerformance());
                document.getElementById('moduleInfoTest').addEventListener('click', () => this.testModuleInfo());
            }

            async testInitialization() {
                const resultsDiv = document.getElementById('initResults');
                resultsDiv.innerHTML = '<div class="test-info">正在初始化 WASM 模块...</div>';

                try {
                    const startTime = performance.now();
                    const success = await this.wasmConverter.initialize();
                    const endTime = performance.now();
                    const initTime = (endTime - startTime).toFixed(2);

                    if (success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                ✅ WASM 模块初始化成功！
                                <br>初始化时间: ${initTime}ms
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                ❌ WASM 模块初始化失败
                            </div>
                        `;
                    }
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            ❌ 初始化错误: ${error.message}
                        </div>
                    `;
                }
            }

            async testWasmConversion() {
                const input = document.getElementById('testInput').value;
                const resultsDiv = document.getElementById('conversionResults');
                
                resultsDiv.innerHTML = '<div class="test-info">正在使用 WASM 进行转换...</div>';

                try {
                    const startTime = performance.now();
                    const result = await this.wasmConverter.convertHandlebarsToSline(input);
                    const endTime = performance.now();
                    const conversionTime = (endTime - startTime).toFixed(2);

                    if (result.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                ✅ WASM 转换成功！ (${conversionTime}ms)
                                <pre style="margin-top: 12px; background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto;">${result.data.converted}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                ❌ WASM 转换失败: ${result.error}
                            </div>
                        `;
                    }
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            ❌ WASM 转换错误: ${error.message}
                        </div>
                    `;
                }
            }

            async testApiConversion() {
                const input = document.getElementById('testInput').value;
                const resultsDiv = document.getElementById('conversionResults');
                
                resultsDiv.innerHTML = '<div class="test-info">正在使用 API 进行转换...</div>';

                try {
                    const startTime = performance.now();
                    const response = await fetch(`${this.apiBaseUrl}/api/convert`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ content: input })
                    });
                    const result = await response.json();
                    const endTime = performance.now();
                    const conversionTime = (endTime - startTime).toFixed(2);

                    if (result.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                ✅ API 转换成功！ (${conversionTime}ms)
                                <pre style="margin-top: 12px; background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto;">${result.data.converted}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                ❌ API 转换失败: ${result.error}
                            </div>
                        `;
                    }
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            ❌ API 转换错误: ${error.message}
                        </div>
                    `;
                }
            }

            async testPerformance() {
                const input = document.getElementById('testInput').value;
                const iterations = 100;
                
                const statsDiv = document.getElementById('performanceStats');
                statsDiv.innerHTML = '<div class="test-info">正在进行性能测试...</div>';

                try {
                    // WASM 性能测试
                    const wasmTimes = [];
                    for (let i = 0; i < iterations; i++) {
                        const start = performance.now();
                        await this.wasmConverter.convertHandlebarsToSline(input);
                        const end = performance.now();
                        wasmTimes.push(end - start);
                    }

                    const wasmAvg = wasmTimes.reduce((a, b) => a + b, 0) / wasmTimes.length;
                    const wasmMin = Math.min(...wasmTimes);
                    const wasmMax = Math.max(...wasmTimes);

                    statsDiv.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${wasmAvg.toFixed(2)}ms</div>
                            <div class="stat-label">WASM 平均时间</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${wasmMin.toFixed(2)}ms</div>
                            <div class="stat-label">WASM 最快时间</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${wasmMax.toFixed(2)}ms</div>
                            <div class="stat-label">WASM 最慢时间</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${iterations}</div>
                            <div class="stat-label">测试次数</div>
                        </div>
                    `;
                } catch (error) {
                    statsDiv.innerHTML = `
                        <div class="test-error">
                            ❌ 性能测试错误: ${error.message}
                        </div>
                    `;
                }
            }

            async testModuleInfo() {
                const resultsDiv = document.getElementById('moduleInfoResults');
                
                try {
                    const info = this.wasmConverter.getModuleInfo();
                    
                    resultsDiv.innerHTML = `
                        <div class="test-info">
                            <h4>模块信息:</h4>
                            <pre style="margin-top: 8px;">${JSON.stringify(info, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            ❌ 获取模块信息错误: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            new WasmTester();
        });
    </script>
</body>
</html>
