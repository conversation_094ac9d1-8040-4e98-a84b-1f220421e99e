<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM 验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 WASM 模块验证</h1>
    
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="clearResults()">清空结果</button>
    
    <div id="results"></div>

    <script src="wasm-converter.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(type, title, content) {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function runAllTests() {
            clearResults();
            
            // 测试 1: 检查 WASM 支持
            addResult('info', '测试 1: WebAssembly 支持', 
                typeof WebAssembly !== 'undefined' ? '✅ 支持' : '❌ 不支持');
            
            // 测试 2: 检查转换器存在
            addResult('info', '测试 2: WASM 转换器', 
                typeof window.wasmConverter !== 'undefined' ? '✅ 存在' : '❌ 不存在');
            
            if (typeof window.wasmConverter === 'undefined') {
                addResult('error', '错误', 'WASM 转换器不存在，无法继续测试');
                return;
            }
            
            // 测试 3: 初始化
            try {
                addResult('info', '测试 3: 初始化', '正在初始化...');
                const success = await window.wasmConverter.initialize();
                addResult(success ? 'success' : 'error', '测试 3: 初始化结果', 
                    success ? '✅ 初始化成功' : '❌ 初始化失败');
                
                if (!success) return;
                
                // 测试 4: 基本转换
                const testInput = '{{#if user}}Hello {{user.name}}!{{/if}}';
                const result = await window.wasmConverter.convertHandlebarsToSline(testInput);
                
                if (result.success) {
                    addResult('success', '测试 4: 转换功能', `
                        ✅ 转换成功<br>
                        输入: <code>${testInput}</code><br>
                        输出长度: ${result.data.converted.length} 字符<br>
                        <pre>${result.data.converted}</pre>
                    `);
                } else {
                    addResult('error', '测试 4: 转换功能', `❌ 转换失败: ${result.error}`);
                }
                
                // 测试 5: 模块信息
                const moduleInfo = window.wasmConverter.getModuleInfo();
                addResult('info', '测试 5: 模块信息', `
                    <pre>${JSON.stringify(moduleInfo, null, 2)}</pre>
                `);
                
                // 测试 6: 性能测试
                const iterations = 10;
                const times = [];
                for (let i = 0; i < iterations; i++) {
                    const start = performance.now();
                    await window.wasmConverter.convertHandlebarsToSline(testInput);
                    const end = performance.now();
                    times.push(end - start);
                }
                
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                addResult('success', '测试 6: 性能测试', `
                    ✅ 性能测试完成<br>
                    迭代次数: ${iterations}<br>
                    平均时间: ${avgTime.toFixed(2)}ms<br>
                    最快时间: ${Math.min(...times).toFixed(2)}ms<br>
                    最慢时间: ${Math.max(...times).toFixed(2)}ms
                `);
                
            } catch (error) {
                addResult('error', '测试错误', `
                    ❌ 测试过程中发生错误<br>
                    错误信息: ${error.message}<br>
                    <pre>${error.stack}</pre>
                `);
            }
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
