# HBS2Sline WASM Converter

A high-performance WebAssembly module for converting Handlebars and Liquid templates to Sline format.

## Overview

This WASM module provides fast, client-side template conversion capabilities with comprehensive rule support for:

- **Handlebars to Sline** conversion
- **Liquid to Sline** conversion
- **Object mappings** (product, cart, customer, etc.)
- **Filter transformations** (date, string, math, array filters)
- **Variable mappings** (loop variables, operators)
- **Tag rules** (conditional, iteration, assignment, include, comment, output)

## Features

### ✅ Complete Rule Coverage

- **Conditional Rules**: if/else/unless/case transformations
- **Iteration Rules**: each/for/tablerow loop conversions
- **Assignment Rules**: assign/var/capture/increment transformations
- **Include Rules**: include/render/section/layout handling
- **Comment Rules**: comment/raw block conversions
- **Output Rules**: variable output and filter chain transformations

### ✅ High Performance

- Compiled to WebAssembly for near-native performance
- Optimized for large template files
- Memory-efficient rule processing
- Fast string manipulation operations

### ✅ Comprehensive Statistics

- Real-time conversion statistics
- Rule usage analytics
- Category-based metrics
- Performance monitoring

## Installation

```bash
npm install
npm run build
```

## Usage

### Basic Usage

```javascript
// Load WASM module
const wasmBuffer = fs.readFileSync('build/converter.wasm');
const wasmModule = await WebAssembly.compile(wasmBuffer);
const wasmInstance = await WebAssembly.instantiate(wasmModule);

const { exports } = wasmInstance;

// Initialize converter
exports.initializeConverter();

// Convert Handlebars to Sline
const handlebarsTemplate = '{{#if product.available}}{{product.title}}{{/if}}';
const slineResult = exports.convertHbsToSline(handlebarsTemplate);

// Convert Liquid to Sline
const liquidTemplate = '{% if product.available %}{{ product.title }}{% endif %}';
const slineResult2 = exports.convertLiquidToSline(liquidTemplate);
```

### Advanced Usage

```javascript
// Get comprehensive statistics
const stats = exports.getAllMappingStats();
console.log('Conversion statistics:', JSON.parse(stats));

// Test specific rule categories
const conditionalStats = exports.getConditionalRuleStats();
const iterationStats = exports.getIterationRuleStats();
const assignmentStats = exports.getAssignmentRuleStats();

// Test individual transformations
const conditionalResult = exports.testHandlebarsConditionalTransformation(input);
const iterationResult = exports.testLiquidIterationTransformation(input);
```

## API Reference

### Core Functions

#### `initializeConverter(): void`
Initialize the converter with all rule systems.

#### `convertHbsToSline(input: string): string`
Convert Handlebars template to Sline format.

#### `convertLiquidToSline(input: string): string`
Convert Liquid template to Sline format.

#### `getVersion(): number`
Get the converter version number.

### Statistics Functions

#### `getAllMappingStats(): string`
Get comprehensive mapping statistics as JSON string.

#### `getConditionalRuleStats(): string`
Get conditional rule statistics.

#### `getIterationRuleStats(): string`
Get iteration rule statistics.

#### `getAssignmentRuleStats(): string`
Get assignment rule statistics.

#### `getIncludeRuleStats(): string`
Get include rule statistics.

#### `getCommentRuleStats(): string`
Get comment rule statistics.

#### `getOutputRuleStats(): string`
Get output rule statistics.

### Test Functions

#### Handlebars Test Functions
- `testHandlebarsConditionalTransformation(input: string): string`
- `testHandlebarsIterationTransformation(input: string): string`
- `testHandlebarsAssignmentTransformation(input: string): string`
- `testHandlebarsIncludeTransformation(input: string): string`
- `testHandlebarsCommentTransformation(input: string): string`
- `testHandlebarsOutputTransformation(input: string): string`

#### Liquid Test Functions
- `testLiquidConditionalTransformation(input: string): string`
- `testLiquidIterationTransformation(input: string): string`
- `testLiquidAssignmentTransformation(input: string): string`
- `testLiquidIncludeTransformation(input: string): string`
- `testLiquidCommentTransformation(input: string): string`
- `testLiquidOutputTransformation(input: string): string`

## Build Commands

```bash
# Build debug version
npm run build:debug

# Build release version
npm run build

# Run tests
npm test

# Clean build files
npm run clean
```

## File Structure

```
wasm/
├── src/
│   ├── converter.ts          # Main converter logic
│   ├── mappings/
│   │   ├── objects.ts        # Object mappings
│   │   ├── filters.ts        # Filter mappings
│   │   └── variables.ts      # Variable mappings
│   └── rules/
│       ├── conditional.ts    # Conditional rules
│       ├── iteration.ts      # Iteration rules
│       ├── assignment.ts     # Assignment rules
│       ├── include.ts        # Include rules
│       ├── comment.ts        # Comment rules
│       └── output.ts         # Output rules
├── tests/
│   ├── basic.test.js         # Basic functionality tests
│   ├── conditional.test.js   # Conditional rule tests
│   ├── iteration.test.js     # Iteration rule tests
│   ├── assignment.test.js    # Assignment rule tests
│   ├── integration.test.js   # Integration tests
│   └── test-runner.js        # Test runner
├── build/
│   ├── converter.wasm        # Release build
│   ├── converter-debug.wasm  # Debug build
│   └── converter.wat         # WebAssembly text format
└── package.json
```

## Performance

The WASM module is optimized for:

- **Fast compilation**: Efficient AssemblyScript to WASM compilation
- **Memory efficiency**: Minimal memory footprint
- **String processing**: Optimized string manipulation
- **Rule processing**: Fast rule lookup and application

## Browser Compatibility

- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## Contributing

1. Make changes to TypeScript source files in `src/`
2. Run `npm run build:debug` to build debug version
3. Run `npm test` to run tests
4. Run `npm run build` to build release version

## License

MIT License - see LICENSE file for details.
