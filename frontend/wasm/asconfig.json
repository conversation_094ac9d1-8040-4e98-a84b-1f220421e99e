{"targets": {"debug": {"binaryFile": "build/converter-debug.wasm", "textFile": "build/converter-debug.wat", "sourceMap": true, "debug": true}, "release": {"binaryFile": "build/converter.wasm", "textFile": "build/converter.wat", "sourceMap": true, "optimizeLevel": 3, "shrinkLevel": 2, "converge": false, "noAssert": false}}, "options": {"bindings": "esm", "exportRuntime": false, "transform": [], "use": [], "lib": [], "path": []}}