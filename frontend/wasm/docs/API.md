# HBS2Sline WASM API Documentation

## Table of Contents

1. [Core Functions](#core-functions)
2. [Statistics Functions](#statistics-functions)
3. [Test Functions](#test-functions)
4. [Rule Lookup Functions](#rule-lookup-functions)
5. [Mapping Functions](#mapping-functions)
6. [Error Handling](#error-handling)
7. [Performance Considerations](#performance-considerations)

## Core Functions

### `initializeConverter(): void`

Initializes the converter with all rule systems including objects, filters, variables, and tag rules.

**Usage:**
```javascript
exports.initializeConverter();
```

**Note:** Must be called before using any conversion functions.

---

### `convertHbsToSline(input: string): string`

Converts Handlebars template to Sline format with complete rule application.

**Parameters:**
- `input` (string): Handlebars template content

**Returns:**
- `string`: Converted Sline template

**Usage:**
```javascript
const handlebarsTemplate = '{{#if product.available}}{{product.title}}{{/if}}';
const result = exports.convertHbsToSline(handlebarsTemplate);
```

---

### `convertLiquidToSline(input: string): string`

Converts Liquid template to Sline format with complete rule application.

**Parameters:**
- `input` (string): Liquid template content

**Returns:**
- `string`: Converted Sline template

**Usage:**
```javascript
const liquidTemplate = '{% if product.available %}{{ product.title }}{% endif %}';
const result = exports.convertLiquidToSline(liquidTemplate);
```

---

### `getVersion(): number`

Returns the converter version number.

**Returns:**
- `number`: Version number

**Usage:**
```javascript
const version = exports.getVersion();
console.log('Converter version:', version);
```

---

### `add(a: number, b: number): number`

Basic math function for testing WASM functionality.

**Parameters:**
- `a` (number): First number
- `b` (number): Second number

**Returns:**
- `number`: Sum of a and b

## Statistics Functions

### `getAllMappingStats(): string`

Returns comprehensive mapping statistics for all rule categories.

**Returns:**
- `string`: JSON string containing all statistics

**Usage:**
```javascript
const stats = exports.getAllMappingStats();
const parsed = JSON.parse(stats);
console.log('Total rules:', parsed);
```

**Response Format:**
```json
{
  "objects": {
    "handlebars": 50,
    "liquid": 45
  },
  "filters": {
    "handlebars": 30,
    "liquid": 75
  },
  "conditional_rules": {
    "handlebars": 8,
    "liquid": 12,
    "if_rules": 6,
    "unless_rules": 2,
    "case_rules": 4
  },
  "iteration_rules": {
    "handlebars": 6,
    "liquid": 10,
    "each_rules": 4,
    "for_rules": 6,
    "variable_rules": 4,
    "control_rules": 2
  }
}
```

---

### `getConditionalRuleStats(): string`

Returns statistics for conditional rules only.

**Returns:**
- `string`: JSON string with conditional rule statistics

---

### `getIterationRuleStats(): string`

Returns statistics for iteration rules only.

**Returns:**
- `string`: JSON string with iteration rule statistics

---

### `getAssignmentRuleStats(): string`

Returns statistics for assignment rules only.

**Returns:**
- `string`: JSON string with assignment rule statistics

---

### `getIncludeRuleStats(): string`

Returns statistics for include rules only.

**Returns:**
- `string`: JSON string with include rule statistics

---

### `getCommentRuleStats(): string`

Returns statistics for comment rules only.

**Returns:**
- `string`: JSON string with comment rule statistics

---

### `getOutputRuleStats(): string`

Returns statistics for output rules only.

**Returns:**
- `string`: JSON string with output rule statistics

## Test Functions

### Handlebars Test Functions

#### `testHandlebarsConditionalTransformation(input: string): string`

Tests conditional transformation on Handlebars input.

**Parameters:**
- `input` (string): Handlebars template with conditionals

**Returns:**
- `string`: Transformed template

---

#### `testHandlebarsIterationTransformation(input: string): string`

Tests iteration transformation on Handlebars input.

---

#### `testHandlebarsAssignmentTransformation(input: string): string`

Tests assignment transformation on Handlebars input.

---

#### `testHandlebarsIncludeTransformation(input: string): string`

Tests include transformation on Handlebars input.

---

#### `testHandlebarsCommentTransformation(input: string): string`

Tests comment transformation on Handlebars input.

---

#### `testHandlebarsOutputTransformation(input: string): string`

Tests output transformation on Handlebars input.

### Liquid Test Functions

#### `testLiquidConditionalTransformation(input: string): string`

Tests conditional transformation on Liquid input.

**Parameters:**
- `input` (string): Liquid template with conditionals

**Returns:**
- `string`: Transformed template

---

#### `testLiquidIterationTransformation(input: string): string`

Tests iteration transformation on Liquid input.

---

#### `testLiquidAssignmentTransformation(input: string): string`

Tests assignment transformation on Liquid input.

---

#### `testLiquidIncludeTransformation(input: string): string`

Tests include transformation on Liquid input.

---

#### `testLiquidCommentTransformation(input: string): string`

Tests comment transformation on Liquid input.

---

#### `testLiquidOutputTransformation(input: string): string`

Tests output transformation on Liquid input.

## Rule Lookup Functions

### `testHandlebarsConditionalRule(ruleName: string): string`

Looks up a specific Handlebars conditional rule by name.

**Parameters:**
- `ruleName` (string): Name of the rule to lookup

**Returns:**
- `string`: Rule replacement or empty string if not found

---

### `testLiquidConditionalRule(ruleName: string): string`

Looks up a specific Liquid conditional rule by name.

---

### `testHandlebarsIterationRule(ruleName: string): string`

Looks up a specific Handlebars iteration rule by name.

---

### `testLiquidIterationRule(ruleName: string): string`

Looks up a specific Liquid iteration rule by name.

## Mapping Functions

### `getObjectStats(): ObjectStats`

Returns object mapping statistics.

### `getFilterStats(): FilterStats`

Returns filter mapping statistics.

### `getVariableStats(): VariableStats`

Returns variable mapping statistics.

## Error Handling

The WASM module handles errors gracefully:

- **Invalid input**: Returns original input unchanged
- **Missing initialization**: Functions may return empty results
- **Memory errors**: Handled by WASM runtime

**Best Practices:**
1. Always call `initializeConverter()` first
2. Check return values for empty strings
3. Handle JSON parsing errors for statistics functions

## Performance Considerations

### Memory Usage

- **Initialization**: ~1MB for all rule systems
- **Processing**: Minimal additional memory per conversion
- **Cleanup**: Automatic garbage collection

### Speed Optimizations

- **Rule caching**: Rules are pre-compiled and cached
- **String operations**: Optimized for large templates
- **Batch processing**: Process multiple templates efficiently

### Recommended Usage Patterns

```javascript
// Initialize once
exports.initializeConverter();

// Process multiple templates
const templates = ['template1', 'template2', 'template3'];
const results = templates.map(template => 
  exports.convertHbsToSline(template)
);

// Get statistics periodically
const stats = exports.getAllMappingStats();
```
