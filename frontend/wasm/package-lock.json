{"name": "hbs2sline-wasm", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "hbs2sline-wasm", "version": "1.0.0", "license": "MIT", "devDependencies": {"assemblyscript": "^0.27.0"}}, "node_modules/assemblyscript": {"version": "0.27.37", "resolved": "https://registry.npmjs.org/assemblyscript/-/assemblyscript-0.27.37.tgz", "integrity": "sha512-YtY5k3PiV3SyUQ6gRlR2OCn8dcVRwkpiG/k2T5buoL2ymH/Z/YbaYWbk/f9mO2HTgEtGWjPiAQrIuvA7G/63Gg==", "dev": true, "license": "Apache-2.0", "dependencies": {"binaryen": "116.0.0-nightly.20240114", "long": "^5.2.4"}, "bin": {"asc": "bin/asc.js", "asinit": "bin/asinit.js"}, "engines": {"node": ">=18", "npm": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/assemblyscript"}}, "node_modules/binaryen": {"version": "116.0.0-nightly.20240114", "resolved": "https://registry.npmjs.org/binaryen/-/binaryen-116.0.0-nightly.20240114.tgz", "integrity": "sha512-0GZrojJnuhoe+hiwji7QFaL3tBlJoA+KFUN7ouYSDGZLSo9CKM8swQX8n/UcbR0d1VuZKU+nhogNzv423JEu5A==", "dev": true, "license": "Apache-2.0", "bin": {"wasm-opt": "bin/wasm-opt", "wasm2js": "bin/wasm2js"}}, "node_modules/long": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/long/-/long-5.3.2.tgz", "integrity": "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==", "dev": true, "license": "Apache-2.0"}}}