{"name": "hbs2sline-wasm", "version": "1.0.0", "description": "WASM module for HBS2Sline converter using AssemblyScript", "main": "build/converter.wasm", "scripts": {"prebuild": "npm run generate:rules", "build": "asc src/converter.ts -o build/converter.wasm -t build/converter.wat --bindings esm --target release", "build:debug": "npm run generate:rules && asc src/converter.ts -o build/converter-debug.wasm -t build/converter-debug.wat --bindings esm --target debug", "build:watch": "asc src/converter.ts -o build/converter-debug.wasm -t build/converter-debug.wat --bindings esm --target debug --watch", "build:release": "npm run generate:rules && asc src/converter.ts -o build/converter.wasm -t build/converter.wat --bindings esm --target release", "generate:rules": "node scripts/generate-rules.js", "generate:rules:watch": "node scripts/generate-rules.js --watch", "generate:rules:validate": "node scripts/generate-rules.js --validate", "test": "npm run build:debug && node tests/test-runner.js", "test:rules": "node scripts/test-rules.js", "test:rules:verbose": "node scripts/test-rules.js --verbose", "test:all": "npm run test:rules && npm run test", "clean": "rm -rf build/*", "clean:rules": "rm -rf src/rules/*.ts"}, "keywords": ["wasm", "assemblyscript", "handlebars", "sline", "converter"], "author": "Shopline Theme Developer", "license": "MIT", "devDependencies": {"assemblyscript": "^0.27.0"}}