# 统一规则源系统

这个系统实现了从 `frontend/rules/` 的 JSON 文件自动生成 WASM 兼容的 TypeScript 规则代码，确保规则的一致性和同步。

## 🎯 目标

- **统一规则源**: 所有规则定义都来自 `frontend/rules/` 目录
- **自动同步**: 构建时自动生成最新的 WASM 规则代码
- **类型安全**: 生成的 TypeScript 代码具有完整的类型定义
- **验证机制**: 自动验证生成的代码与原始规则的一致性

## 📁 文件结构

```
frontend/wasm/scripts/
├── generate-rules.js     # 主要的规则生成脚本
├── test-rules.js        # 规则验证和测试脚本
├── config.json          # 配置文件
└── README.md           # 本文档

frontend/wasm/src/rules/         # 生成的 TypeScript 规则文件
├── assignment.ts       # 变量赋值规则
├── include.ts          # 模板包含规则
├── conditional.ts      # 条件判断规则
├── iteration.ts        # 循环迭代规则
├── output.ts          # 输出规则
└── comment.ts         # 注释规则
```

## 🚀 使用方法

### 生成规则

```bash
# 生成所有规则
npm run generate:rules

# 只生成特定类别的规则
node scripts/generate-rules.js --category assignment

# 生成并验证规则
npm run generate:rules:validate

# 监听模式（自动重新生成）
npm run generate:rules:watch
```

### 测试规则

```bash
# 运行所有规则测试
npm run test:rules

# 详细输出模式
npm run test:rules:verbose

# 只测试特定类别
node scripts/test-rules.js --category assignment --verbose
```

### 构建集成

规则生成已集成到构建流程中：

```bash
# 构建时自动生成规则
npm run build:debug
npm run build:release

# 完整测试（包括规则测试）
npm run test:all
```

## 📝 规则格式

### JSON 规则格式

```json
{
  "version": "1.0.0",
  "source": "backend/handlebars-to-sline-rules",
  "category": "assignment",
  "lastUpdated": "2025-08-06T00:47:00.000Z",
  "data": {
    "HANDLEBARS_ASSIGNMENT_RULES": [
      {
        "name": "Handlebars 变量赋值转换",
        "category": "assignment",
        "description": "转换 Handlebars 变量赋值到 Sline var 标签",
        "pattern": {},
        "examples": {
          "handlebars": "{{#assign price = product.price}}",
          "sline": "{{#var price = product.price /}}"
        }
      }
    ]
  }
}
```

### 生成的 TypeScript 格式

```typescript
class AssignmentRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

const handlebarsAssignmentRules: AssignmentRule[] = [];

function initializeHandlebarsAssignmentRules(): void {
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 变量赋值转换",
    "assignment",
    "转换 Handlebars 变量赋值到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
}
```

## 🔧 配置

配置文件 `config.json` 包含以下设置：

- **paths**: 输入和输出路径配置
- **categories**: 支持的规则类别
- **generation**: 代码生成选项
- **validation**: 验证设置
- **templates**: 代码模板配置

## 📊 验证机制

测试脚本会验证以下内容：

1. **文件存在性**: 检查生成的 TypeScript 文件是否存在
2. **规则数量**: 验证生成的规则数量与原始 JSON 规则一致
3. **规则内容**: 检查所有规则名称是否正确包含在生成的代码中
4. **TypeScript 语法**: 验证生成的代码语法正确性

## 🔄 工作流程

1. **规则定义**: 在 `frontend/rules/` 中定义或修改 JSON 规则
2. **自动生成**: 构建时自动运行 `generate-rules.js`
3. **代码生成**: 生成对应的 TypeScript 规则文件
4. **验证测试**: 运行测试确保一致性
5. **WASM 编译**: 将生成的规则编译到 WASM 模块中

## 🛠️ 开发指南

### 添加新的规则类别

1. 在 `frontend/rules/handlebars/` 和 `frontend/rules/liquid/` 中创建对应的 JSON 文件
2. 更新 `config.json` 中的 `categories` 配置
3. 在 `generate-rules.js` 中的 `SUPPORTED_CATEGORIES` 数组中添加新类别
4. 运行 `npm run generate:rules` 生成新的规则文件

### 修改现有规则

1. 编辑对应的 JSON 规则文件
2. 运行 `npm run generate:rules` 重新生成
3. 运行 `npm run test:rules` 验证更改

### 调试生成过程

```bash
# 启用详细输出
node scripts/generate-rules.js --category assignment --validate

# 查看生成的代码
cat ../src/rules/assignment.ts

# 运行详细测试
node scripts/test-rules.js --category assignment --verbose
```

## 🚨 注意事项

1. **不要手动编辑生成的文件**: 所有 `wasm/src/rules/*.ts` 文件都是自动生成的
2. **保持 JSON 格式一致**: 确保新的规则文件遵循现有的 JSON 结构
3. **运行测试**: 修改规则后务必运行测试验证
4. **版本控制**: 生成的文件应该提交到版本控制系统中

## 📈 性能优势

- **编译时优化**: 规则在编译时就确定，运行时性能更好
- **类型安全**: TypeScript 提供完整的类型检查
- **内存效率**: 避免运行时解析 JSON 的开销
- **一致性保证**: 自动化确保前端和 WASM 规则同步

## 🔗 相关文档

- [WASM 构建指南](../docs/WASM_BUILD_GUIDE.md)
- [规则系统文档](../../docs/RULES_SYSTEM.md)
- [前端规则管理](../../frontend/rules/README.md)
