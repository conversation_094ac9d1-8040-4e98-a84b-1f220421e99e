{"version": "1.0.0", "description": "规则生成脚本配置文件", "lastUpdated": "2025-08-06T00:00:00.000Z", "paths": {"frontendRulesDir": "../../rules", "outputDir": "../src/rules", "templatesDir": "./templates", "backupDir": "./backup"}, "categories": {"assignment": {"enabled": true, "priority": 1, "description": "变量赋值和捕获规则", "handlebarsFile": "assignment.json", "liquidFile": "assignment.json", "outputFile": "assignment.ts"}, "include": {"enabled": true, "priority": 2, "description": "模板包含和渲染规则", "handlebarsFile": "partial.json", "liquidFile": "include.json", "outputFile": "include.ts"}, "conditional": {"enabled": true, "priority": 3, "description": "条件判断规则", "handlebarsFile": "conditional.json", "liquidFile": "conditional.json", "outputFile": "conditional.ts"}, "iteration": {"enabled": true, "priority": 4, "description": "循环迭代规则", "handlebarsFile": "iteration.json", "liquidFile": "iteration.json", "outputFile": "iteration.ts"}, "output": {"enabled": true, "priority": 5, "description": "输出和过滤器规则", "handlebarsFile": "output.json", "liquidFile": "output.json", "outputFile": "output.ts"}, "comment": {"enabled": true, "priority": 6, "description": "注释处理规则", "handlebarsFile": "comment.json", "liquidFile": "comment.json", "outputFile": "comment.ts"}}, "generation": {"fileHeader": {"includeTimestamp": true, "includeSourceInfo": true, "includeWarning": true, "customHeader": ""}, "codeStyle": {"indentation": "  ", "lineEnding": "\n", "maxLineLength": 120, "includeComments": true}, "validation": {"enabled": true, "strictMode": false, "checkRuleCount": true, "checkRuleNames": true, "checkExamples": true}}, "watch": {"enabled": false, "debounceMs": 1000, "includePatterns": ["*.json"], "excludePatterns": ["temp/*", "backup/*", "*.bak"]}, "backup": {"enabled": true, "maxBackups": 10, "timestampFormat": "YYYY-MM-DD_HH-mm-ss", "compressionEnabled": false}, "logging": {"level": "info", "colorOutput": true, "logFile": "./logs/generate-rules.log", "maxLogSize": "10MB", "maxLogFiles": 5}, "templates": {"fileHeader": "file-header.template", "ruleClass": "rule-class.template", "ruleArray": "rule-array.template", "exportFunctions": "export-functions.template", "transformFunctions": "transform-functions.template"}, "engines": {"handlebars": {"name": "Handlebars", "prefix": "handlebars", "fileExtension": ".hbs", "defaultCategory": "handlebars"}, "liquid": {"name": "Liquid", "prefix": "liquid", "fileExtension": ".liquid", "defaultCategory": "liquid"}}, "output": {"typescript": {"enabled": true, "strict": true, "target": "ES2020", "moduleResolution": "node"}, "assemblyScript": {"enabled": true, "optimizeLevel": "3s", "runtime": "stub"}}}