[34mℹ️  Frontend rules directory: /Users/<USER>/Documents/shopline/hbs-to-sline-website/frontend/rules[0m
[34mℹ️  Output directory: /Users/<USER>/Documents/shopline/hbs-to-sline-website/wasm/src/rules[0m
[34mℹ️  Found rules in HANDLEBARS_ASSIGNMENT_RULES: 5 rules[0m
[34mℹ️  Found rules in LIQUID_ASSIGNMENT_TAG_RULES: 9 rules[0m
[34mℹ️  Loaded 5 Handlebars + 9 Liquid rules for assignment[0m
=== FULL GENERATED CODE ===
/**
 * WASM ASSIGNMENT Rules for HBS2Sline Converter
 * AssemblyScript implementation of assignment conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/assignment/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T00:49:13.284Z
 */

// ================== Rule Structure ==================
class AssignmentRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Assignment Rules ==================
const handlebarsAssignmentRules: AssignmentRule[] = [];

function initializeHandlebarsAssignmentRules(): void {
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 变量赋值转换",
    "assignment",
    "转换 Handlebars 变量赋值到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
  
  // Example: {{#assign price = product.price}} -> {{#var price = product.price /}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 变量赋值转换 - 带过滤器",
    "assignment",
    "转换带过滤器的 Handlebars 变量赋值",
    "",
    "{{#var title = product.title|upcase() /}}"
  ));
  
  // Example: {{#assign title = (upcase product.title)}} -> {{#var title = product.title|upcase() /}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars with 语句转换",
    "assignment",
    "转换 Handlebars with 语句到 Sline var 标签",
    "",
    "{{#var p = product}}{{#with p}}"
  ));
  
  // Example: {{#with product as |p|}} -> {{#var p = product}}{{#with p}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars let 语句转换",
    "assignment",
    "转换 Handlebars let 语句到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
  
  
=== END PREVIEW ===
