#!/usr/bin/env node

/**
 * 规则生成脚本 - 统一规则源实现
 * 从 frontend/rules/ 的 JSON 文件生成 WASM 兼容的 TypeScript 规则代码
 * 
 * 使用方法:
 * node generate-rules.js [options]
 * 
 * 选项:
 * --category <name>  只生成指定类别的规则 (assignment, include, conditional, etc.)
 * --output <dir>     指定输出目录 (默认: ../src/rules/)
 * --validate         验证生成的代码与原始规则的一致性
 * --watch            监听规则文件变化并自动重新生成
 * --help             显示帮助信息
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置常量
const CONFIG = {
    // 输入路径 (相对于脚本位置)
    FRONTEND_RULES_DIR: '../../rules',

    // 输出路径 (相对于脚本位置)
    OUTPUT_DIR: '../src/rules',
    
    // 支持的规则类别
    SUPPORTED_CATEGORIES: [
        'assignment',
        'include', 
        'conditional',
        'iteration',
        'output',
        'comment'
    ],
    
    // 模板配置
    TEMPLATES: {
        FILE_HEADER: `/**
 * WASM {CATEGORY} Rules for HBS2Sline Converter
 * AssemblyScript implementation of {category} conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/{category}/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: {timestamp}
 */`,
        
        RULE_CLASS: `// ================== Rule Structure ==================
class {Category}Rule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}`
    }
};

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) { log(`ℹ️  ${message}`, 'blue'); }
function logSuccess(message) { log(`✅ ${message}`, 'green'); }
function logWarning(message) { log(`⚠️  ${message}`, 'yellow'); }
function logError(message) { log(`❌ ${message}`, 'red'); }

// 主类
class RulesGenerator {
    constructor(options = {}) {
        this.options = {
            category: options.category || null,
            outputDir: options.outputDir || CONFIG.OUTPUT_DIR,
            validate: options.validate || false,
            watch: options.watch || false,
            ...options
        };
        
        // 解析路径
        this.scriptDir = __dirname;
        this.frontendRulesDir = path.resolve(this.scriptDir, CONFIG.FRONTEND_RULES_DIR);
        this.outputDir = path.resolve(this.scriptDir, this.options.outputDir);
        
        logInfo(`Frontend rules directory: ${this.frontendRulesDir}`);
        logInfo(`Output directory: ${this.outputDir}`);
    }
    
    /**
     * 主执行函数
     */
    async run() {
        try {
            logInfo('🚀 Starting rules generation...');
            
            // 验证输入目录
            this.validateInputDirectory();
            
            // 确保输出目录存在
            this.ensureOutputDirectory();
            
            // 获取要处理的类别
            const categories = this.getCategoriesToProcess();
            
            // 生成规则文件
            for (const category of categories) {
                await this.generateCategoryRules(category);
            }
            
            // 验证生成的代码 (如果启用)
            if (this.options.validate) {
                await this.validateGeneratedCode();
            }
            
            logSuccess('🎉 Rules generation completed successfully!');
            
            // 监听模式
            if (this.options.watch) {
                this.startWatchMode();
            }
            
        } catch (error) {
            logError(`Rules generation failed: ${error.message}`);
            process.exit(1);
        }
    }
    
    /**
     * 验证输入目录
     */
    validateInputDirectory() {
        if (!fs.existsSync(this.frontendRulesDir)) {
            throw new Error(`Frontend rules directory not found: ${this.frontendRulesDir}`);
        }
        
        // 检查必要的子目录
        const requiredDirs = ['handlebars', 'liquid'];
        for (const dir of requiredDirs) {
            const dirPath = path.join(this.frontendRulesDir, dir);
            if (!fs.existsSync(dirPath)) {
                throw new Error(`Required subdirectory not found: ${dirPath}`);
            }
        }
        
        logSuccess('Input directory validation passed');
    }
    
    /**
     * 确保输出目录存在
     */
    ensureOutputDirectory() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
            logInfo(`Created output directory: ${this.outputDir}`);
        }
    }
    
    /**
     * 获取要处理的类别列表
     */
    getCategoriesToProcess() {
        if (this.options.category) {
            if (!CONFIG.SUPPORTED_CATEGORIES.includes(this.options.category)) {
                throw new Error(`Unsupported category: ${this.options.category}`);
            }
            return [this.options.category];
        }
        
        return CONFIG.SUPPORTED_CATEGORIES;
    }
    
    /**
     * 生成指定类别的规则文件
     */
    async generateCategoryRules(category) {
        logInfo(`Generating rules for category: ${category}`);
        
        try {
            // 加载规则数据
            const rulesData = await this.loadCategoryRules(category);
            
            // 生成 TypeScript 代码
            const generatedCode = this.generateTypeScriptCode(category, rulesData);
            
            // 写入文件
            const outputFile = path.join(this.outputDir, `${category}.ts`);
            fs.writeFileSync(outputFile, generatedCode, 'utf8');
            
            logSuccess(`Generated: ${outputFile}`);
            
        } catch (error) {
            logError(`Failed to generate rules for ${category}: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * 加载指定类别的规则数据
     */
    async loadCategoryRules(category) {
        const rulesData = {
            handlebars: [],
            liquid: []
        };
        
        // 加载 Handlebars 规则
        const handlebarsFile = path.join(this.frontendRulesDir, 'handlebars', `${category}.json`);
        if (fs.existsSync(handlebarsFile)) {
            const data = JSON.parse(fs.readFileSync(handlebarsFile, 'utf8'));
            rulesData.handlebars = this.extractRulesFromData(data, 'handlebars');
        }
        
        // 加载 Liquid 规则
        const liquidFile = path.join(this.frontendRulesDir, 'liquid', `${category}.json`);
        if (fs.existsSync(liquidFile)) {
            const data = JSON.parse(fs.readFileSync(liquidFile, 'utf8'));
            rulesData.liquid = this.extractRulesFromData(data, 'liquid');
        }
        
        logInfo(`Loaded ${rulesData.handlebars.length} Handlebars + ${rulesData.liquid.length} Liquid rules for ${category}`);
        
        return rulesData;
    }
    
    /**
     * 从 JSON 数据中提取规则
     */
    extractRulesFromData(data, engine) {
        let rules = [];

        try {
            // 根据不同的 JSON 结构提取规则
            if (data.data) {
                // 新格式: { data: { RULES_ARRAY: [...] } }
                const rulesArrayKey = Object.keys(data.data).find(key =>
                    key.includes('RULES') || key.includes('TAG_RULES') || key.includes('FILTER_RULES')
                );

                if (rulesArrayKey && Array.isArray(data.data[rulesArrayKey])) {
                    rules = data.data[rulesArrayKey];
                    logInfo(`Found rules in ${rulesArrayKey}: ${rules.length} rules`);
                }
            } else if (Array.isArray(data)) {
                // 直接数组格式
                rules = data;
                logInfo(`Found direct array format: ${rules.length} rules`);
            } else if (data.rules && Array.isArray(data.rules)) {
                // { rules: [...] } 格式
                rules = data.rules;
                logInfo(`Found rules property: ${rules.length} rules`);
            }

            if (rules.length === 0) {
                logWarning(`No rules found for ${engine} in data structure`);
                logInfo(`Available keys: ${Object.keys(data)}`);
                if (data.data) {
                    logInfo(`Data keys: ${Object.keys(data.data)}`);
                }
            }

            // 规范化规则数据
            return this.normalizeRules(rules, engine);

        } catch (error) {
            logError(`Error extracting rules for ${engine}: ${error.message}`);
            return [];
        }
    }

    /**
     * 规范化规则数据，确保所有规则都有必需的字段
     */
    normalizeRules(rules, engine) {
        return rules.map((rule, index) => {
            // 确保必需字段存在
            const normalizedRule = {
                name: rule.name || `${engine}_rule_${index + 1}`,
                category: rule.category || 'unknown',
                description: rule.description || '',
                pattern: this.extractPattern(rule),
                replacement: this.extractReplacement(rule),
                examples: rule.examples || {},
                priority: rule.priority || 0,
                enabled: rule.enabled !== false, // 默认启用
                metadata: {
                    source: rule.source || 'unknown',
                    engine: engine,
                    originalIndex: index
                }
            };

            return normalizedRule;
        }).filter(rule => rule.enabled); // 只返回启用的规则
    }

    /**
     * 从规则中提取模式
     */
    extractPattern(rule) {
        if (rule.pattern) {
            if (typeof rule.pattern === 'string') {
                return rule.pattern;
            } else if (typeof rule.pattern === 'object') {
                // 复杂模式对象，提取主要模式
                return rule.pattern.regex || rule.pattern.match || rule.pattern.search || '';
            }
        }

        // 从示例中推断模式
        if (rule.examples) {
            const sourceKey = Object.keys(rule.examples).find(key =>
                key.includes('handlebars') || key.includes('liquid') || key.includes('hbs') || key.includes('source')
            );
            if (sourceKey && rule.examples[sourceKey]) {
                return this.inferPatternFromExample(rule.examples[sourceKey]);
            }
        }

        return '';
    }

    /**
     * 从规则中提取替换内容
     */
    extractReplacement(rule) {
        if (rule.replacement) {
            return rule.replacement;
        }

        // 从示例中推断替换内容
        if (rule.examples) {
            const targetKey = Object.keys(rule.examples).find(key =>
                key.includes('sline') || key.includes('target') || key.includes('output')
            );
            if (targetKey && rule.examples[targetKey]) {
                return rule.examples[targetKey];
            }
        }

        return '';
    }

    /**
     * 从示例中推断模式
     */
    inferPatternFromExample(example) {
        if (typeof example !== 'string') {
            return '';
        }

        // 简单的模式推断逻辑
        // 提取关键词或标签名
        const matches = example.match(/\{\{[#\/]?(\w+)/g);
        if (matches && matches.length > 0) {
            return matches[0].replace(/[{}#\/]/g, '');
        }

        return example;
    }

    /**
     * 生成 TypeScript 代码
     */
    generateTypeScriptCode(category, rulesData) {
        const timestamp = new Date().toISOString();
        const categoryCapitalized = this.capitalize(category);

        let code = '';

        // 文件头部
        code += this.generateFileHeader(category, categoryCapitalized, timestamp);
        code += '\n\n';

        // 规则类定义
        code += this.generateRuleClass(categoryCapitalized);
        code += '\n\n';

        // Handlebars 规则
        code += this.generateHandlebarsRules(categoryCapitalized, rulesData.handlebars);
        code += '\n\n';

        // Liquid 规则
        code += this.generateLiquidRules(categoryCapitalized, rulesData.liquid);
        code += '\n\n';

        // 统计类
        code += this.generateStatsClass(categoryCapitalized);
        code += '\n\n';

        // 导出函数
        code += this.generateExportFunctions(categoryCapitalized);
        code += '\n\n';

        // 转换函数
        code += this.generateTransformFunctions(categoryCapitalized);

        return code;
    }

    /**
     * 生成文件头部
     */
    generateFileHeader(category, categoryCapitalized, timestamp) {
        return CONFIG.TEMPLATES.FILE_HEADER
            .replace(/\{CATEGORY\}/g, categoryCapitalized.toUpperCase())
            .replace(/\{category\}/g, category)
            .replace(/\{timestamp\}/g, timestamp);
    }

    /**
     * 生成规则类定义
     */
    generateRuleClass(categoryCapitalized) {
        return CONFIG.TEMPLATES.RULE_CLASS
            .replace(/\{Category\}/g, categoryCapitalized);
    }

    /**
     * 生成 Handlebars 规则
     */
    generateHandlebarsRules(categoryCapitalized, rules) {
        let code = `// ================== Handlebars ${categoryCapitalized} Rules ==================\n`;
        code += `const handlebars${categoryCapitalized}Rules: ${categoryCapitalized}Rule[] = [];\n\n`;

        code += `function initializeHandlebars${categoryCapitalized}Rules(): void {\n`;

        for (const rule of rules) {
            code += `  handlebars${categoryCapitalized}Rules.push(new ${categoryCapitalized}Rule(\n`;
            code += `    ${this.escapeString(rule.name)},\n`;
            code += `    ${this.escapeString(rule.category)},\n`;
            code += `    ${this.escapeString(rule.description)},\n`;
            code += `    ${this.escapeString(rule.pattern)},\n`;
            code += `    ${this.escapeString(rule.replacement)}\n`;
            code += `  ));\n`;

            // 添加注释说明
            if (rule.examples && Object.keys(rule.examples).length > 0) {
                code += `  \n`;
                code += `  // Example: ${this.formatExample(rule.examples)}\n`;
            }
            code += `  \n`;
        }

        code += `}\n`;

        return code;
    }

    /**
     * 生成 Liquid 规则
     */
    generateLiquidRules(categoryCapitalized, rules) {
        let code = `// ================== Liquid ${categoryCapitalized} Rules ==================\n`;
        code += `const liquid${categoryCapitalized}Rules: ${categoryCapitalized}Rule[] = [];\n\n`;

        code += `function initializeLiquid${categoryCapitalized}Rules(): void {\n`;

        for (const rule of rules) {
            code += `  liquid${categoryCapitalized}Rules.push(new ${categoryCapitalized}Rule(\n`;
            code += `    ${this.escapeString(rule.name)},\n`;
            code += `    ${this.escapeString(rule.category)},\n`;
            code += `    ${this.escapeString(rule.description)},\n`;
            code += `    ${this.escapeString(rule.pattern)},\n`;
            code += `    ${this.escapeString(rule.replacement)}\n`;
            code += `  ));\n`;

            // 添加注释说明
            if (rule.examples && Object.keys(rule.examples).length > 0) {
                code += `  \n`;
                code += `  // Example: ${this.formatExample(rule.examples)}\n`;
            }
            code += `  \n`;
        }

        code += `}\n`;

        return code;
    }

    /**
     * 生成统计类
     */
    generateStatsClass(categoryCapitalized) {
        let code = `// ================== Statistics Class ==================\n`;
        code += `class ${categoryCapitalized}Stats {\n`;
        code += `  totalHandlebarsRules: number;\n`;
        code += `  totalLiquidRules: number;\n`;

        // 根据类别添加特定的统计字段
        const statsFields = this.getStatsFieldsForCategory(categoryCapitalized.toLowerCase());
        for (const field of statsFields) {
            code += `  ${field}: number;\n`;
        }

        code += `  \n`;
        code += `  constructor() {\n`;
        code += `    this.totalHandlebarsRules = 0;\n`;
        code += `    this.totalLiquidRules = 0;\n`;

        for (const field of statsFields) {
            code += `    this.${field} = 0;\n`;
        }

        code += `  }\n`;
        code += `}\n`;

        return code;
    }

    /**
     * 根据类别获取统计字段
     */
    getStatsFieldsForCategory(category) {
        const fieldsMap = {
            'assignment': ['assignRules', 'captureRules', 'incrementRules', 'includeRules'],
            'include': ['includeRules', 'renderRules', 'sectionRules', 'layoutRules', 'contentRules', 'yieldRules'],
            'conditional': ['ifRules', 'unlessRules', 'caseRules', 'elseRules'],
            'iteration': ['forRules', 'eachRules', 'whileRules', 'tablerowRules', 'variableRules', 'controlRules'],
            'output': ['rawOutputRules', 'commentRules', 'variableOutputRules', 'filterRules', 'mathRules', 'stringRules'],
            'comment': ['blockCommentRules', 'lineCommentRules', 'conditionalCommentRules', 'commentRules', 'rawRules', 'outputRules', 'warningRules']
        };

        return fieldsMap[category] || ['customRules'];
    }

    /**
     * 工具函数：首字母大写
     */
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    /**
     * 工具函数：转义字符串
     */
    escapeString(str) {
        if (typeof str !== 'string') {
            return '""';
        }
        return `"${str.replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r')}"`;
    }

    /**
     * 工具函数：格式化示例
     */
    formatExample(examples) {
        const entries = Object.entries(examples);
        if (entries.length === 0) return '';

        const [source, target] = entries.length >= 2 ?
            [entries[0][1], entries[1][1]] :
            [entries[0][1], ''];

        return `${source} -> ${target}`.replace(/\n/g, ' ');
    }

    /**
     * 生成导出函数
     */
    generateExportFunctions(categoryCapitalized) {
        const category = categoryCapitalized.toLowerCase();

        let code = `// ================== Exported Functions ==================\n\n`;

        // 初始化函数
        code += `/**\n`;
        code += ` * Initialize the ${category} rules system\n`;
        code += ` */\n`;
        code += `export function initialize${categoryCapitalized}Rules(): void {\n`;
        code += `  initializeHandlebars${categoryCapitalized}Rules();\n`;
        code += `  initializeLiquid${categoryCapitalized}Rules();\n`;
        code += `}\n\n`;

        // 统计函数
        code += `/**\n`;
        code += ` * Get ${category} rule statistics\n`;
        code += ` * @returns ${categoryCapitalized} rule statistics\n`;
        code += ` */\n`;
        code += `export function get${categoryCapitalized}Stats(): ${categoryCapitalized}Stats {\n`;
        code += `  const stats = new ${categoryCapitalized}Stats();\n`;
        code += `  stats.totalHandlebarsRules = handlebars${categoryCapitalized}Rules.length;\n`;
        code += `  stats.totalLiquidRules = liquid${categoryCapitalized}Rules.length;\n`;
        code += `  \n`;
        code += `  // Count rule types\n`;
        code += `  // TODO: Add specific rule type counting logic\n`;
        code += `  \n`;
        code += `  return stats;\n`;
        code += `}\n\n`;

        // 规则查找函数
        code += `/**\n`;
        code += ` * Get Handlebars ${category} rule by name\n`;
        code += ` * @param ruleName - Rule name to find\n`;
        code += ` * @returns Rule replacement or empty string if not found\n`;
        code += ` */\n`;
        code += `export function getHandlebars${categoryCapitalized}RuleByName(ruleName: string): string {\n`;
        code += `  for (let i = 0; i < handlebars${categoryCapitalized}Rules.length; i++) {\n`;
        code += `    if (handlebars${categoryCapitalized}Rules[i].name === ruleName) {\n`;
        code += `      return handlebars${categoryCapitalized}Rules[i].replacement;\n`;
        code += `    }\n`;
        code += `  }\n`;
        code += `  return "";\n`;
        code += `}\n\n`;

        code += `/**\n`;
        code += ` * Get Liquid ${category} rule by name\n`;
        code += ` * @param ruleName - Rule name to find\n`;
        code += ` * @returns Rule replacement or empty string if not found\n`;
        code += ` */\n`;
        code += `export function getLiquid${categoryCapitalized}RuleByName(ruleName: string): string {\n`;
        code += `  for (let i = 0; i < liquid${categoryCapitalized}Rules.length; i++) {\n`;
        code += `    if (liquid${categoryCapitalized}Rules[i].name === ruleName) {\n`;
        code += `      return liquid${categoryCapitalized}Rules[i].replacement;\n`;
        code += `    }\n`;
        code += `  }\n`;
        code += `  return "";\n`;
        code += `}\n\n`;

        // 规则计数函数
        code += `/**\n`;
        code += ` * Get total number of Handlebars ${category} rules\n`;
        code += ` * @returns Total count of Handlebars ${category} rules\n`;
        code += ` */\n`;
        code += `export function getHandlebars${categoryCapitalized}RuleCount(): number {\n`;
        code += `  return handlebars${categoryCapitalized}Rules.length;\n`;
        code += `}\n\n`;

        code += `/**\n`;
        code += ` * Get total number of Liquid ${category} rules\n`;
        code += ` * @returns Total count of Liquid ${category} rules\n`;
        code += ` */\n`;
        code += `export function getLiquid${categoryCapitalized}RuleCount(): number {\n`;
        code += `  return liquid${categoryCapitalized}Rules.length;\n`;
        code += `}\n`;

        return code;
    }

    /**
     * 生成转换函数
     */
    generateTransformFunctions(categoryCapitalized) {
        const category = categoryCapitalized.toLowerCase();

        let code = `// ================== ${categoryCapitalized} Transformation Functions ==================\n\n`;

        // Handlebars 转换函数
        code += `/**\n`;
        code += ` * Apply Handlebars ${category} transformations\n`;
        code += ` * @param input - Handlebars template content\n`;
        code += ` * @returns Content with ${category} transformations applied\n`;
        code += ` */\n`;
        code += `export function applyHandlebars${categoryCapitalized}Transforms(input: string): string {\n`;
        code += `  // Handle null input\n`;
        code += `  if (input === null) {\n`;
        code += `    return "";\n`;
        code += `  }\n\n`;
        code += `  let output = input;\n\n`;
        code += `  // Apply ${category} transformations\n`;
        code += `  // TODO: Implement specific transformation logic\n`;
        code += `  \n`;
        code += `  return output;\n`;
        code += `}\n\n`;

        // Liquid 转换函数
        code += `/**\n`;
        code += ` * Apply Liquid ${category} transformations\n`;
        code += ` * @param input - Liquid template content\n`;
        code += ` * @returns Content with ${category} transformations applied\n`;
        code += ` */\n`;
        code += `export function applyLiquid${categoryCapitalized}Transforms(input: string): string {\n`;
        code += `  // Handle null input\n`;
        code += `  if (input === null) {\n`;
        code += `    return "";\n`;
        code += `  }\n\n`;
        code += `  let output = input;\n\n`;
        code += `  // Apply ${category} transformations\n`;
        code += `  // TODO: Implement specific transformation logic\n`;
        code += `  \n`;
        code += `  return output;\n`;
        code += `}\n\n`;

        // 辅助函数
        code += `// ================== Helper Functions ==================\n\n`;
        code += `/**\n`;
        code += ` * Simple string replacement function\n`;
        code += ` * @param input - Input string\n`;
        code += ` * @param search - String to search for\n`;
        code += ` * @param replacement - Replacement string\n`;
        code += ` * @returns String with replacements\n`;
        code += ` */\n`;
        code += `function simpleReplace(input: string, search: string, replacement: string): string {\n`;
        code += `  let result = input;\n`;
        code += `  let index = result.indexOf(search);\n\n`;
        code += `  while (index !== -1) {\n`;
        code += `    result = result.substring(0, index) + replacement + result.substring(index + search.length);\n`;
        code += `    index = result.indexOf(search, index + replacement.length);\n`;
        code += `  }\n\n`;
        code += `  return result;\n`;
        code += `}\n`;

        return code;
    }

    /**
     * 验证生成的代码
     */
    async validateGeneratedCode() {
        logInfo('🔍 Validating generated code...');

        try {
            const categories = this.getCategoriesToProcess();
            let allValid = true;

            for (const category of categories) {
                const isValid = await this.validateCategoryCode(category);
                if (!isValid) {
                    allValid = false;
                }
            }

            if (allValid) {
                logSuccess('✅ All generated code validation passed');
            } else {
                logError('❌ Some validation checks failed');
            }

        } catch (error) {
            logError(`Validation failed: ${error.message}`);
        }
    }

    /**
     * 验证指定类别的代码
     */
    async validateCategoryCode(category) {
        try {
            const outputFile = path.join(this.outputDir, `${category}.ts`);

            if (!fs.existsSync(outputFile)) {
                logError(`Generated file not found: ${outputFile}`);
                return false;
            }

            // 检查文件内容
            const content = fs.readFileSync(outputFile, 'utf8');

            // 基本语法检查
            if (!this.validateTypeScriptSyntax(content, category)) {
                return false;
            }

            // 规则数量检查
            if (!this.validateRuleCount(content, category)) {
                return false;
            }

            logSuccess(`✅ Validation passed for ${category}`);
            return true;

        } catch (error) {
            logError(`Validation failed for ${category}: ${error.message}`);
            return false;
        }
    }

    /**
     * 验证 TypeScript 语法
     */
    validateTypeScriptSyntax(content, category) {
        // 检查必要的导出函数
        const requiredExports = [
            `initialize${this.capitalize(category)}Rules`,
            `get${this.capitalize(category)}Stats`,
            `applyHandlebars${this.capitalize(category)}Transforms`,
            `applyLiquid${this.capitalize(category)}Transforms`
        ];

        for (const exportName of requiredExports) {
            if (!content.includes(`export function ${exportName}`)) {
                logError(`Missing required export: ${exportName}`);
                return false;
            }
        }

        // 检查类定义
        const className = `${this.capitalize(category)}Rule`;
        if (!content.includes(`class ${className}`)) {
            logError(`Missing required class: ${className}`);
            return false;
        }

        return true;
    }

    /**
     * 验证规则数量
     */
    validateRuleCount(content, category) {
        // 简单的规则数量检查
        const handlebarsMatches = content.match(/handlebars\w+Rules\.push/g);
        const liquidMatches = content.match(/liquid\w+Rules\.push/g);

        const handlebarsCount = handlebarsMatches ? handlebarsMatches.length : 0;
        const liquidCount = liquidMatches ? liquidMatches.length : 0;

        logInfo(`Generated rules count - Handlebars: ${handlebarsCount}, Liquid: ${liquidCount}`);

        if (handlebarsCount === 0 && liquidCount === 0) {
            logWarning(`No rules generated for ${category}`);
        }

        return true;
    }

    /**
     * 启动监听模式
     */
    startWatchMode() {
        logInfo('👀 Starting watch mode...');

        const chokidar = require('chokidar');

        // 监听规则文件变化
        const watchPaths = [
            path.join(this.frontendRulesDir, '**/*.json')
        ];

        const watcher = chokidar.watch(watchPaths, {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true
        });

        let debounceTimer = null;

        watcher.on('change', (filePath) => {
            logInfo(`📝 Rules file changed: ${path.relative(this.frontendRulesDir, filePath)}`);

            // 防抖处理
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            debounceTimer = setTimeout(async () => {
                try {
                    await this.regenerateRules();
                    logSuccess('🔄 Rules regenerated successfully');
                } catch (error) {
                    logError(`Failed to regenerate rules: ${error.message}`);
                }
            }, 1000);
        });

        logSuccess('👀 Watch mode started. Press Ctrl+C to stop.');

        // 优雅退出
        process.on('SIGINT', () => {
            logInfo('🛑 Stopping watch mode...');
            watcher.close();
            process.exit(0);
        });
    }

    /**
     * 重新生成规则
     */
    async regenerateRules() {
        const categories = this.getCategoriesToProcess();

        for (const category of categories) {
            await this.generateCategoryRules(category);
        }

        if (this.options.validate) {
            await this.validateGeneratedCode();
        }
    }
}

// 命令行参数解析
function parseArguments() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        switch (arg) {
            case '--category':
                options.category = args[++i];
                break;
            case '--output':
                options.outputDir = args[++i];
                break;
            case '--validate':
                options.validate = true;
                break;
            case '--watch':
                options.watch = true;
                break;
            case '--help':
                showHelp();
                process.exit(0);
                break;
            default:
                if (arg.startsWith('--')) {
                    logError(`Unknown option: ${arg}`);
                    process.exit(1);
                }
        }
    }
    
    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`
规则生成脚本 - 统一规则源实现

使用方法:
  node generate-rules.js [options]

选项:
  --category <name>  只生成指定类别的规则
  --output <dir>     指定输出目录 (默认: ../src/rules/)
  --validate         验证生成的代码与原始规则的一致性
  --watch            监听规则文件变化并自动重新生成
  --help             显示此帮助信息

支持的类别:
  ${CONFIG.SUPPORTED_CATEGORIES.join(', ')}

示例:
  node generate-rules.js                    # 生成所有规则
  node generate-rules.js --category assignment  # 只生成 assignment 规则
  node generate-rules.js --validate         # 生成并验证规则
  node generate-rules.js --watch            # 监听模式
`);
}

// 主入口
if (require.main === module) {
    const options = parseArguments();
    const generator = new RulesGenerator(options);
    generator.run().catch(error => {
        logError(`Fatal error: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { RulesGenerator, CONFIG };
