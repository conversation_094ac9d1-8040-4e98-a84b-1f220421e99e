#!/usr/bin/env node

/**
 * 规则验证和测试脚本
 * 验证生成的 TypeScript 规则代码与原始 JSON 规则的一致性
 * 
 * 使用方法:
 * node test-rules.js [options]
 * 
 * 选项:
 * --category <name>  只测试指定类别的规则
 * --verbose          显示详细输出
 * --fix              自动修复发现的问题
 * --help             显示帮助信息
 */

const fs = require('fs');
const path = require('path');
const { RulesGenerator, CONFIG } = require('./generate-rules.js');

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) { log(`ℹ️  ${message}`, 'blue'); }
function logSuccess(message) { log(`✅ ${message}`, 'green'); }
function logWarning(message) { log(`⚠️  ${message}`, 'yellow'); }
function logError(message) { log(`❌ ${message}`, 'red'); }

class RulesTester {
    constructor(options = {}) {
        this.options = {
            category: options.category || null,
            verbose: options.verbose || false,
            fix: options.fix || false,
            ...options
        };
        
        this.scriptDir = __dirname;
        this.frontendRulesDir = path.resolve(this.scriptDir, CONFIG.FRONTEND_RULES_DIR);
        this.outputDir = path.resolve(this.scriptDir, CONFIG.OUTPUT_DIR);
        this.generator = new RulesGenerator();
        
        this.testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            categories: {}
        };
    }
    
    /**
     * 运行所有测试
     */
    async run() {
        try {
            logInfo('🧪 Starting rules validation tests...');
            
            const categories = this.getCategoriesToTest();
            
            for (const category of categories) {
                await this.testCategory(category);
            }
            
            this.printSummary();
            
            if (this.testResults.failed > 0) {
                process.exit(1);
            }
            
        } catch (error) {
            logError(`Test execution failed: ${error.message}`);
            process.exit(1);
        }
    }
    
    /**
     * 获取要测试的类别列表
     */
    getCategoriesToTest() {
        if (this.options.category) {
            if (!CONFIG.SUPPORTED_CATEGORIES.includes(this.options.category)) {
                throw new Error(`Unsupported category: ${this.options.category}`);
            }
            return [this.options.category];
        }
        
        return CONFIG.SUPPORTED_CATEGORIES;
    }
    
    /**
     * 测试指定类别
     */
    async testCategory(category) {
        logInfo(`Testing category: ${category}`);
        
        const categoryResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };
        
        try {
            // 测试1: 检查生成的文件是否存在
            const fileExistsResult = this.testGeneratedFileExists(category);
            categoryResults.tests.push(fileExistsResult);
            
            if (fileExistsResult.passed) {
                categoryResults.passed++;
                
                // 测试2: 检查规则数量一致性
                const ruleCountResult = await this.testRuleCount(category);
                categoryResults.tests.push(ruleCountResult);
                
                if (ruleCountResult.passed) {
                    categoryResults.passed++;
                } else {
                    categoryResults.failed++;
                }
                
                // 测试3: 检查规则内容一致性
                const ruleContentResult = await this.testRuleContent(category);
                categoryResults.tests.push(ruleContentResult);
                
                if (ruleContentResult.passed) {
                    categoryResults.passed++;
                } else if (ruleContentResult.warning) {
                    categoryResults.warnings++;
                } else {
                    categoryResults.failed++;
                }
                
                // 测试4: 检查 TypeScript 语法
                const syntaxResult = this.testTypeScriptSyntax(category);
                categoryResults.tests.push(syntaxResult);
                
                if (syntaxResult.passed) {
                    categoryResults.passed++;
                } else {
                    categoryResults.failed++;
                }
                
            } else {
                categoryResults.failed++;
            }
            
        } catch (error) {
            logError(`Error testing category ${category}: ${error.message}`);
            categoryResults.failed++;
            categoryResults.tests.push({
                name: 'Category Test Execution',
                passed: false,
                error: error.message
            });
        }
        
        // 更新总体结果
        this.testResults.passed += categoryResults.passed;
        this.testResults.failed += categoryResults.failed;
        this.testResults.warnings += categoryResults.warnings;
        this.testResults.categories[category] = categoryResults;
        
        // 显示类别结果
        if (categoryResults.failed > 0) {
            logError(`${category}: ${categoryResults.failed} failed, ${categoryResults.passed} passed`);
        } else if (categoryResults.warnings > 0) {
            logWarning(`${category}: ${categoryResults.warnings} warnings, ${categoryResults.passed} passed`);
        } else {
            logSuccess(`${category}: All ${categoryResults.passed} tests passed`);
        }
        
        // 详细输出
        if (this.options.verbose) {
            for (const test of categoryResults.tests) {
                const status = test.passed ? '✅' : test.warning ? '⚠️' : '❌';
                log(`  ${status} ${test.name}`, test.passed ? 'green' : test.warning ? 'yellow' : 'red');
                if (test.error && this.options.verbose) {
                    log(`    Error: ${test.error}`, 'red');
                }
                if (test.details && this.options.verbose) {
                    log(`    Details: ${test.details}`, 'cyan');
                }
            }
        }
    }
    
    /**
     * 测试生成的文件是否存在
     */
    testGeneratedFileExists(category) {
        const outputFile = path.join(this.outputDir, `${category}.ts`);
        const exists = fs.existsSync(outputFile);
        
        return {
            name: 'Generated File Exists',
            passed: exists,
            error: exists ? null : `Generated file not found: ${outputFile}`,
            details: exists ? `File found: ${outputFile}` : null
        };
    }
    
    /**
     * 测试规则数量一致性
     */
    async testRuleCount(category) {
        try {
            // 加载原始规则数据
            const rulesData = await this.generator.loadCategoryRules(category);
            const expectedHandlebarsCount = rulesData.handlebars.length;
            const expectedLiquidCount = rulesData.liquid.length;
            
            // 分析生成的文件
            const outputFile = path.join(this.outputDir, `${category}.ts`);
            const content = fs.readFileSync(outputFile, 'utf8');
            
            // 计算生成的规则数量
            const handlebarsMatches = content.match(/handlebars\w+Rules\.push/g);
            const liquidMatches = content.match(/liquid\w+Rules\.push/g);
            
            const actualHandlebarsCount = handlebarsMatches ? handlebarsMatches.length : 0;
            const actualLiquidCount = liquidMatches ? liquidMatches.length : 0;
            
            const handlebarsMatch = actualHandlebarsCount === expectedHandlebarsCount;
            const liquidMatch = actualLiquidCount === expectedLiquidCount;
            
            return {
                name: 'Rule Count Consistency',
                passed: handlebarsMatch && liquidMatch,
                error: (!handlebarsMatch || !liquidMatch) ? 
                    `Count mismatch - Expected: HBS=${expectedHandlebarsCount}, Liquid=${expectedLiquidCount}; Actual: HBS=${actualHandlebarsCount}, Liquid=${actualLiquidCount}` : 
                    null,
                details: `HBS: ${actualHandlebarsCount}/${expectedHandlebarsCount}, Liquid: ${actualLiquidCount}/${expectedLiquidCount}`
            };
            
        } catch (error) {
            return {
                name: 'Rule Count Consistency',
                passed: false,
                error: error.message
            };
        }
    }
    
    /**
     * 测试规则内容一致性
     */
    async testRuleContent(category) {
        try {
            // 加载原始规则数据
            const rulesData = await this.generator.loadCategoryRules(category);
            
            // 读取生成的文件
            const outputFile = path.join(this.outputDir, `${category}.ts`);
            const content = fs.readFileSync(outputFile, 'utf8');
            
            let inconsistencies = [];
            
            // 检查 Handlebars 规则
            for (const rule of rulesData.handlebars) {
                if (!content.includes(rule.name)) {
                    inconsistencies.push(`Missing Handlebars rule: ${rule.name}`);
                }
            }
            
            // 检查 Liquid 规则
            for (const rule of rulesData.liquid) {
                if (!content.includes(rule.name)) {
                    inconsistencies.push(`Missing Liquid rule: ${rule.name}`);
                }
            }
            
            const hasInconsistencies = inconsistencies.length > 0;
            
            return {
                name: 'Rule Content Consistency',
                passed: !hasInconsistencies,
                warning: false,
                error: hasInconsistencies ? `Found ${inconsistencies.length} inconsistencies` : null,
                details: hasInconsistencies ? inconsistencies.slice(0, 3).join('; ') + (inconsistencies.length > 3 ? '...' : '') : 'All rules found in generated code'
            };
            
        } catch (error) {
            return {
                name: 'Rule Content Consistency',
                passed: false,
                error: error.message
            };
        }
    }
    
    /**
     * 测试 TypeScript 语法
     */
    testTypeScriptSyntax(category) {
        try {
            const outputFile = path.join(this.outputDir, `${category}.ts`);
            const content = fs.readFileSync(outputFile, 'utf8');
            
            // 基本语法检查
            const syntaxErrors = [];
            
            // 检查类定义
            const className = `${this.capitalize(category)}Rule`;
            if (!content.includes(`class ${className}`)) {
                syntaxErrors.push(`Missing class definition: ${className}`);
            }
            
            // 检查导出函数
            const requiredExports = [
                `initialize${this.capitalize(category)}Rules`,
                `get${this.capitalize(category)}Stats`,
                `applyHandlebars${this.capitalize(category)}Transforms`,
                `applyLiquid${this.capitalize(category)}Transforms`
            ];
            
            for (const exportName of requiredExports) {
                if (!content.includes(`export function ${exportName}`)) {
                    syntaxErrors.push(`Missing export function: ${exportName}`);
                }
            }
            
            // 检查基本的 TypeScript 语法
            if (content.includes('undefined') && !content.includes('// undefined')) {
                syntaxErrors.push('Found undefined values in generated code');
            }
            
            return {
                name: 'TypeScript Syntax',
                passed: syntaxErrors.length === 0,
                error: syntaxErrors.length > 0 ? syntaxErrors.join('; ') : null,
                details: syntaxErrors.length === 0 ? 'All syntax checks passed' : `${syntaxErrors.length} syntax issues found`
            };
            
        } catch (error) {
            return {
                name: 'TypeScript Syntax',
                passed: false,
                error: error.message
            };
        }
    }
    
    /**
     * 打印测试摘要
     */
    printSummary() {
        console.log('\n' + '='.repeat(60));
        log('📊 Test Summary', 'cyan');
        console.log('='.repeat(60));
        
        log(`Total Tests: ${this.testResults.passed + this.testResults.failed}`, 'blue');
        log(`Passed: ${this.testResults.passed}`, 'green');
        log(`Failed: ${this.testResults.failed}`, this.testResults.failed > 0 ? 'red' : 'green');
        log(`Warnings: ${this.testResults.warnings}`, this.testResults.warnings > 0 ? 'yellow' : 'green');
        
        console.log('\nCategory Results:');
        for (const [category, results] of Object.entries(this.testResults.categories)) {
            const status = results.failed > 0 ? '❌' : results.warnings > 0 ? '⚠️' : '✅';
            log(`  ${status} ${category}: ${results.passed} passed, ${results.failed} failed, ${results.warnings} warnings`);
        }
        
        if (this.testResults.failed === 0) {
            log('\n🎉 All tests passed!', 'green');
        } else {
            log(`\n💥 ${this.testResults.failed} tests failed!`, 'red');
        }
    }
    
    /**
     * 工具函数：首字母大写
     */
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// 命令行参数解析
function parseArguments() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        switch (arg) {
            case '--category':
                options.category = args[++i];
                break;
            case '--verbose':
                options.verbose = true;
                break;
            case '--fix':
                options.fix = true;
                break;
            case '--help':
                showHelp();
                process.exit(0);
                break;
            default:
                if (arg.startsWith('--')) {
                    logError(`Unknown option: ${arg}`);
                    process.exit(1);
                }
        }
    }
    
    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`
规则验证和测试脚本

使用方法:
  node test-rules.js [options]

选项:
  --category <name>  只测试指定类别的规则
  --verbose          显示详细输出
  --fix              自动修复发现的问题
  --help             显示此帮助信息

示例:
  node test-rules.js                    # 测试所有规则
  node test-rules.js --category assignment  # 只测试 assignment 规则
  node test-rules.js --verbose          # 显示详细输出
`);
}

// 主入口
if (require.main === module) {
    const options = parseArguments();
    const tester = new RulesTester(options);
    tester.run().catch(error => {
        logError(`Fatal error: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { RulesTester };
