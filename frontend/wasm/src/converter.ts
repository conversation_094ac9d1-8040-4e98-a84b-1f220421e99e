/**
 * HBS2Sline WASM Converter
 * AssemblyScript implementation of the Handlebars to Sline converter
 */

// Import object mappings
import {
  initializeObjects,
  initializeLiquidObjects,
  getObjectByName,
  getLiquidObjectByName,
  getObjectStats,
  getLiquidObjectStats,
  hasObject,
  hasLiquidObject
} from "./mappings/objects";

// Import filter mappings
import {
  initializeFilters,
  initializeLiquidFilters,
  getFilterByName,
  getLiquidFilterByName,
  getFilterStats,
  getLiquidFilterStats,
  hasFilter,
  hasLiquidFilter,
  getFiltersByCategory,
  getLiquidFiltersByCategory
} from "./mappings/filters";

// Import variable mappings
import {
  initializeVariables,
  getVariableByName,
  getOperatorByName,
  getLiquidVariableByName,
  getLiquidOperatorByName,
  getLiquidSpecialOperator,
  isLoopVariable,
  isConvertibleOperator,
  isLiquidLoopVariable,
  isLiquidConvertibleOperator,
  getVariableStats,
  convertLiquidCondition,
  isLiquidSingleVariable,
  negateLiquidCondition
} from "./mappings/variables";

// Import conditional rules
import {
  initializeConditionalRules,
  getConditionalStats,
  getHandlebarsConditionalRuleByName,
  getLiquidConditionalRuleByName,
  getHandlebarsConditionalRuleCount,
  getLiquidConditionalRuleCount,
  applyHandlebarsConditionalTransforms,
  applyLiquidConditionalTransforms
} from "./rules/conditional";

// Import iteration rules
import {
  initializeIterationRules,
  getIterationStats,
  getHandlebarsIterationRuleByName,
  getLiquidIterationRuleByName,
  getHandlebarsIterationRuleCount,
  getLiquidIterationRuleCount,
  applyHandlebarsIterationTransforms,
  applyLiquidIterationTransforms
} from "./rules/iteration";

// Import assignment rules
import {
  initializeAssignmentRules,
  getAssignmentStats,
  getHandlebarsAssignmentRuleByName,
  getLiquidAssignmentRuleByName,
  getHandlebarsAssignmentRuleCount,
  getLiquidAssignmentRuleCount,
  applyHandlebarsAssignmentTransforms,
  applyLiquidAssignmentTransforms
} from "./rules/assignment";

// Import include rules
import {
  initializeIncludeRules,
  getIncludeStats,
  getHandlebarsIncludeRuleByName,
  getLiquidIncludeRuleByName,
  getHandlebarsIncludeRuleCount,
  getLiquidIncludeRuleCount,
  applyHandlebarsIncludeTransforms,
  applyLiquidIncludeTransforms
} from "./rules/include";

// Import comment rules
import {
  initializeCommentRules,
  getCommentStats,
  getHandlebarsCommentRuleByName,
  getLiquidCommentRuleByName,
  getHandlebarsCommentRuleCount,
  getLiquidCommentRuleCount,
  applyHandlebarsCommentTransforms,
  applyLiquidCommentTransforms
} from "./rules/comment";

// Import output rules
import {
  initializeOutputRules,
  getOutputStats,
  getHandlebarsOutputRuleByName,
  getLiquidOutputRuleByName,
  getHandlebarsOutputRuleCount,
  getLiquidOutputRuleCount,
  applyHandlebarsOutputTransforms,
  applyLiquidOutputTransforms
} from "./rules/output";

/**
 * Initialize the converter system
 */
export function initializeConverter(): void {
  initializeObjects();
  initializeLiquidObjects();
  initializeFilters();
  initializeLiquidFilters();
  initializeVariables();
  initializeConditionalRules();
  initializeIterationRules();
  initializeAssignmentRules();
  initializeIncludeRules();
  initializeCommentRules();
  initializeOutputRules();
}

/**
 * Basic string conversion function - entry point for WASM module
 * @param input - Handlebars template string
 * @returns Converted Sline template string
 */
export function convertHbsToSline(input: string = ""): string {
  // Initialize if not already done
  initializeConverter();

  // Handle null input
  if (input === null) {
    return "";
  }

  // Basic implementation - will be expanded in subsequent tasks
  if (input.length === 0) {
    return "";
  }

  // For now, just return the input with a simple transformation
  // This will be replaced with the full conversion logic
  let output = input;

  // Basic test transformation to verify WASM is working
  // Note: Using simple string operations instead of regex for now
  output = simpleReplace(output, "{{ #if ", "{{#if ");
  output = simpleReplace(output, "{{ /if }}", "{{/if}}");

  // Apply object mappings to simple cases
  output = applyObjectMappings(output);

  // Apply filter mappings
  output = applyFilterMappings(output);

  // Apply variable mappings
  output = applyVariableMappings(output);

  // Apply conditional transformations
  output = applyHandlebarsConditionalTransforms(output);

  // Apply iteration transformations
  output = applyHandlebarsIterationTransforms(output);

  // Apply assignment transformations
  output = applyHandlebarsAssignmentTransforms(output);

  // Apply include transformations
  output = applyHandlebarsIncludeTransforms(output);

  // Apply comment transformations
  output = applyHandlebarsCommentTransforms(output);

  // Apply output transformations
  output = applyHandlebarsOutputTransforms(output);

  return output;
}

/**
 * Convert Liquid template to Sline
 * @param input - Liquid template string
 * @returns Converted Sline template string
 */
export function convertLiquidToSline(input: string = ""): string {
  // Initialize if not already done
  initializeConverter();

  // Handle null input
  if (input === null) {
    return "";
  }

  if (input.length === 0) {
    return "";
  }

  let output = input;

  // Apply Liquid object mappings
  output = applyLiquidObjectMappings(output);

  // Apply Liquid filter mappings
  output = applyLiquidFilterMappings(output);

  // Apply Liquid variable mappings
  output = applyLiquidVariableMappings(output);

  // Apply Liquid conditional transformations
  output = applyLiquidConditionalTransforms(output);

  // Apply Liquid iteration transformations
  output = applyLiquidIterationTransforms(output);

  // Apply Liquid assignment transformations
  output = applyLiquidAssignmentTransforms(output);

  // Apply Liquid include transformations
  output = applyLiquidIncludeTransforms(output);

  // Apply Liquid comment transformations
  output = applyLiquidCommentTransforms(output);

  // Apply Liquid output transformations
  output = applyLiquidOutputTransforms(output);

  return output;
}

/**
 * Simple string replacement function (placeholder for regex)
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns Modified string
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);
  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }
  return result;
}

/**
 * Get version information
 * @returns Version number (major * 10000 + minor * 100 + patch)
 */
export function getVersion(): i32 {
  return 10000; // 1.0.0 as integer (1*10000 + 0*100 + 0)
}

/**
 * Test function to verify WASM module is working
 * @param a - First number
 * @param b - Second number
 * @returns Sum of a and b
 */
export function add(a: number, b: number): number {
  return a + b;
}

/**
 * Memory allocation helper for string operations
 * @param size - Size in bytes
 * @returns Pointer to allocated memory
 */
export function allocate(size: number): number {
  return size > 0 ? 1 : 0; // Simplified for now
}

/**
 * Memory deallocation helper
 * @param ptr - Pointer to memory to free
 */
export function deallocate(ptr: number): void {
  // Simplified for now - just mark as used
  if (ptr > 0) {
    // Memory deallocation would happen here
  }
}

/**
 * Apply object mappings to template content
 * @param input - Template content
 * @returns Content with object mappings applied
 */
function applyObjectMappings(input: string): string {
  let output = input;

  // Simple object mapping for common cases
  // This is a basic implementation - will be enhanced with proper parsing
  output = simpleReplace(output, "{{ shop.name }}", "{{ " + getObjectByName("shop.name") + " }}");
  output = simpleReplace(output, "{{ product.title }}", "{{ " + getObjectByName("product.title") + " }}");
  output = simpleReplace(output, "{{ product.price }}", "{{ " + getObjectByName("product.price") + " }}");
  output = simpleReplace(output, "{{ collection.title }}", "{{ " + getObjectByName("collection.title") + " }}");
  output = simpleReplace(output, "{{ customer.email }}", "{{ " + getObjectByName("customer.email") + " }}");
  output = simpleReplace(output, "{{ cart.item_count }}", "{{ " + getObjectByName("cart.item_count") + " }}");

  return output;
}

/**
 * Apply Liquid object mappings to template content
 * @param input - Liquid template content
 * @returns Content with Liquid object mappings applied
 */
function applyLiquidObjectMappings(input: string): string {
  let output = input;

  // Simple Liquid object mapping for common cases
  output = simpleReplace(output, "{{ shop.name }}", "{{ " + getLiquidObjectByName("shop.name") + " }}");
  output = simpleReplace(output, "{{ product.title }}", "{{ " + getLiquidObjectByName("product.title") + " }}");
  output = simpleReplace(output, "{{ product.price }}", "{{ " + getLiquidObjectByName("product.price") + " }}");
  output = simpleReplace(output, "{{ collection.title }}", "{{ " + getLiquidObjectByName("collection.title") + " }}");

  return output;
}

/**
 * Apply filter mappings to template content
 * @param input - Template content
 * @returns Content with filter mappings applied
 */
function applyFilterMappings(input: string): string {
  let output = input;

  // Simple filter mapping for common cases
  // Convert Handlebars helper syntax to Sline filter syntax
  output = simpleReplace(output, "{{ capitalize ", "{{ ");
  output = simpleReplace(output, "{{ upcase ", "{{ ");
  output = simpleReplace(output, "{{ downcase ", "{{ ");
  output = simpleReplace(output, "{{ money ", "{{ ");

  // Apply basic filter transformations
  output = applyBasicFilterTransforms(output);

  return output;
}

/**
 * Apply Liquid filter mappings to template content
 * @param input - Liquid template content
 * @returns Content with Liquid filter mappings applied
 */
function applyLiquidFilterMappings(input: string): string {
  let output = input;

  // Simple Liquid filter mapping for common cases
  // Convert Liquid filter syntax to Sline filter syntax
  output = simpleReplace(output, " | capitalize", "|capitalize()");
  output = simpleReplace(output, " | upcase", "|upcase()");
  output = simpleReplace(output, " | downcase", "|downcase()");
  output = simpleReplace(output, " | money", "|money()");
  output = simpleReplace(output, " | date:", "|date(");

  return output;
}

/**
 * Apply basic filter transformations
 * @param input - Template content
 * @returns Content with basic filter transformations applied
 */
function applyBasicFilterTransforms(input: string): string {
  let output = input;

  // Transform common Handlebars helpers to Sline filters
  // {{ capitalize product.title }} -> {{ product.title|capitalize() }}
  output = transformHelperToFilter(output, "capitalize");
  output = transformHelperToFilter(output, "upcase");
  output = transformHelperToFilter(output, "downcase");
  output = transformHelperToFilter(output, "money");
  output = transformHelperToFilter(output, "escape");
  output = transformHelperToFilter(output, "strip_html");

  return output;
}

/**
 * Transform Handlebars helper to Sline filter
 * @param input - Template content
 * @param helperName - Helper name to transform
 * @returns Content with helper transformed to filter
 */
function transformHelperToFilter(input: string, helperName: string): string {
  let output = input;

  // Simple transformation: {{ helper variable }} -> {{ variable|helper() }}
  const helperPattern = "{{ " + helperName + " ";
  const helperIndex = output.indexOf(helperPattern);

  if (helperIndex !== -1) {
    const startIndex = helperIndex;
    const endIndex = output.indexOf(" }}", startIndex);

    if (endIndex !== -1) {
      const fullMatch = output.substring(startIndex, endIndex + 3);
      const variable = fullMatch.substring(helperPattern.length, fullMatch.length - 3).trim();
      const replacement = "{{ " + variable + "|" + getFilterByName(helperName) + "() }}";
      output = simpleReplace(output, fullMatch, replacement);
    }
  }

  return output;
}

/**
 * Get object mapping statistics
 * @returns JSON string with statistics
 */
export function getObjectMappingStats(): string {
  const stats = getObjectStats();
  const liquidStats = getLiquidObjectStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_objects\":" + stats.totalObjects.toString() + ",";
  result += "\"handlebars_categories\":" + stats.categories.toString() + ",";
  result += "\"liquid_objects\":" + liquidStats.totalObjects.toString() + ",";
  result += "\"liquid_categories\":" + liquidStats.categories.toString();
  result += "}";

  return result;
}

/**
 * Get filter mapping statistics
 * @returns JSON string with filter statistics
 */
export function getFilterMappingStats(): string {
  const stats = getFilterStats();
  const liquidStats = getLiquidFilterStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_filters\":" + stats.totalFilters.toString() + ",";
  result += "\"handlebars_filter_categories\":" + stats.categories.toString() + ",";
  result += "\"liquid_filters\":" + liquidStats.totalFilters.toString() + ",";
  result += "\"liquid_filter_categories\":" + liquidStats.categories.toString();
  result += "}";

  return result;
}

/**
 * Get comprehensive mapping statistics
 * @returns JSON string with all mapping statistics
 */
export function getAllMappingStats(): string {
  const objectStats = getObjectStats();
  const liquidObjectStats = getLiquidObjectStats();
  const filterStats = getFilterStats();
  const liquidFilterStats = getLiquidFilterStats();
  const variableStats = getVariableStats();
  const conditionalStats = getConditionalStats();
  const iterationStats = getIterationStats();
  const assignmentStats = getAssignmentStats();
  const includeStats = getIncludeStats();
  const commentStats = getCommentStats();
  const outputStats = getOutputStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"objects\":{";
  result += "\"handlebars\":" + objectStats.totalObjects.toString() + ",";
  result += "\"liquid\":" + liquidObjectStats.totalObjects.toString();
  result += "},";
  result += "\"filters\":{";
  result += "\"handlebars\":" + filterStats.totalFilters.toString() + ",";
  result += "\"liquid\":" + liquidFilterStats.totalFilters.toString();
  result += "},";
  result += "\"variables\":{";
  result += "\"handlebars_loop\":" + variableStats.totalLoopVariables.toString() + ",";
  result += "\"handlebars_operators\":" + variableStats.totalOperators.toString() + ",";
  result += "\"liquid_variables\":" + variableStats.totalLiquidVariables.toString() + ",";
  result += "\"liquid_operators\":" + variableStats.totalLiquidOperators.toString() + ",";
  result += "\"liquid_special\":" + variableStats.totalSpecialOperators.toString();
  result += "},";
  result += "\"conditional_rules\":{";
  result += "\"handlebars\":" + conditionalStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + conditionalStats.totalLiquidRules.toString() + ",";
  result += "\"if_rules\":" + conditionalStats.ifRules.toString() + ",";
  result += "\"unless_rules\":" + conditionalStats.unlessRules.toString() + ",";
  result += "\"case_rules\":" + conditionalStats.caseRules.toString();
  result += "},";
  result += "\"iteration_rules\":{";
  result += "\"handlebars\":" + iterationStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + iterationStats.totalLiquidRules.toString() + ",";
  result += "\"each_rules\":" + iterationStats.eachRules.toString() + ",";
  result += "\"for_rules\":" + iterationStats.forRules.toString() + ",";
  result += "\"variable_rules\":" + iterationStats.variableRules.toString() + ",";
  result += "\"control_rules\":" + iterationStats.controlRules.toString();
  result += "},";
  result += "\"assignment_rules\":{";
  result += "\"handlebars\":" + assignmentStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + assignmentStats.totalLiquidRules.toString() + ",";
  result += "\"assign_rules\":" + assignmentStats.assignRules.toString() + ",";
  result += "\"capture_rules\":" + assignmentStats.captureRules.toString() + ",";
  result += "\"increment_rules\":" + assignmentStats.incrementRules.toString() + ",";
  result += "\"include_rules\":" + assignmentStats.includeRules.toString();
  result += "},";
  result += "\"include_rules\":{";
  result += "\"handlebars\":" + includeStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + includeStats.totalLiquidRules.toString() + ",";
  result += "\"include_rules\":" + includeStats.includeRules.toString() + ",";
  result += "\"render_rules\":" + includeStats.renderRules.toString() + ",";
  result += "\"section_rules\":" + includeStats.sectionRules.toString() + ",";
  result += "\"layout_rules\":" + includeStats.layoutRules.toString() + ",";
  result += "\"content_rules\":" + includeStats.contentRules.toString() + ",";
  result += "\"yield_rules\":" + includeStats.yieldRules.toString();
  result += "},";
  result += "\"comment_rules\":{";
  result += "\"handlebars\":" + commentStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + commentStats.totalLiquidRules.toString() + ",";
  result += "\"comment_rules\":" + commentStats.commentRules.toString() + ",";
  result += "\"raw_rules\":" + commentStats.rawRules.toString() + ",";
  result += "\"output_rules\":" + commentStats.outputRules.toString() + ",";
  result += "\"warning_rules\":" + commentStats.warningRules.toString();
  result += "},";
  result += "\"output_rules\":{";
  result += "\"handlebars\":" + outputStats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid\":" + outputStats.totalLiquidRules.toString() + ",";
  result += "\"raw_output_rules\":" + outputStats.rawOutputRules.toString() + ",";
  result += "\"comment_rules\":" + outputStats.commentRules.toString() + ",";
  result += "\"variable_output_rules\":" + outputStats.variableOutputRules.toString() + ",";
  result += "\"filter_rules\":" + outputStats.filterRules.toString() + ",";
  result += "\"math_rules\":" + outputStats.mathRules.toString() + ",";
  result += "\"string_rules\":" + outputStats.stringRules.toString();
  result += "},";
  result += "\"categories\":{";
  result += "\"object_handlebars\":" + objectStats.categories.toString() + ",";
  result += "\"object_liquid\":" + liquidObjectStats.categories.toString() + ",";
  result += "\"filter_handlebars\":" + filterStats.categories.toString() + ",";
  result += "\"filter_liquid\":" + liquidFilterStats.categories.toString();
  result += "}";
  result += "}";

  return result;
}

/**
 * Get variable mapping statistics
 * @returns JSON string with variable statistics
 */
export function getVariableMappingStats(): string {
  const stats = getVariableStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_loop_variables\":" + stats.totalLoopVariables.toString() + ",";
  result += "\"handlebars_operators\":" + stats.totalOperators.toString() + ",";
  result += "\"liquid_variables\":" + stats.totalLiquidVariables.toString() + ",";
  result += "\"liquid_operators\":" + stats.totalLiquidOperators.toString() + ",";
  result += "\"liquid_special_operators\":" + stats.totalSpecialOperators.toString();
  result += "}";

  return result;
}

/**
 * Get conditional rule statistics
 * @returns JSON string with conditional rule statistics
 */
export function getConditionalRuleStats(): string {
  const stats = getConditionalStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"if_rules\":" + stats.ifRules.toString() + ",";
  result += "\"unless_rules\":" + stats.unlessRules.toString() + ",";
  result += "\"case_rules\":" + stats.caseRules.toString();
  result += "}";

  return result;
}

/**
 * Get iteration rule statistics
 * @returns JSON string with iteration rule statistics
 */
export function getIterationRuleStats(): string {
  const stats = getIterationStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"each_rules\":" + stats.eachRules.toString() + ",";
  result += "\"for_rules\":" + stats.forRules.toString() + ",";
  result += "\"variable_rules\":" + stats.variableRules.toString() + ",";
  result += "\"control_rules\":" + stats.controlRules.toString();
  result += "}";

  return result;
}

/**
 * Get assignment rule statistics
 * @returns JSON string with assignment rule statistics
 */
export function getAssignmentRuleStats(): string {
  const stats = getAssignmentStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"assign_rules\":" + stats.assignRules.toString() + ",";
  result += "\"capture_rules\":" + stats.captureRules.toString() + ",";
  result += "\"increment_rules\":" + stats.incrementRules.toString() + ",";
  result += "\"include_rules\":" + stats.includeRules.toString();
  result += "}";

  return result;
}

/**
 * Get include rule statistics
 * @returns JSON string with include rule statistics
 */
export function getIncludeRuleStats(): string {
  const stats = getIncludeStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"include_rules\":" + stats.includeRules.toString() + ",";
  result += "\"render_rules\":" + stats.renderRules.toString() + ",";
  result += "\"section_rules\":" + stats.sectionRules.toString() + ",";
  result += "\"layout_rules\":" + stats.layoutRules.toString() + ",";
  result += "\"content_rules\":" + stats.contentRules.toString() + ",";
  result += "\"yield_rules\":" + stats.yieldRules.toString();
  result += "}";

  return result;
}

/**
 * Get comment rule statistics
 * @returns JSON string with comment rule statistics
 */
export function getCommentRuleStats(): string {
  const stats = getCommentStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"comment_rules\":" + stats.commentRules.toString() + ",";
  result += "\"raw_rules\":" + stats.rawRules.toString() + ",";
  result += "\"output_rules\":" + stats.outputRules.toString() + ",";
  result += "\"warning_rules\":" + stats.warningRules.toString();
  result += "}";

  return result;
}

/**
 * Get output rule statistics
 * @returns JSON string with output rule statistics
 */
export function getOutputRuleStats(): string {
  const stats = getOutputStats();

  // Simple JSON-like string construction (AssemblyScript limitation)
  let result = "{";
  result += "\"handlebars_rules\":" + stats.totalHandlebarsRules.toString() + ",";
  result += "\"liquid_rules\":" + stats.totalLiquidRules.toString() + ",";
  result += "\"raw_output_rules\":" + stats.rawOutputRules.toString() + ",";
  result += "\"comment_rules\":" + stats.commentRules.toString() + ",";
  result += "\"variable_output_rules\":" + stats.variableOutputRules.toString() + ",";
  result += "\"filter_rules\":" + stats.filterRules.toString() + ",";
  result += "\"math_rules\":" + stats.mathRules.toString() + ",";
  result += "\"string_rules\":" + stats.stringRules.toString();
  result += "}";

  return result;
}

/**
 * Test object mapping functionality
 * @param objectName - Object name to test
 * @returns Mapped object name
 */
export function testObjectMapping(objectName: string): string {
  initializeConverter();
  return getObjectByName(objectName);
}

/**
 * Test Liquid object mapping functionality
 * @param objectName - Liquid object name to test
 * @returns Mapped Liquid object name
 */
export function testLiquidObjectMapping(objectName: string): string {
  initializeConverter();
  return getLiquidObjectByName(objectName);
}

/**
 * Test filter mapping functionality
 * @param filterName - Filter name to test
 * @returns Mapped filter name
 */
export function testFilterMapping(filterName: string): string {
  initializeConverter();
  return getFilterByName(filterName);
}

/**
 * Test Liquid filter mapping functionality
 * @param filterName - Liquid filter name to test
 * @returns Mapped Liquid filter name
 */
export function testLiquidFilterMapping(filterName: string): string {
  initializeConverter();
  return getLiquidFilterByName(filterName);
}

/**
 * Test filter transformation functionality
 * @param input - Input template with filter
 * @returns Transformed template
 */
export function testFilterTransformation(input: string = ""): string {
  initializeConverter();
  return applyFilterMappings(input);
}

/**
 * Test Liquid filter transformation functionality
 * @param input - Input Liquid template with filter
 * @returns Transformed template
 */
export function testLiquidFilterTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidFilterMappings(input);
}

// ================== Variable Mapping Functions ==================

/**
 * Apply variable mappings to template content
 * @param input - Template content
 * @returns Content with variable mappings applied
 */
function applyVariableMappings(input: string): string {
  let output = input;

  // Apply loop variable mappings
  output = applyLoopVariableTransforms(output);

  // Apply operator mappings
  output = applyOperatorTransforms(output);

  return output;
}

/**
 * Apply Liquid variable mappings to template content
 * @param input - Liquid template content
 * @returns Content with Liquid variable mappings applied
 */
function applyLiquidVariableMappings(input: string): string {
  let output = input;

  // Apply Liquid variable mappings
  output = applyLiquidVariableTransforms(output);

  // Apply Liquid operator mappings
  output = applyLiquidOperatorTransforms(output);

  // Apply condition transformations
  output = applyLiquidConditionTransforms(output);

  return output;
}

/**
 * Apply loop variable transformations
 * @param input - Template content
 * @returns Content with loop variable transformations applied
 */
function applyLoopVariableTransforms(input: string): string {
  let output = input;

  // Transform common loop variables
  output = simpleReplace(output, "@index", getVariableByName("@index"));
  output = simpleReplace(output, "@first", getVariableByName("@first"));
  output = simpleReplace(output, "@last", getVariableByName("@last"));
  output = simpleReplace(output, "@key", getVariableByName("@key"));
  output = simpleReplace(output, "@root", getVariableByName("@root"));
  output = simpleReplace(output, "this", getVariableByName("this"));

  return output;
}

/**
 * Apply operator transformations
 * @param input - Template content
 * @returns Content with operator transformations applied
 */
function applyOperatorTransforms(input: string): string {
  let output = input;

  // Transform operators
  output = simpleReplace(output, "===", getOperatorByName("==="));
  output = simpleReplace(output, "!==", getOperatorByName("!=="));

  return output;
}

/**
 * Apply Liquid variable transformations
 * @param input - Liquid template content
 * @returns Content with Liquid variable transformations applied
 */
function applyLiquidVariableTransforms(input: string): string {
  let output = input;

  // Transform forloop variables
  output = simpleReplace(output, "forloop.index", getLiquidVariableByName("forloop.index"));
  output = simpleReplace(output, "forloop.index0", getLiquidVariableByName("forloop.index0"));
  output = simpleReplace(output, "forloop.first", getLiquidVariableByName("forloop.first"));
  output = simpleReplace(output, "forloop.last", getLiquidVariableByName("forloop.last"));
  output = simpleReplace(output, "forloop.length", getLiquidVariableByName("forloop.length"));

  // Transform special variables
  output = simpleReplace(output, "blank", getLiquidVariableByName("blank"));
  output = simpleReplace(output, "empty", getLiquidVariableByName("empty"));
  output = simpleReplace(output, "nil", getLiquidVariableByName("nil"));

  return output;
}

/**
 * Apply Liquid operator transformations
 * @param input - Liquid template content
 * @returns Content with Liquid operator transformations applied
 */
function applyLiquidOperatorTransforms(input: string): string {
  let output = input;

  // Transform logical operators
  output = simpleReplace(output, " and ", " " + getLiquidOperatorByName("and") + " ");
  output = simpleReplace(output, " or ", " " + getLiquidOperatorByName("or") + " ");

  // Transform comparison operators
  output = simpleReplace(output, "<>", getLiquidOperatorByName("<>"));
  output = simpleReplace(output, "contains", getLiquidOperatorByName("contains"));

  return output;
}

/**
 * Apply Liquid condition transformations
 * @param input - Liquid template content
 * @returns Content with condition transformations applied
 */
function applyLiquidConditionTransforms(input: string): string {
  let output = input;

  // Simple condition transformations
  // Transform .size to |size()
  output = simpleReplace(output, ".size", "|size()");

  return output;
}

// ================== Variable Mapping Test Functions ==================

/**
 * Test variable mapping functionality
 * @param variableName - Variable name to test
 * @returns Mapped variable name
 */
export function testVariableMapping(variableName: string): string {
  initializeConverter();
  return getVariableByName(variableName);
}

/**
 * Test operator mapping functionality
 * @param operator - Operator to test
 * @returns Mapped operator
 */
export function testOperatorMapping(operator: string): string {
  initializeConverter();
  return getOperatorByName(operator);
}

/**
 * Test Liquid variable mapping functionality
 * @param variableName - Liquid variable name to test
 * @returns Mapped Liquid variable name
 */
export function testLiquidVariableMapping(variableName: string): string {
  initializeConverter();
  return getLiquidVariableByName(variableName);
}

/**
 * Test Liquid operator mapping functionality
 * @param operator - Liquid operator to test
 * @returns Mapped Liquid operator
 */
export function testLiquidOperatorMapping(operator: string): string {
  initializeConverter();
  return getLiquidOperatorByName(operator);
}

/**
 * Test variable transformation functionality
 * @param input - Input template with variables
 * @returns Transformed template
 */
export function testVariableTransformation(input: string = ""): string {
  initializeConverter();
  return applyVariableMappings(input);
}

/**
 * Test Liquid variable transformation functionality
 * @param input - Input Liquid template with variables
 * @returns Transformed template
 */
export function testLiquidVariableTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidVariableMappings(input);
}

/**
 * Test Liquid condition conversion functionality
 * @param condition - Input Liquid condition
 * @returns Converted Sline condition
 */
export function testLiquidConditionConversion(condition: string): string {
  initializeConverter();
  return convertLiquidCondition(condition);
}

/**
 * Test Liquid condition negation functionality
 * @param condition - Input Liquid condition
 * @returns Negated Sline condition
 */
export function testLiquidConditionNegation(condition: string): string {
  initializeConverter();
  return negateLiquidCondition(condition);
}

// ================== Conditional Rule Test Functions ==================

/**
 * Test Handlebars conditional rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsConditionalRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsConditionalRuleByName(ruleName);
}

/**
 * Test Liquid conditional rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidConditionalRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidConditionalRuleByName(ruleName);
}

/**
 * Test Handlebars conditional transformation functionality
 * @param input - Input Handlebars template with conditionals
 * @returns Transformed template
 */
export function testHandlebarsConditionalTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsConditionalTransforms(input);
}

/**
 * Test Liquid conditional transformation functionality
 * @param input - Input Liquid template with conditionals
 * @returns Transformed template
 */
export function testLiquidConditionalTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidConditionalTransforms(input);
}

// ================== Iteration Rule Test Functions ==================

/**
 * Test Handlebars iteration rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsIterationRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsIterationRuleByName(ruleName);
}

/**
 * Test Liquid iteration rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidIterationRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidIterationRuleByName(ruleName);
}

/**
 * Test Handlebars iteration transformation functionality
 * @param input - Input Handlebars template with iterations
 * @returns Transformed template
 */
export function testHandlebarsIterationTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsIterationTransforms(input);
}

/**
 * Test Liquid iteration transformation functionality
 * @param input - Input Liquid template with iterations
 * @returns Transformed template
 */
export function testLiquidIterationTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidIterationTransforms(input);
}

// ================== Assignment Rule Test Functions ==================

/**
 * Test Handlebars assignment rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsAssignmentRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsAssignmentRuleByName(ruleName);
}

/**
 * Test Liquid assignment rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidAssignmentRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidAssignmentRuleByName(ruleName);
}

/**
 * Test Handlebars assignment transformation functionality
 * @param input - Input Handlebars template with assignments
 * @returns Transformed template
 */
export function testHandlebarsAssignmentTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsAssignmentTransforms(input);
}

/**
 * Test Liquid assignment transformation functionality
 * @param input - Input Liquid template with assignments
 * @returns Transformed template
 */
export function testLiquidAssignmentTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidAssignmentTransforms(input);
}

// ================== Include Rule Test Functions ==================

/**
 * Test Handlebars include rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsIncludeRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsIncludeRuleByName(ruleName);
}

/**
 * Test Liquid include rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidIncludeRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidIncludeRuleByName(ruleName);
}

/**
 * Test Handlebars include transformation functionality
 * @param input - Input Handlebars template with includes
 * @returns Transformed template
 */
export function testHandlebarsIncludeTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsIncludeTransforms(input);
}

/**
 * Test Liquid include transformation functionality
 * @param input - Input Liquid template with includes
 * @returns Transformed template
 */
export function testLiquidIncludeTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidIncludeTransforms(input);
}

// ================== Comment Rule Test Functions ==================

/**
 * Test Handlebars comment rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsCommentRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsCommentRuleByName(ruleName);
}

/**
 * Test Liquid comment rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidCommentRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidCommentRuleByName(ruleName);
}

/**
 * Test Handlebars comment transformation functionality
 * @param input - Input Handlebars template with comments
 * @returns Transformed template
 */
export function testHandlebarsCommentTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsCommentTransforms(input);
}

/**
 * Test Liquid comment transformation functionality
 * @param input - Input Liquid template with comments
 * @returns Transformed template
 */
export function testLiquidCommentTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidCommentTransforms(input);
}

// ================== Output Rule Test Functions ==================

/**
 * Test Handlebars output rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testHandlebarsOutputRule(ruleName: string = ""): string {
  initializeConverter();
  return getHandlebarsOutputRuleByName(ruleName);
}

/**
 * Test Liquid output rule by name
 * @param ruleName - Rule name to test
 * @returns Rule replacement or empty string
 */
export function testLiquidOutputRule(ruleName: string = ""): string {
  initializeConverter();
  return getLiquidOutputRuleByName(ruleName);
}

/**
 * Test Handlebars output transformation functionality
 * @param input - Input Handlebars template with output
 * @returns Transformed template
 */
export function testHandlebarsOutputTransformation(input: string = ""): string {
  initializeConverter();
  return applyHandlebarsOutputTransforms(input);
}

/**
 * Test Liquid output transformation functionality
 * @param input - Input Liquid template with output
 * @returns Transformed template
 */
export function testLiquidOutputTransformation(input: string = ""): string {
  initializeConverter();
  return applyLiquidOutputTransforms(input);
}
