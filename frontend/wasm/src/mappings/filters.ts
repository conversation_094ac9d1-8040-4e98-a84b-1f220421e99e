/**
 * WASM Filter Mappings for HBS2Sline Converter
 * AssemblyScript implementation of filter mapping system
 * Based on backend/rules/mappings/filters.js
 */

// ================== Filter Mappings Map ==================
// Using Map for efficient lookups in WASM
const filterMappings = new Map<string, string>();

// Initialize Handlebars filter mappings
function initializeFilterMappings(): void {
  // String filters
  filterMappings.set("capitalize", "capitalize");
  filterMappings.set("downcase", "downcase");
  filterMappings.set("upcase", "upcase");
  filterMappings.set("truncate", "truncate");
  filterMappings.set("strip_html", "strip_html");
  filterMappings.set("strip_newlines", "strip_newlines");
  filterMappings.set("newline_to_br", "newline_to_br");
  filterMappings.set("escape", "escape");
  filterMappings.set("url_encode", "url_encode");
  filterMappings.set("url_decode", "url_decode");
  filterMappings.set("strip", "strip");
  filterMappings.set("lstrip", "lstrip");
  filterMappings.set("rstrip", "rstrip");
  
  // Number filters
  filterMappings.set("abs", "abs");
  filterMappings.set("ceil", "ceil");
  filterMappings.set("floor", "floor");
  filterMappings.set("round", "round");
  filterMappings.set("plus", "plus");
  filterMappings.set("minus", "minus");
  filterMappings.set("times", "times");
  filterMappings.set("divided_by", "divided_by");
  filterMappings.set("modulo", "modulo");
  
  // Currency filters
  filterMappings.set("money", "money");
  filterMappings.set("money_with_currency", "money_with_currency");
  filterMappings.set("money_without_currency", "money_without_currency");
  filterMappings.set("money_without_trailing_zeros", "money_without_trailing_zeros");
  
  // Date filters
  filterMappings.set("date", "date");
  
  // Array filters
  filterMappings.set("size", "size");
  filterMappings.set("first", "first");
  filterMappings.set("last", "last");
  filterMappings.set("join", "join");
  filterMappings.set("sort", "sort");
  filterMappings.set("sort_natural", "sort_natural");
  filterMappings.set("reverse", "reverse");
  filterMappings.set("uniq", "uniq");
  filterMappings.set("map", "map");
  filterMappings.set("where", "where");
  filterMappings.set("slice", "slice");
  
  // URL filters
  filterMappings.set("asset_url", "asset_url");
  filterMappings.set("file_url", "file_url");
  filterMappings.set("img_url", "img_url");
  filterMappings.set("url", "url");
  
  // HTML filters
  filterMappings.set("link_to", "link_to");
  filterMappings.set("script_tag", "script_tag");
  filterMappings.set("stylesheet_tag", "stylesheet_tag");
  filterMappings.set("img_tag", "img_tag");
  
  // Other filters
  filterMappings.set("default", "default");
  filterMappings.set("json", "json");
  filterMappings.set("raw", "raw");
  filterMappings.set("base64_encode", "base64_encode");
  filterMappings.set("base64_decode", "base64_decode");
  filterMappings.set("md5", "md5");
  filterMappings.set("sha1", "sha1");
  filterMappings.set("sha256", "sha256");
  filterMappings.set("hmac_sha1", "hmac_sha1");
  filterMappings.set("hmac_sha256", "hmac_sha256");
}

// ================== Filter Categories ==================
class FilterCategory {
  name: string;
  description: string;
  filters: string[];
  
  constructor(name: string, description: string, filters: string[]) {
    this.name = name;
    this.description = description;
    this.filters = filters;
  }
}

const filterCategories: FilterCategory[] = [];

function initializeFilterCategories(): void {
  filterCategories.push(new FilterCategory(
    "字符串过滤器",
    "用于处理字符串内容的过滤器",
    ["capitalize", "downcase", "upcase", "truncate", "strip_html", "strip_newlines", 
     "newline_to_br", "escape", "url_encode", "url_decode", "strip", "lstrip", "rstrip"]
  ));
  
  filterCategories.push(new FilterCategory(
    "数字过滤器",
    "用于数学运算和数字处理的过滤器",
    ["abs", "ceil", "floor", "round", "plus", "minus", "times", "divided_by", "modulo"]
  ));
  
  filterCategories.push(new FilterCategory(
    "货币过滤器",
    "用于格式化货币显示的过滤器",
    ["money", "money_with_currency", "money_without_currency", "money_without_trailing_zeros"]
  ));
  
  filterCategories.push(new FilterCategory(
    "日期过滤器",
    "用于处理日期和时间的过滤器",
    ["date"]
  ));
  
  filterCategories.push(new FilterCategory(
    "数组过滤器",
    "用于处理数组和集合的过滤器",
    ["size", "first", "last", "join", "sort", "sort_natural", "reverse", "uniq", "map", "where", "slice"]
  ));
  
  filterCategories.push(new FilterCategory(
    "URL 过滤器",
    "用于生成和处理 URL 的过滤器",
    ["asset_url", "file_url", "img_url", "url"]
  ));
  
  filterCategories.push(new FilterCategory(
    "HTML 过滤器",
    "用于生成 HTML 标签的过滤器",
    ["link_to", "script_tag", "stylesheet_tag", "img_tag"]
  ));
  
  filterCategories.push(new FilterCategory(
    "编码过滤器",
    "用于数据编码和解码的过滤器",
    ["base64_encode", "base64_decode", "md5", "sha1", "sha256", "hmac_sha1", "hmac_sha256"]
  ));
  
  filterCategories.push(new FilterCategory(
    "其他过滤器",
    "其他实用的过滤器",
    ["default", "json", "raw"]
  ));
}

// ================== Statistics Class ==================
class FilterStats {
  totalFilters: number;
  categories: number;
  filtersByCategory: Map<string, number>;

  constructor() {
    this.totalFilters = 0;
    this.categories = 0;
    this.filtersByCategory = new Map<string, number>();
  }
}

// ================== Exported Functions ==================

/**
 * Initialize the filter mapping system
 */
export function initializeFilters(): void {
  initializeFilterMappings();
  initializeFilterCategories();
}

/**
 * Get mapped filter name by original name
 * @param filterName - Original filter name
 * @returns Mapped filter name or original if not found
 */
export function getFilterByName(filterName: string): string {
  const mapped = filterMappings.get(filterName);
  if (mapped !== null) {
    return mapped;
  }
  return filterName;
}

/**
 * Get filter statistics
 * @returns Filter statistics
 */
export function getFilterStats(): FilterStats {
  const stats = new FilterStats();
  stats.totalFilters = filterMappings.size;
  stats.categories = filterCategories.length;
  
  // Calculate filters by category
  for (let i = 0; i < filterCategories.length; i++) {
    const category = filterCategories[i];
    stats.filtersByCategory.set(category.name, category.filters.length);
  }
  
  return stats;
}

/**
 * Check if filter exists in mappings
 * @param filterName - Filter name to check
 * @returns True if filter exists in mappings
 */
export function hasFilter(filterName: string): boolean {
  return filterMappings.has(filterName);
}

/**
 * Get filters by category name
 * @param categoryName - Category name
 * @returns Array of filter names in the category
 */
export function getFiltersByCategory(categoryName: string): string[] {
  for (let i = 0; i < filterCategories.length; i++) {
    const category = filterCategories[i];
    if (category.name === categoryName) {
      return category.filters;
    }
  }
  return [];
}

/**
 * Get total number of mapped filters
 * @returns Total count of mapped filters
 */
export function getFilterCount(): number {
  return filterMappings.size;
}

/**
 * Get category count
 * @returns Number of filter categories
 */
export function getFilterCategoryCount(): number {
  return filterCategories.length;
}

/**
 * Get category name by index
 * @param index - Category index
 * @returns Category name or empty string if index out of bounds
 */
export function getFilterCategoryName(index: number): string {
  if (index >= 0 && index < filterCategories.length) {
    return filterCategories[index].name;
  }
  return "";
}

/**
 * Get category description by index
 * @param index - Category index
 * @returns Category description or empty string if index out of bounds
 */
export function getFilterCategoryDescription(index: number): string {
  if (index >= 0 && index < filterCategories.length) {
    return filterCategories[index].description;
  }
  return "";
}

// ================== Liquid Filter Mappings ==================
// Separate mapping for Liquid filters
const liquidFilterMappings = new Map<string, string>();

function initializeLiquidFilterMappings(): void {
  // String filters
  liquidFilterMappings.set("capitalize", "capitalize");
  liquidFilterMappings.set("downcase", "downcase");
  liquidFilterMappings.set("upcase", "upcase");
  liquidFilterMappings.set("escape", "escape");
  liquidFilterMappings.set("escape_once", "escape_once");
  liquidFilterMappings.set("newline_to_br", "newline_to_br");
  liquidFilterMappings.set("strip_html", "strip_html");
  liquidFilterMappings.set("strip_newlines", "strip_newlines");
  liquidFilterMappings.set("truncate", "truncate");
  liquidFilterMappings.set("truncatewords", "truncatewords");
  liquidFilterMappings.set("lstrip", "trim_left");
  liquidFilterMappings.set("rstrip", "trim_right");
  liquidFilterMappings.set("strip", "trim");
  liquidFilterMappings.set("replace", "replace");
  liquidFilterMappings.set("replace_first", "replace_first");
  liquidFilterMappings.set("remove", "remove");
  liquidFilterMappings.set("remove_first", "remove_first");
  liquidFilterMappings.set("append", "append");
  liquidFilterMappings.set("prepend", "prepend");
  liquidFilterMappings.set("split", "split");
  liquidFilterMappings.set("slice", "slice");

  // Number filters
  liquidFilterMappings.set("abs", "abs");
  liquidFilterMappings.set("ceil", "ceil");
  liquidFilterMappings.set("floor", "floor");
  liquidFilterMappings.set("round", "round");
  liquidFilterMappings.set("plus", "plus");
  liquidFilterMappings.set("minus", "minus");
  liquidFilterMappings.set("times", "times");
  liquidFilterMappings.set("divided_by", "divided_by");
  liquidFilterMappings.set("modulo", "modulo");
  liquidFilterMappings.set("at_most", "at_most");
  liquidFilterMappings.set("at_least", "at_least");

  // Array filters
  liquidFilterMappings.set("join", "join");
  liquidFilterMappings.set("first", "first");
  liquidFilterMappings.set("last", "last");
  liquidFilterMappings.set("concat", "concat");
  liquidFilterMappings.set("index", "index");
  liquidFilterMappings.set("map", "map");
  liquidFilterMappings.set("reverse", "reverse");
  liquidFilterMappings.set("size", "size");
  liquidFilterMappings.set("sort", "sort");
  liquidFilterMappings.set("sort_natural", "sort_natural");
  liquidFilterMappings.set("uniq", "uniq");
  liquidFilterMappings.set("where", "where");
  liquidFilterMappings.set("group_by", "group_by");
  liquidFilterMappings.set("compact", "compact");
  liquidFilterMappings.set("flatten", "flatten");

  // Date filters
  liquidFilterMappings.set("date", "date");
  liquidFilterMappings.set("strftime", "strftime");

  // URL filters
  liquidFilterMappings.set("url_encode", "url_encode");
  liquidFilterMappings.set("url_decode", "url_decode");
  liquidFilterMappings.set("url_escape", "url_escape");
  liquidFilterMappings.set("url_param_escape", "url_param_escape");

  // Money filters
  liquidFilterMappings.set("money", "money");
  liquidFilterMappings.set("money_with_currency", "money_with_currency");
  liquidFilterMappings.set("money_without_currency", "money_without_currency");
  liquidFilterMappings.set("money_without_trailing_zeros", "money_without_trailing_zeros");

  // Image filters
  liquidFilterMappings.set("img_url", "img_url");
  liquidFilterMappings.set("img_tag", "img_tag");
  liquidFilterMappings.set("asset_url", "asset_url");
  liquidFilterMappings.set("asset_img_url", "asset_img_url");
  liquidFilterMappings.set("file_url", "file_url");
  liquidFilterMappings.set("file_img_url", "file_img_url");
  liquidFilterMappings.set("product_img_url", "product_img_url");
  liquidFilterMappings.set("collection_img_url", "collection_img_url");

  // Color filters
  liquidFilterMappings.set("color_to_rgb", "color_to_rgb");
  liquidFilterMappings.set("color_to_hsl", "color_to_hsl");
  liquidFilterMappings.set("color_to_hex", "color_to_hex");
  liquidFilterMappings.set("color_extract", "color_extract");
  liquidFilterMappings.set("color_brightness", "color_brightness");
  liquidFilterMappings.set("color_modify", "color_modify");
  liquidFilterMappings.set("color_lighten", "color_lighten");
  liquidFilterMappings.set("color_darken", "color_darken");
  liquidFilterMappings.set("color_saturate", "color_saturate");
  liquidFilterMappings.set("color_desaturate", "color_desaturate");
  liquidFilterMappings.set("color_mix", "color_mix");
  liquidFilterMappings.set("color_difference", "color_difference");

  // Font filters
  liquidFilterMappings.set("font_face", "font_face");
  liquidFilterMappings.set("font_url", "font_url");
  liquidFilterMappings.set("font_modify", "font_modify");

  // Weight filters
  liquidFilterMappings.set("weight_with_unit", "weight_with_unit");

  // Advanced filters
  liquidFilterMappings.set("highlight", "highlight");
  liquidFilterMappings.set("highlight_active_tag", "highlight_active_tag");
  liquidFilterMappings.set("link_to", "link_to");
  liquidFilterMappings.set("link_to_vendor", "link_to_vendor");
  liquidFilterMappings.set("link_to_type", "link_to_type");
  liquidFilterMappings.set("link_to_tag", "link_to_tag");
  liquidFilterMappings.set("link_to_add_tag", "link_to_add_tag");
  liquidFilterMappings.set("link_to_remove_tag", "link_to_remove_tag");
  liquidFilterMappings.set("payment_type_img_url", "payment_type_img_url");
  liquidFilterMappings.set("shopify_asset_url", "shopify_asset_url");
  liquidFilterMappings.set("global_asset_url", "global_asset_url");
  liquidFilterMappings.set("script_tag", "script_tag");
  liquidFilterMappings.set("stylesheet_tag", "stylesheet_tag");
  liquidFilterMappings.set("placeholder_svg_tag", "placeholder_svg_tag");
  liquidFilterMappings.set("customer_login_link", "customer_login_link");
  liquidFilterMappings.set("customer_logout_link", "customer_logout_link");
  liquidFilterMappings.set("customer_register_link", "customer_register_link");

  // Format filters
  liquidFilterMappings.set("default", "default");
  liquidFilterMappings.set("json", "json");
  liquidFilterMappings.set("base64_encode", "base64_encode");
  liquidFilterMappings.set("base64_decode", "base64_decode");
  liquidFilterMappings.set("md5", "md5");
  liquidFilterMappings.set("sha1", "sha1");
  liquidFilterMappings.set("sha256", "sha256");
  liquidFilterMappings.set("hmac_sha1", "hmac_sha1");
  liquidFilterMappings.set("hmac_sha256", "hmac_sha256");

  // Form filters
  liquidFilterMappings.set("form", "form");
  liquidFilterMappings.set("endform", "endform");

  // Pagination filters
  liquidFilterMappings.set("paginate", "paginate");
  liquidFilterMappings.set("endpaginate", "endpaginate");

  // Loop filters
  liquidFilterMappings.set("cycle", "cycle");
  liquidFilterMappings.set("tablerow", "tablerow");
  liquidFilterMappings.set("endtablerow", "endtablerow");

  // Conditional filters
  liquidFilterMappings.set("if", "if");
  liquidFilterMappings.set("unless", "unless");
  liquidFilterMappings.set("elsif", "elsif");
  liquidFilterMappings.set("else", "else");
  liquidFilterMappings.set("endif", "endif");
  liquidFilterMappings.set("endunless", "endunless");
  liquidFilterMappings.set("case", "case");
  liquidFilterMappings.set("when", "when");
  liquidFilterMappings.set("endcase", "endcase");

  // Loop filters
  liquidFilterMappings.set("for", "for");
  liquidFilterMappings.set("endfor", "endfor");
  liquidFilterMappings.set("break", "break");
  liquidFilterMappings.set("continue", "continue");

  // Variable filters
  liquidFilterMappings.set("assign", "assign");
  liquidFilterMappings.set("capture", "capture");
  liquidFilterMappings.set("endcapture", "endcapture");
  liquidFilterMappings.set("increment", "increment");
  liquidFilterMappings.set("decrement", "decrement");

  // Include filters
  liquidFilterMappings.set("include", "include");
  liquidFilterMappings.set("render", "render");
  liquidFilterMappings.set("section", "section");

  // Comment filters
  liquidFilterMappings.set("comment", "comment");
  liquidFilterMappings.set("endcomment", "endcomment");
  liquidFilterMappings.set("raw", "raw");
  liquidFilterMappings.set("endraw", "endraw");

  // Layout filters
  liquidFilterMappings.set("layout", "layout");
  liquidFilterMappings.set("content_for", "content_for");
  liquidFilterMappings.set("yield", "yield");

  // Internationalization filters
  liquidFilterMappings.set("t", "t");
  liquidFilterMappings.set("translate", "translate");

  // Custom filters
  liquidFilterMappings.set("pluralize", "pluralize");
  liquidFilterMappings.set("within", "within");
  liquidFilterMappings.set("contains", "contains");
}

/**
 * Initialize Liquid filter mappings
 */
export function initializeLiquidFilters(): void {
  initializeLiquidFilterMappings();
}

/**
 * Get Liquid filter mapping by name
 * @param filterName - Original Liquid filter name
 * @returns Mapped filter name or original if not found
 */
export function getLiquidFilterByName(filterName: string): string {
  const mapped = liquidFilterMappings.get(filterName);
  if (mapped !== null) {
    return mapped;
  }
  return filterName;
}

/**
 * Get Liquid filter statistics
 * @returns Liquid filter statistics
 */
export function getLiquidFilterStats(): FilterStats {
  const stats = new FilterStats();
  stats.totalFilters = liquidFilterMappings.size;
  stats.categories = 10; // Based on LIQUID_FILTER_CATEGORIES

  return stats;
}

/**
 * Check if Liquid filter exists in mappings
 * @param filterName - Filter name to check
 * @returns True if filter exists in Liquid mappings
 */
export function hasLiquidFilter(filterName: string): boolean {
  return liquidFilterMappings.has(filterName);
}

/**
 * Get Liquid filters by category name
 * @param categoryName - Category name
 * @returns Array of Liquid filter names in the category
 */
export function getLiquidFiltersByCategory(categoryName: string): string[] {
  // Simplified implementation - in a full implementation,
  // we would have separate Liquid categories
  const liquidCategories = new Map<string, string[]>();
  liquidCategories.set("string", ["capitalize", "downcase", "upcase", "escape", "escape_once", "newline_to_br", "strip_html", "strip_newlines", "truncate", "truncatewords", "lstrip", "rstrip", "strip", "replace", "replace_first", "remove", "remove_first", "append", "prepend", "split", "slice"]);
  liquidCategories.set("number", ["abs", "ceil", "floor", "round", "plus", "minus", "times", "divided_by", "modulo", "at_most", "at_least"]);
  liquidCategories.set("array", ["join", "first", "last", "concat", "index", "map", "reverse", "size", "sort", "sort_natural", "uniq", "where", "group_by", "compact", "flatten"]);
  liquidCategories.set("date", ["date", "strftime"]);
  liquidCategories.set("url", ["url_encode", "url_decode", "url_escape", "url_param_escape"]);
  liquidCategories.set("money", ["money", "money_with_currency", "money_without_currency", "money_without_trailing_zeros"]);
  liquidCategories.set("image", ["img_url", "img_tag", "asset_url", "asset_img_url", "file_url", "file_img_url", "product_img_url", "collection_img_url"]);
  liquidCategories.set("color", ["color_to_rgb", "color_to_hsl", "color_to_hex", "color_extract", "color_brightness", "color_modify", "color_lighten", "color_darken", "color_saturate", "color_desaturate", "color_mix", "color_difference"]);
  liquidCategories.set("format", ["default", "json", "base64_encode", "base64_decode", "md5", "sha1", "sha256", "hmac_sha1", "hmac_sha256"]);
  liquidCategories.set("advanced", ["highlight", "highlight_active_tag", "link_to", "link_to_vendor", "link_to_type", "link_to_tag", "link_to_add_tag", "link_to_remove_tag", "payment_type_img_url", "shopify_asset_url", "global_asset_url", "script_tag", "stylesheet_tag", "placeholder_svg_tag", "customer_login_link", "customer_logout_link", "customer_register_link"]);

  if (liquidCategories.has(categoryName)) {
    return liquidCategories.get(categoryName)!;
  }
  return [];
}
