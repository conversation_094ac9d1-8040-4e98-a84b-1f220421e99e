/**
 * WASM Object Mappings for HBS2Sline Converter
 * AssemblyScript implementation of object mapping system
 * Based on backend/rules/mappings/objects.js
 */

// ================== Object Mappings Map ==================
// Using Map for efficient lookups in WASM
const objectMappings = new Map<string, string>();

// Initialize object mappings
function initializeObjectMappings(): void {
  // Shop objects
  objectMappings.set("shop", "shop");
  objectMappings.set("store", "shop"); // compatibility mapping
  
  // Product objects
  objectMappings.set("product", "product");
  objectMappings.set("products", "products");
  objectMappings.set("product.title", "product.title");
  objectMappings.set("product.price", "product.price");
  objectMappings.set("product.compare_at_price", "product.compare_at_price");
  objectMappings.set("product.description", "product.description");
  objectMappings.set("product.images", "product.images");
  objectMappings.set("product.variants", "product.variants");
  objectMappings.set("product.tags", "product.tags");
  objectMappings.set("product.handle", "product.handle");
  objectMappings.set("product.available", "product.available");
  objectMappings.set("product.vendor", "product.vendor");
  objectMappings.set("product.type", "product.type");
  
  // Collection objects
  objectMappings.set("collection", "collection");
  objectMappings.set("collections", "collections");
  objectMappings.set("collection.title", "collection.title");
  objectMappings.set("collection.description", "collection.description");
  objectMappings.set("collection.products", "collection.products");
  objectMappings.set("collection.handle", "collection.handle");
  
  // Page objects
  objectMappings.set("page", "page");
  objectMappings.set("pages", "pages");
  objectMappings.set("page_title", "page.title");
  objectMappings.set("page_description", "page.description");
  
  // Customer objects
  objectMappings.set("customer", "customer");
  objectMappings.set("customer.email", "customer.email");
  objectMappings.set("customer.first_name", "customer.first_name");
  objectMappings.set("customer.last_name", "customer.last_name");
  
  // Cart objects
  objectMappings.set("cart", "cart");
  objectMappings.set("cart.item_count", "cart.item_count");
  objectMappings.set("cart.total_price", "cart.total_price");
  objectMappings.set("cart.items", "cart.items");
  
  // Theme settings
  objectMappings.set("settings", "settings");
  
  // Template related
  objectMappings.set("template", "template");
  objectMappings.set("content_for_header", "content_for_header");
  objectMappings.set("content_for_layout", "content_for_layout");
  objectMappings.set("content_for_footer", "content_for_footer");
  
  // Request objects
  objectMappings.set("request", "request");
  objectMappings.set("request.locale", "request.locale.iso_code");
}

// ================== Object Categories ==================
class ObjectCategory {
  name: string;
  description: string;
  objects: string[];
  
  constructor(name: string, description: string, objects: string[]) {
    this.name = name;
    this.description = description;
    this.objects = objects;
  }
}

const objectCategories: ObjectCategory[] = [];

function initializeObjectCategories(): void {
  objectCategories.push(new ObjectCategory(
    "商店对象",
    "商店基本信息和设置",
    ["shop", "store", "settings"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "产品对象", 
    "产品相关的所有数据",
    ["product", "products", "product.title", "product.price", "product.compare_at_price", 
     "product.description", "product.images", "product.variants", "product.tags", 
     "product.handle", "product.available", "product.vendor", "product.type"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "集合对象",
    "产品集合相关数据", 
    ["collection", "collections", "collection.title", "collection.description", 
     "collection.products", "collection.handle"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "页面对象",
    "页面和内容相关数据",
    ["page", "pages", "page_title", "page_description"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "客户对象",
    "客户信息相关数据",
    ["customer", "customer.email", "customer.first_name", "customer.last_name"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "购物车对象",
    "购物车和订单相关数据",
    ["cart", "cart.item_count", "cart.total_price", "cart.items"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "模板对象",
    "模板系统相关对象",
    ["template", "content_for_header", "content_for_layout", "content_for_footer"]
  ));
  
  objectCategories.push(new ObjectCategory(
    "请求对象",
    "请求和上下文相关数据",
    ["request", "request.locale"]
  ));
}

// ================== Statistics Class ==================
class ObjectStats {
  totalObjects: number;
  categories: number;
  objectsByCategory: Map<string, number>;

  constructor() {
    this.totalObjects = 0;
    this.categories = 0;
    this.objectsByCategory = new Map<string, number>();
  }
}

// ================== Exported Functions ==================

/**
 * Initialize the object mapping system
 */
export function initializeObjects(): void {
  initializeObjectMappings();
  initializeObjectCategories();
}

/**
 * Get mapped object name by original name
 * @param objectName - Original object name
 * @returns Mapped object name or original if not found
 */
export function getObjectByName(objectName: string): string {
  const mapped = objectMappings.get(objectName);
  if (mapped !== null) {
    return mapped;
  }
  return objectName;
}

/**
 * Get object statistics
 * @returns Object statistics
 */
export function getObjectStats(): ObjectStats {
  const stats = new ObjectStats();
  stats.totalObjects = objectMappings.size;
  stats.categories = objectCategories.length;
  
  // Calculate objects by category
  for (let i = 0; i < objectCategories.length; i++) {
    const category = objectCategories[i];
    stats.objectsByCategory.set(category.name, category.objects.length);
  }
  
  return stats;
}

/**
 * Check if object exists in mappings
 * @param objectName - Object name to check
 * @returns True if object exists in mappings
 */
export function hasObject(objectName: string): boolean {
  return objectMappings.has(objectName);
}

/**
 * Get total number of mapped objects
 * @returns Total count of mapped objects
 */
export function getObjectCount(): number {
  return objectMappings.size;
}

/**
 * Get category count
 * @returns Number of object categories
 */
export function getCategoryCount(): number {
  return objectCategories.length;
}

/**
 * Get category name by index
 * @param index - Category index
 * @returns Category name or empty string if index out of bounds
 */
export function getCategoryName(index: number): string {
  if (index >= 0 && index < objectCategories.length) {
    return objectCategories[index].name;
  }
  return "";
}

/**
 * Get category description by index
 * @param index - Category index
 * @returns Category description or empty string if index out of bounds
 */
export function getCategoryDescription(index: number): string {
  if (index >= 0 && index < objectCategories.length) {
    return objectCategories[index].description;
  }
  return "";
}

// ================== Liquid Object Mappings ==================
// Separate mapping for Liquid objects
const liquidObjectMappings = new Map<string, string>();

function initializeLiquidObjectMappings(): void {
  // Shop related objects
  liquidObjectMappings.set("shop", "shop");
  liquidObjectMappings.set("shop.name", "shop.name");
  liquidObjectMappings.set("shop.domain", "shop.domain");
  liquidObjectMappings.set("shop.url", "shop.url");
  liquidObjectMappings.set("shop.email", "shop.email");
  liquidObjectMappings.set("shop.description", "shop.description");
  liquidObjectMappings.set("shop.money_format", "shop.money_format");
  liquidObjectMappings.set("shop.currency", "shop.currency");
  liquidObjectMappings.set("shop.timezone", "shop.timezone");
  liquidObjectMappings.set("shop.address", "shop.address");
  liquidObjectMappings.set("shop.phone", "shop.phone");

  // Product related objects
  liquidObjectMappings.set("product", "product");
  liquidObjectMappings.set("product.id", "product.id");
  liquidObjectMappings.set("product.title", "product.title");
  liquidObjectMappings.set("product.handle", "product.handle");
  liquidObjectMappings.set("product.description", "product.description");
  liquidObjectMappings.set("product.content", "product.content");
  liquidObjectMappings.set("product.excerpt", "product.excerpt");
  liquidObjectMappings.set("product.vendor", "product.vendor");
  liquidObjectMappings.set("product.type", "product.type");
  liquidObjectMappings.set("product.price", "product.price");
  liquidObjectMappings.set("product.price_min", "product.price_min");
  liquidObjectMappings.set("product.price_max", "product.price_max");
  liquidObjectMappings.set("product.compare_at_price", "product.compare_at_price");
  liquidObjectMappings.set("product.compare_at_price_min", "product.compare_at_price_min");
  liquidObjectMappings.set("product.compare_at_price_max", "product.compare_at_price_max");
  liquidObjectMappings.set("product.available", "product.available");
  liquidObjectMappings.set("product.tags", "product.tags");
  liquidObjectMappings.set("product.variants", "product.variants");

  // Collection related objects
  liquidObjectMappings.set("collection", "collection");
  liquidObjectMappings.set("collection.id", "collection.id");
  liquidObjectMappings.set("collection.title", "collection.title");
  liquidObjectMappings.set("collection.handle", "collection.handle");
  liquidObjectMappings.set("collection.description", "collection.description");
  liquidObjectMappings.set("collection.image", "collection.image");
  liquidObjectMappings.set("collection.url", "collection.url");
  liquidObjectMappings.set("collection.products", "collection.products");
  liquidObjectMappings.set("collections", "collections");

  // Cart related objects
  liquidObjectMappings.set("cart", "cart");
  liquidObjectMappings.set("cart.item_count", "cart.item_count");
  liquidObjectMappings.set("cart.total_price", "cart.total_price");
  liquidObjectMappings.set("cart.total_weight", "cart.total_weight");
  liquidObjectMappings.set("cart.items", "cart.items");
  liquidObjectMappings.set("cart.note", "cart.note");
  liquidObjectMappings.set("cart.attributes", "cart.attributes");

  // Customer related objects
  liquidObjectMappings.set("customer", "customer");
  liquidObjectMappings.set("customer.id", "customer.id");
  liquidObjectMappings.set("customer.email", "customer.email");
  liquidObjectMappings.set("customer.first_name", "customer.first_name");
  liquidObjectMappings.set("customer.last_name", "customer.last_name");
  liquidObjectMappings.set("customer.name", "customer.name");
  liquidObjectMappings.set("customer.phone", "customer.phone");
  liquidObjectMappings.set("customer.accepts_marketing", "customer.accepts_marketing");
  liquidObjectMappings.set("customer.tags", "customer.tags");
  liquidObjectMappings.set("customer.orders_count", "customer.orders_count");
  liquidObjectMappings.set("customer.total_spent", "customer.total_spent");
  liquidObjectMappings.set("customer.addresses", "customer.addresses");
  liquidObjectMappings.set("customer.default_address", "customer.default_address");

  // Order related objects
  liquidObjectMappings.set("order", "order");
  liquidObjectMappings.set("order.id", "order.id");
  liquidObjectMappings.set("order.name", "order.name");
  liquidObjectMappings.set("order.email", "order.email");
  liquidObjectMappings.set("order.created_at", "order.created_at");
  liquidObjectMappings.set("order.updated_at", "order.updated_at");
  liquidObjectMappings.set("order.cancelled_at", "order.cancelled_at");
  liquidObjectMappings.set("order.cancel_reason", "order.cancel_reason");
  liquidObjectMappings.set("order.total_price", "order.total_price");
  liquidObjectMappings.set("order.subtotal_price", "order.subtotal_price");
  liquidObjectMappings.set("order.total_tax", "order.total_tax");
  liquidObjectMappings.set("order.tax_lines", "order.tax_lines");
  liquidObjectMappings.set("order.shipping_price", "order.shipping_price");
  liquidObjectMappings.set("order.shipping_methods", "order.shipping_methods");
  liquidObjectMappings.set("order.line_items", "order.line_items");
  liquidObjectMappings.set("order.billing_address", "order.billing_address");
  liquidObjectMappings.set("order.shipping_address", "order.shipping_address");
  liquidObjectMappings.set("order.customer", "order.customer");
  liquidObjectMappings.set("order.note", "order.note");
  liquidObjectMappings.set("order.attributes", "order.attributes");

  // Page related objects
  liquidObjectMappings.set("page", "page");
  liquidObjectMappings.set("page.id", "page.id");
  liquidObjectMappings.set("page.title", "page.title");
  liquidObjectMappings.set("page.content", "page.content");
  liquidObjectMappings.set("page.excerpt", "page.excerpt");
  liquidObjectMappings.set("page.handle", "page.handle");
  liquidObjectMappings.set("page.url", "page.url");
  liquidObjectMappings.set("page.created_at", "page.created_at");
  liquidObjectMappings.set("page.updated_at", "page.updated_at");
  liquidObjectMappings.set("page.author", "page.author");
  liquidObjectMappings.set("pages", "pages");

  // Additional objects (blog, article, image, etc.)
  liquidObjectMappings.set("blog", "blog");
  liquidObjectMappings.set("article", "article");
  liquidObjectMappings.set("image", "image");
  liquidObjectMappings.set("linklists", "linklists");
  liquidObjectMappings.set("linklist", "linklist");
  liquidObjectMappings.set("link", "link");
  liquidObjectMappings.set("form", "form");
  liquidObjectMappings.set("search", "search");
  liquidObjectMappings.set("request", "request");
  liquidObjectMappings.set("template", "template");
  liquidObjectMappings.set("settings", "settings");
  liquidObjectMappings.set("canonical_url", "canonical_url");
  liquidObjectMappings.set("content_for_header", "content_for_header");
  liquidObjectMappings.set("content_for_layout", "content_for_layout");
  liquidObjectMappings.set("page_title", "page_title");
  liquidObjectMappings.set("page_description", "page_description");
}

/**
 * Initialize Liquid object mappings
 */
export function initializeLiquidObjects(): void {
  initializeLiquidObjectMappings();
}

/**
 * Get Liquid object mapping by name
 * @param objectName - Original Liquid object name
 * @returns Mapped object name or original if not found
 */
export function getLiquidObjectByName(objectName: string): string {
  const mapped = liquidObjectMappings.get(objectName);
  if (mapped !== null) {
    return mapped;
  }
  return objectName;
}

/**
 * Get Liquid object statistics
 * @returns Liquid object statistics
 */
export function getLiquidObjectStats(): ObjectStats {
  const stats = new ObjectStats();
  stats.totalObjects = liquidObjectMappings.size;
  stats.categories = 9; // Based on LIQUID_OBJECT_CATEGORIES

  return stats;
}

/**
 * Check if Liquid object exists in mappings
 * @param objectName - Object name to check
 * @returns True if object exists in Liquid mappings
 */
export function hasLiquidObject(objectName: string): boolean {
  return liquidObjectMappings.has(objectName);
}
