/**
 * WASM Variable and Operator Mappings for HBS2Sline Converter
 * AssemblyScript implementation of variable and operator mapping system
 * Based on backend/rules/mappings/variables.js and backend/liquid-to-sline-rules/mappings/variables.js
 */

// ================== Handlebars Loop Variable Mappings ==================
const loopVariableMappings = new Map<string, string>();

function initializeLoopVariableMappings(): void {
  loopVariableMappings.set("@index", "forloop.index0");      // 从 0 开始的索引
  loopVariableMappings.set("@first", "forloop.first");       // 是否是第一个
  loopVariableMappings.set("@last", "forloop.last");         // 是否是最后一个
  loopVariableMappings.set("@key", "forloop.key");           // 对象的键
  loopVariableMappings.set("@root", "global");               // 根上下文
  loopVariableMappings.set("@../", "../");                   // 父级上下文
  loopVariableMappings.set("this", "item");                  // 当前项（在 each 循环中）
}

// ================== Handlebars Operator Mappings ==================
const operatorMappings = new Map<string, string>();

function initializeOperatorMappings(): void {
  operatorMappings.set("===", "==");
  operatorMappings.set("!==", "!=");
  // 注意：and 和 or 的转换已经在 OPERATOR_TAG_RULES 中更精确地处理
}

// ================== Liquid Variable Mappings ==================
const liquidVariableMappings = new Map<string, string>();

function initializeLiquidVariableMappings(): void {
  // forloop 变量映射
  liquidVariableMappings.set("forloop.index", "@index");
  liquidVariableMappings.set("forloop.index0", "@index0");
  liquidVariableMappings.set("forloop.rindex", "@rindex");
  liquidVariableMappings.set("forloop.rindex0", "@rindex0");
  liquidVariableMappings.set("forloop.first", "@first");
  liquidVariableMappings.set("forloop.last", "@last");
  liquidVariableMappings.set("forloop.length", "@length");
  
  // tablerowloop 变量映射
  liquidVariableMappings.set("tablerowloop.index", "@index");
  liquidVariableMappings.set("tablerowloop.index0", "@index0");
  liquidVariableMappings.set("tablerowloop.rindex", "@rindex");
  liquidVariableMappings.set("tablerowloop.rindex0", "@rindex0");
  liquidVariableMappings.set("tablerowloop.first", "@first");
  liquidVariableMappings.set("tablerowloop.last", "@last");
  liquidVariableMappings.set("tablerowloop.length", "@length");
  liquidVariableMappings.set("tablerowloop.col", "@col");
  liquidVariableMappings.set("tablerowloop.col0", "@col0");
  liquidVariableMappings.set("tablerowloop.col_first", "@col_first");
  liquidVariableMappings.set("tablerowloop.col_last", "@col_last");
  liquidVariableMappings.set("tablerowloop.row", "@row");
  
  // paginate 变量映射
  liquidVariableMappings.set("paginate.current_page", "paginate.current_page");
  liquidVariableMappings.set("paginate.current_offset", "paginate.current_offset");
  liquidVariableMappings.set("paginate.items", "paginate.items");
  liquidVariableMappings.set("paginate.parts", "paginate.parts");
  liquidVariableMappings.set("paginate.pages", "paginate.pages");
  liquidVariableMappings.set("paginate.previous", "paginate.previous");
  liquidVariableMappings.set("paginate.next", "paginate.next");
  
  // 特殊变量
  liquidVariableMappings.set("blank", "null");
  liquidVariableMappings.set("empty", "empty");
  liquidVariableMappings.set("nil", "null");
  liquidVariableMappings.set("null", "null");
  liquidVariableMappings.set("true", "true");
  liquidVariableMappings.set("false", "false");
}

// ================== Liquid Operator Mappings ==================
const liquidOperatorMappings = new Map<string, string>();

function initializeLiquidOperatorMappings(): void {
  // 逻辑运算符
  liquidOperatorMappings.set("and", "&&");
  liquidOperatorMappings.set("or", "||");
  
  // 比较运算符
  liquidOperatorMappings.set("==", "==");
  liquidOperatorMappings.set("!=", "!=");
  liquidOperatorMappings.set("<>", "!=");
  liquidOperatorMappings.set(">", ">");
  liquidOperatorMappings.set("<", "<");
  liquidOperatorMappings.set(">=", ">=");
  liquidOperatorMappings.set("<=", "<=");
  
  // 包含运算符
  liquidOperatorMappings.set("contains", "contains");
  
  // 赋值运算符
  liquidOperatorMappings.set("=", "=");
}

// ================== Special Operators ==================
const liquidSpecialOperators = new Map<string, string>();

function initializeLiquidSpecialOperators(): void {
  // 大小比较
  liquidSpecialOperators.set("size", "size()");
  
  // 范围操作
  liquidSpecialOperators.set("..", "range");
  
  // 属性访问
  liquidSpecialOperators.set(".", ".");
  
  // 数组索引
  liquidSpecialOperators.set("[", "[");
  liquidSpecialOperators.set("]", "]");
}

// ================== Statistics Class ==================
class VariableStats {
  totalLoopVariables: number;
  totalOperators: number;
  totalLiquidVariables: number;
  totalLiquidOperators: number;
  totalSpecialOperators: number;
  
  constructor() {
    this.totalLoopVariables = 0;
    this.totalOperators = 0;
    this.totalLiquidVariables = 0;
    this.totalLiquidOperators = 0;
    this.totalSpecialOperators = 0;
  }
}

// ================== Exported Functions ==================

/**
 * Initialize the variable mapping system
 */
export function initializeVariables(): void {
  initializeLoopVariableMappings();
  initializeOperatorMappings();
  initializeLiquidVariableMappings();
  initializeLiquidOperatorMappings();
  initializeLiquidSpecialOperators();
}

/**
 * Get mapped variable name by original name (Handlebars)
 * @param variableName - Original variable name
 * @returns Mapped variable name or original if not found
 */
export function getVariableByName(variableName: string): string {
  const mapped = loopVariableMappings.get(variableName);
  if (mapped !== null) {
    return mapped;
  }
  return variableName;
}

/**
 * Get mapped operator by original operator (Handlebars)
 * @param operator - Original operator
 * @returns Mapped operator or original if not found
 */
export function getOperatorByName(operator: string): string {
  const mapped = operatorMappings.get(operator);
  if (mapped !== null) {
    return mapped;
  }
  return operator;
}

/**
 * Get mapped Liquid variable name by original name
 * @param variableName - Original Liquid variable name
 * @returns Mapped variable name or original if not found
 */
export function getLiquidVariableByName(variableName: string): string {
  const mapped = liquidVariableMappings.get(variableName);
  if (mapped !== null) {
    return mapped;
  }
  return variableName;
}

/**
 * Get mapped Liquid operator by original operator
 * @param operator - Original Liquid operator
 * @returns Mapped operator or original if not found
 */
export function getLiquidOperatorByName(operator: string): string {
  const mapped = liquidOperatorMappings.get(operator);
  if (mapped !== null) {
    return mapped;
  }
  return operator;
}

/**
 * Get special operator mapping
 * @param operator - Special operator
 * @returns Mapped special operator or original if not found
 */
export function getLiquidSpecialOperator(operator: string): string {
  return liquidSpecialOperators.has(operator) ? liquidSpecialOperators.get(operator)! : operator;
}

/**
 * Check if variable is a loop variable (Handlebars)
 * @param variableName - Variable name to check
 * @returns True if variable is a loop variable
 */
export function isLoopVariable(variableName: string): boolean {
  return loopVariableMappings.has(variableName);
}

/**
 * Check if operator is convertible (Handlebars)
 * @param operator - Operator to check
 * @returns True if operator is convertible
 */
export function isConvertibleOperator(operator: string): boolean {
  return operatorMappings.has(operator);
}

/**
 * Check if variable is a Liquid loop variable
 * @param variableName - Variable name to check
 * @returns True if variable is a Liquid loop variable
 */
export function isLiquidLoopVariable(variableName: string): boolean {
  return variableName.indexOf("forloop.") === 0 || 
         variableName.indexOf("tablerowloop.") === 0 ||
         variableName.indexOf("@") === 0;
}

/**
 * Check if operator is convertible (Liquid)
 * @param operator - Operator to check
 * @returns True if operator is convertible
 */
export function isLiquidConvertibleOperator(operator: string): boolean {
  return liquidOperatorMappings.has(operator);
}

/**
 * Get variable statistics
 * @returns Variable statistics
 */
export function getVariableStats(): VariableStats {
  const stats = new VariableStats();
  stats.totalLoopVariables = loopVariableMappings.size;
  stats.totalOperators = operatorMappings.size;
  stats.totalLiquidVariables = liquidVariableMappings.size;
  stats.totalLiquidOperators = liquidOperatorMappings.size;
  stats.totalSpecialOperators = liquidSpecialOperators.size;
  
  return stats;
}

/**
 * Get total number of loop variables
 * @returns Total count of loop variables
 */
export function getLoopVariableCount(): number {
  return loopVariableMappings.size;
}

/**
 * Get total number of operators
 * @returns Total count of operators
 */
export function getOperatorCount(): number {
  return operatorMappings.size;
}

/**
 * Get total number of Liquid variables
 * @returns Total count of Liquid variables
 */
export function getLiquidVariableCount(): number {
  return liquidVariableMappings.size;
}

/**
 * Get total number of Liquid operators
 * @returns Total count of Liquid operators
 */
export function getLiquidOperatorCount(): number {
  return liquidOperatorMappings.size;
}

// ================== Condition Transformation Functions ==================

/**
 * Convert Liquid condition expression to Sline format
 * @param condition - Original Liquid condition
 * @returns Converted Sline condition
 */
export function convertLiquidCondition(condition: string): string {
  let result = condition;

  // 转换 .size 属性为 size() 过滤器
  result = simpleReplace(result, ".size", "|size()");

  // 转换 contains 操作为过滤器
  // 简化版本，处理基本的 contains 操作
  if (result.indexOf(" contains ") !== -1) {
    const parts = result.split(" contains ");
    if (parts.length === 2) {
      const variable = parts[0].trim();
      const value = parts[1].trim();
      // 移除引号并重新添加
      const cleanValue = removeQuotes(value);
      result = variable + '|contains("' + cleanValue + '")';
    }
  }

  // 转换逻辑运算符
  result = simpleReplace(result, " or ", " || ");
  result = simpleReplace(result, " and ", " && ");

  // 标准化比较运算符空格
  result = normalizeOperatorSpaces(result);

  // 将单引号转换为双引号
  result = convertSingleToDoubleQuotes(result);

  return result;
}

/**
 * Check if condition is a single variable
 * @param condition - Condition expression
 * @returns True if condition is a single variable
 */
export function isLiquidSingleVariable(condition: string): boolean {
  const trimmed = condition.trim();
  // 简化的变量检查：只包含字母、数字、点和下划线
  return isSimpleVariableName(trimmed);
}

/**
 * Negate a Liquid condition (for unless conversion)
 * @param condition - Original condition
 * @returns Negated condition
 */
export function negateLiquidCondition(condition: string): string {
  const cleanCondition = condition.trim();

  if (isLiquidSingleVariable(cleanCondition)) {
    return "!" + cleanCondition;
  }

  const convertedCondition = convertLiquidCondition(cleanCondition);
  return "!(" + convertedCondition + ")";
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}

/**
 * Normalize operator spaces
 * @param input - Input string
 * @returns String with normalized operator spaces
 */
function normalizeOperatorSpaces(input: string): string {
  let result = input;

  // 标准化比较运算符
  result = simpleReplace(result, "==", " == ");
  result = simpleReplace(result, "!=", " != ");
  result = simpleReplace(result, ">=", " >= ");
  result = simpleReplace(result, "<=", " <= ");
  result = simpleReplace(result, ">", " > ");
  result = simpleReplace(result, "<", " < ");

  // 清理多余空格
  result = cleanupSpaces(result);

  return result;
}

/**
 * Convert single quotes to double quotes
 * @param input - Input string
 * @returns String with double quotes
 */
function convertSingleToDoubleQuotes(input: string): string {
  let result = input;
  let inSingleQuote = false;
  let newResult = "";

  for (let i = 0; i < result.length; i++) {
    const char = result.charAt(i);

    if (char === "'" && !inSingleQuote) {
      inSingleQuote = true;
      newResult += '"';
    } else if (char === "'" && inSingleQuote) {
      inSingleQuote = false;
      newResult += '"';
    } else {
      newResult += char;
    }
  }

  return newResult;
}

/**
 * Check if string is a simple variable name
 * @param name - Variable name to check
 * @returns True if it's a simple variable name
 */
function isSimpleVariableName(name: string): boolean {
  if (name.length === 0) return false;

  for (let i = 0; i < name.length; i++) {
    const char = name.charAt(i);
    const code = char.charCodeAt(0);

    // 允许字母、数字、点、下划线
    if (!((code >= 65 && code <= 90) ||   // A-Z
          (code >= 97 && code <= 122) ||  // a-z
          (code >= 48 && code <= 57) ||   // 0-9
          code === 46 ||                  // .
          code === 95)) {                 // _
      return false;
    }
  }

  return true;
}

/**
 * Clean up multiple spaces
 * @param input - Input string
 * @returns String with cleaned up spaces
 */
function cleanupSpaces(input: string): string {
  let result = input;

  // 简化的空格清理
  while (result.indexOf("  ") !== -1) {
    result = simpleReplace(result, "  ", " ");
  }

  return result.trim();
}

/**
 * Remove quotes from string
 * @param input - Input string
 * @returns String without quotes
 */
function removeQuotes(input: string): string {
  let result = input;

  // 移除单引号
  result = simpleReplace(result, "'", "");
  // 移除双引号
  result = simpleReplace(result, '"', "");

  return result;
}
