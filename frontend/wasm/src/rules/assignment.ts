/**
 * WASM ASSIGNMENT Rules for HBS2Sline Converter
 * AssemblyScript implementation of assignment conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/assignment/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T02:04:57.014Z
 */

// ================== Rule Structure ==================
class AssignmentRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Assignment Rules ==================
const handlebarsAssignmentRules: AssignmentRule[] = [];

function initializeHandlebarsAssignmentRules(): void {
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 变量赋值转换",
    "assignment",
    "转换 Handlebars 变量赋值到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
  
  // Example: {{#assign price = product.price}} -> {{#var price = product.price /}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 变量赋值转换 - 带过滤器",
    "assignment",
    "转换带过滤器的 Handlebars 变量赋值",
    "",
    "{{#var title = product.title|upcase() /}}"
  ));
  
  // Example: {{#assign title = (upcase product.title)}} -> {{#var title = product.title|upcase() /}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars with 语句转换",
    "assignment",
    "转换 Handlebars with 语句到 Sline var 标签",
    "",
    "{{#var p = product}}{{#with p}}"
  ));
  
  // Example: {{#with product as |p|}} -> {{#var p = product}}{{#with p}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars let 语句转换",
    "assignment",
    "转换 Handlebars let 语句到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
  
  // Example: {{#let price=product.price}} -> {{#var price = product.price /}}
  
  handlebarsAssignmentRules.push(new AssignmentRule(
    "Handlebars 局部变量转换",
    "assignment",
    "转换 Handlebars 局部变量定义",
    "",
    "{{#var title = product.title /}}"
  ));
  
  // Example: {{#set title product.title}} -> {{#var title = product.title /}}
  
}


// ================== Liquid Assignment Rules ==================
const liquidAssignmentRules: AssignmentRule[] = [];

function initializeLiquidAssignmentRules(): void {
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid assign 标签转换",
    "assignment",
    "转换 Liquid assign 标签到 Sline var 标签",
    "",
    "{{#var price = product.price /}}"
  ));
  
  // Example: {% assign price = product.price %} -> {{#var price = product.price /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid assign 标签转换 - 带过滤器",
    "assignment",
    "转换带过滤器的 Liquid assign 标签到 Sline var 标签",
    "",
    "{{#var title = product.title|upcase() /}}"
  ));
  
  // Example: {% assign title = product.title | upcase %} -> {{#var title = product.title|upcase() /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid capture 标签转换",
    "assignment",
    "转换 Liquid capture 标签到 Sline capture 标签",
    "",
    "{{#capture my_variable}}"
  ));
  
  // Example: {% capture my_variable %} -> {{#capture my_variable}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid endcapture 标签转换",
    "assignment",
    "转换 Liquid endcapture 标签到 Sline /capture 标签",
    "",
    "{{/capture}}"
  ));
  
  // Example: {% endcapture %} -> {{/capture}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid increment 标签转换",
    "assignment",
    "转换 Liquid increment 标签到 Sline increment 标签",
    "",
    "{{#increment counter /}}"
  ));
  
  // Example: {% increment counter %} -> {{#increment counter /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid decrement 标签转换",
    "assignment",
    "转换 Liquid decrement 标签到 Sline decrement 标签",
    "",
    "{{#decrement counter /}}"
  ));
  
  // Example: {% decrement counter %} -> {{#decrement counter /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid assign 标签转换 - 字符串拼接",
    "assignment",
    "转换包含字符串拼接的 Liquid assign 标签",
    "",
    "{{#var url = \"/products/\"|append(product.handle) /}}"
  ));
  
  // Example: {% assign url = "/products/" | append: product.handle %} -> {{#var url = "/products/"|append(product.handle) /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid assign 标签转换 - 数组操作",
    "assignment",
    "转换包含数组操作的 Liquid assign 标签",
    "",
    "{{#var product_titles = collection.products|map(\"title\") /}}"
  ));
  
  // Example: {% assign product_titles = collection.products | map: "title" %} -> {{#var product_titles = collection.products|map("title") /}}
  
  liquidAssignmentRules.push(new AssignmentRule(
    "Liquid assign 标签转换 - 条件赋值",
    "assignment",
    "转换包含条件的 Liquid assign 标签",
    "",
    "{{#var title = product.title|default(\"No Title\") /}}"
  ));
  
  // Example: {% assign title = product.title | default: "No Title" %} -> {{#var title = product.title|default("No Title") /}}
  
}


// ================== Statistics Class ==================
class AssignmentStats {
  totalHandlebarsRules: number;
  totalLiquidRules: number;
  assignRules: number;
  captureRules: number;
  incrementRules: number;
  includeRules: number;
  
  constructor() {
    this.totalHandlebarsRules = 0;
    this.totalLiquidRules = 0;
    this.assignRules = 0;
    this.captureRules = 0;
    this.incrementRules = 0;
    this.includeRules = 0;
  }
}


// ================== Exported Functions ==================

/**
 * Initialize the assignment rules system
 */
export function initializeAssignmentRules(): void {
  initializeHandlebarsAssignmentRules();
  initializeLiquidAssignmentRules();
}

/**
 * Get assignment rule statistics
 * @returns Assignment rule statistics
 */
export function getAssignmentStats(): AssignmentStats {
  const stats = new AssignmentStats();
  stats.totalHandlebarsRules = handlebarsAssignmentRules.length;
  stats.totalLiquidRules = liquidAssignmentRules.length;
  
  // Count rule types
  // TODO: Add specific rule type counting logic
  
  return stats;
}

/**
 * Get Handlebars assignment rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getHandlebarsAssignmentRuleByName(ruleName: string): string {
  for (let i = 0; i < handlebarsAssignmentRules.length; i++) {
    if (handlebarsAssignmentRules[i].name === ruleName) {
      return handlebarsAssignmentRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get Liquid assignment rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getLiquidAssignmentRuleByName(ruleName: string): string {
  for (let i = 0; i < liquidAssignmentRules.length; i++) {
    if (liquidAssignmentRules[i].name === ruleName) {
      return liquidAssignmentRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get total number of Handlebars assignment rules
 * @returns Total count of Handlebars assignment rules
 */
export function getHandlebarsAssignmentRuleCount(): number {
  return handlebarsAssignmentRules.length;
}

/**
 * Get total number of Liquid assignment rules
 * @returns Total count of Liquid assignment rules
 */
export function getLiquidAssignmentRuleCount(): number {
  return liquidAssignmentRules.length;
}


// ================== Assignment Transformation Functions ==================

/**
 * Apply Handlebars assignment transformations
 * @param input - Handlebars template content
 * @returns Content with assignment transformations applied
 */
export function applyHandlebarsAssignmentTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply assignment transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

/**
 * Apply Liquid assignment transformations
 * @param input - Liquid template content
 * @returns Content with assignment transformations applied
 */
export function applyLiquidAssignmentTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply assignment transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}
