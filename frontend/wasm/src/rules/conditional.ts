/**
 * WASM CONDITIONAL Rules for HBS2Sline Converter
 * AssemblyScript implementation of conditional conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/conditional/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T02:04:57.016Z
 */

// ================== Rule Structure ==================
class ConditionalRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Conditional Rules ==================
const handlebarsConditionalRules: ConditionalRule[] = [];

function initializeHandlebarsConditionalRules(): void {
  handlebarsConditionalRules.push(new ConditionalRule(
    "case/when 语句转换",
    "conditional",
    "转换 Handlebars case/when 到 Sline switch/case",
    "",
    "{{#switch $1}}"
  ));
  
  // Example: {{#case product.type}} -> {{#switch product.type}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "when 分支转换 - 带引号字符串",
    "conditional",
    "转换 when 分支到 case 自闭合标签（字符串值保持引号）",
    "",
    "{{#case \"$1\" /}}"
  ));
  
  // Example: {{when "shirt"}} -> {{#case "shirt" /}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "when 分支转换 - 变量",
    "conditional",
    "转换 when 分支到 case 自闭合标签（变量不加引号）",
    "",
    "{{#case $1 /}}"
  ));
  
  // Example: {{when shirt}} -> {{#case shirt /}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "case 结束标签",
    "conditional",
    "转换 case 结束标签",
    "",
    "{{/switch}}"
  ));
  
  // Example: {{/case}} -> {{/switch}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "if 标签转换 - 带复杂条件",
    "conditional",
    "转换 Handlebars 复杂条件表达式到 Sline",
    "",
    "{{#if $1}}"
  ));
  
  // Example: {{#if (and product.available (gt product.price 100))}} -> {{#if product.available and product.price > 100}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "if 标签转换 - 基础",
    "conditional",
    "转换 Handlebars if 到 Sline if",
    "",
    "{{#if $1}}"
  ));
  
  // Example: {{#if product.available}} -> {{#if product.available}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "unless 标签转换 - 复杂条件",
    "conditional",
    "转换 unless 复杂条件到 if 否定形式",
    "",
    "{{#if !($1)}}"
  ));
  
  // Example: {{#unless (and product.available (eq product.stock 0))}} -> {{#if !(product.available and product.stock == 0)}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "unless 标签转换 - 基础",
    "conditional",
    "转换 unless 到 if 否定形式",
    "",
    "{{#if !($1)}}"
  ));
  
  // Example: {{#unless product.available}} -> {{#if !(product.available)}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "else 标签转换",
    "conditional",
    "转换 else 到 Sline 自闭合格式",
    "",
    "{{#else /}}"
  ));
  
  // Example: {{else}} -> {{#else /}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "unless 结束标签",
    "conditional",
    "转换 unless 结束标签",
    "",
    "{{/if}}"
  ));
  
  // Example: {{/unless}} -> {{/if}}
  
  handlebarsConditionalRules.push(new ConditionalRule(
    "elseif 标签转换",
    "conditional",
    "转换 elseif 到 Sline 格式",
    "",
    "{{#else if $1 /}}"
  ));
  
  // Example: {{else if product.sale}} -> {{#else if product.sale /}}
  
}


// ================== Liquid Conditional Rules ==================
const liquidConditionalRules: ConditionalRule[] = [];

function initializeLiquidConditionalRules(): void {
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid if 标签转换",
    "conditional",
    "转换 Liquid if 标签到 Sline if 标签",
    "",
    "{{#if product.available}}"
  ));
  
  // Example: {% if product.available %} -> {{#if product.available}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid elsif 标签转换",
    "conditional",
    "转换 Liquid elsif 标签到 Sline elsif 标签",
    "",
    "{{#elsif product.price > 100}}"
  ));
  
  // Example: {% elsif product.price > 100 %} -> {{#elsif product.price > 100}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid else 标签转换",
    "conditional",
    "转换 Liquid else 标签到 Sline else 标签",
    "",
    "{{#else /}}"
  ));
  
  // Example: {% else %} -> {{#else /}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid endif 标签转换",
    "conditional",
    "转换 Liquid endif 标签到 Sline /if 标签",
    "",
    "{{/if}}"
  ));
  
  // Example: {% endif %} -> {{/if}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid unless 标签转换",
    "conditional",
    "转换 Liquid unless 标签到 Sline if 标签（否定条件）",
    "",
    "{{#if !product.available}}"
  ));
  
  // Example: {% unless product.available %} -> {{#if !product.available}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid endunless 标签转换",
    "conditional",
    "转换 Liquid endunless 标签到 Sline /if 标签",
    "",
    "{{/if}}"
  ));
  
  // Example: {% endunless %} -> {{/if}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid case 标签转换",
    "conditional",
    "转换 Liquid case 标签到 Sline switch 标签",
    "",
    "{{#switch product.type}}"
  ));
  
  // Example: {% case product.type %} -> {{#switch product.type}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid when 标签转换 - 字符串值",
    "conditional",
    "转换 Liquid when 标签到 Sline case 自闭合标签（字符串值）",
    "",
    "{{#case \"shirt\" /}}"
  ));
  
  // Example: {% when "shirt" %} -> {{#case "shirt" /}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid when 标签转换 - 变量值",
    "conditional",
    "转换 Liquid when 标签到 Sline case 自闭合标签（变量值）",
    "",
    "{{#case shirt /}}"
  ));
  
  // Example: {% when shirt %} -> {{#case shirt /}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid when 标签转换 - 多个值",
    "conditional",
    "转换 Liquid when 标签到 Sline case 自闭合标签（多个值）",
    "",
    "{{#case \"shirt\", \"pants\" /}}"
  ));
  
  // Example: {% when "shirt", "pants" %} -> {{#case "shirt", "pants" /}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid endcase 标签转换",
    "conditional",
    "转换 Liquid endcase 标签到 Sline /switch 标签",
    "",
    "{{/switch}}"
  ));
  
  // Example: {% endcase %} -> {{/switch}}
  
  liquidConditionalRules.push(new ConditionalRule(
    "Liquid 复杂条件表达式转换",
    "conditional",
    "转换包含 contains、size 等复杂条件的表达式",
    "",
    "{{#if product.tags|contains(\"sale\") && product.available}}"
  ));
  
  // Example: {% if product.tags contains "sale" and product.available %} -> {{#if product.tags|contains("sale") && product.available}}
  
}


// ================== Statistics Class ==================
class ConditionalStats {
  totalHandlebarsRules: number;
  totalLiquidRules: number;
  ifRules: number;
  unlessRules: number;
  caseRules: number;
  elseRules: number;
  
  constructor() {
    this.totalHandlebarsRules = 0;
    this.totalLiquidRules = 0;
    this.ifRules = 0;
    this.unlessRules = 0;
    this.caseRules = 0;
    this.elseRules = 0;
  }
}


// ================== Exported Functions ==================

/**
 * Initialize the conditional rules system
 */
export function initializeConditionalRules(): void {
  initializeHandlebarsConditionalRules();
  initializeLiquidConditionalRules();
}

/**
 * Get conditional rule statistics
 * @returns Conditional rule statistics
 */
export function getConditionalStats(): ConditionalStats {
  const stats = new ConditionalStats();
  stats.totalHandlebarsRules = handlebarsConditionalRules.length;
  stats.totalLiquidRules = liquidConditionalRules.length;
  
  // Count rule types
  // TODO: Add specific rule type counting logic
  
  return stats;
}

/**
 * Get Handlebars conditional rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getHandlebarsConditionalRuleByName(ruleName: string): string {
  for (let i = 0; i < handlebarsConditionalRules.length; i++) {
    if (handlebarsConditionalRules[i].name === ruleName) {
      return handlebarsConditionalRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get Liquid conditional rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getLiquidConditionalRuleByName(ruleName: string): string {
  for (let i = 0; i < liquidConditionalRules.length; i++) {
    if (liquidConditionalRules[i].name === ruleName) {
      return liquidConditionalRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get total number of Handlebars conditional rules
 * @returns Total count of Handlebars conditional rules
 */
export function getHandlebarsConditionalRuleCount(): number {
  return handlebarsConditionalRules.length;
}

/**
 * Get total number of Liquid conditional rules
 * @returns Total count of Liquid conditional rules
 */
export function getLiquidConditionalRuleCount(): number {
  return liquidConditionalRules.length;
}


// ================== Conditional Transformation Functions ==================

/**
 * Apply Handlebars conditional transformations
 * @param input - Handlebars template content
 * @returns Content with conditional transformations applied
 */
export function applyHandlebarsConditionalTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply conditional transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

/**
 * Apply Liquid conditional transformations
 * @param input - Liquid template content
 * @returns Content with conditional transformations applied
 */
export function applyLiquidConditionalTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply conditional transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}
