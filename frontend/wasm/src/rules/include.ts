/**
 * WASM INCLUDE Rules for HBS2Sline Converter
 * AssemblyScript implementation of include conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/include/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T02:04:57.015Z
 */

// ================== Rule Structure ==================
class IncludeRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Include Rules ==================
const handlebarsIncludeRules: IncludeRule[] = [];

function initializeHandlebarsIncludeRules(): void {
}


// ================== Liquid Include Rules ==================
const liquidIncludeRules: IncludeRule[] = [];

function initializeLiquidIncludeRules(): void {
  liquidIncludeRules.push(new IncludeRule(
    "Liquid include 标签转换",
    "include",
    "转换 Liquid include 标签到 Sline include 标签",
    "",
    "{{#include \"product-card\" /}}"
  ));
  
  // Example: {% include "product-card.liquid" %} -> {{#include "product-card" /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid include 标签转换 - 带参数",
    "include",
    "转换带参数的 Liquid include 标签到 Sline include 标签",
    "",
    "{{#include \"product-card\" product=featured_product /}}"
  ));
  
  // Example: {% include "product-card.liquid", product: featured_product %} -> {{#include "product-card" product=featured_product /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid render 标签转换",
    "include",
    "转换 Liquid render 标签到 Sline include 标签",
    "",
    "{{#include \"product-card\" /}}"
  ));
  
  // Example: {% render "product-card" %} -> {{#include "product-card" /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid render 标签转换 - 带参数",
    "include",
    "转换带参数的 Liquid render 标签到 Sline include 标签",
    "",
    "{{#include \"product-card\" product=featured_product /}}"
  ));
  
  // Example: {% render "product-card", product: featured_product %} -> {{#include "product-card" product=featured_product /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid render 标签转换 - with 语法",
    "include",
    "转换使用 with 语法的 Liquid render 标签",
    "",
    "{{#include \"product-card\" with=featured_product /}}"
  ));
  
  // Example: {% render "product-card" with featured_product %} -> {{#include "product-card" with=featured_product /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid render 标签转换 - as 语法",
    "include",
    "转换使用 as 语法的 Liquid render 标签",
    "",
    "{{#include \"product-card\" product=featured_product /}}"
  ));
  
  // Example: {% render "product-card" with featured_product as product %} -> {{#include "product-card" product=featured_product /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid section 标签转换",
    "include",
    "转换 Liquid section 标签到 Sline include 标签",
    "",
    "{{#include \"header\" /}}"
  ));
  
  // Example: {% section "header" %} -> {{#include "header" /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid 特殊包含标签转换",
    "include",
    "转换特殊的 Liquid 包含标签（如 render_section）",
    "",
    "{{#include \"render_footer\" /}}"
  ));
  
  // Example: {% render_footer %} -> {{#include "render_footer" /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid layout 标签转换",
    "include",
    "转换 Liquid layout 标签到 Sline layout 标签",
    "",
    "{{#layout \"theme\" /}}"
  ));
  
  // Example: {% layout "theme" %} -> {{#layout "theme" /}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid content_for 标签转换",
    "include",
    "转换 Liquid content_for 标签到 Sline content_for 标签",
    "",
    "{{#content_for \"header\"}}"
  ));
  
  // Example: {% content_for "header" %} -> {{#content_for "header"}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid endcontent_for 标签转换",
    "include",
    "转换 Liquid endcontent_for 标签到 Sline /content_for 标签",
    "",
    "{{/content_for}}"
  ));
  
  // Example: {% endcontent_for %} -> {{/content_for}}
  
  liquidIncludeRules.push(new IncludeRule(
    "Liquid yield 标签转换",
    "include",
    "转换 Liquid yield 标签到 Sline yield 标签",
    "",
    "{{#yield /}}"
  ));
  
  // Example: {% yield %} -> {{#yield /}}
  
}


// ================== Statistics Class ==================
class IncludeStats {
  totalHandlebarsRules: number;
  totalLiquidRules: number;
  includeRules: number;
  renderRules: number;
  sectionRules: number;
  layoutRules: number;
  contentRules: number;
  yieldRules: number;
  
  constructor() {
    this.totalHandlebarsRules = 0;
    this.totalLiquidRules = 0;
    this.includeRules = 0;
    this.renderRules = 0;
    this.sectionRules = 0;
    this.layoutRules = 0;
    this.contentRules = 0;
    this.yieldRules = 0;
  }
}


// ================== Exported Functions ==================

/**
 * Initialize the include rules system
 */
export function initializeIncludeRules(): void {
  initializeHandlebarsIncludeRules();
  initializeLiquidIncludeRules();
}

/**
 * Get include rule statistics
 * @returns Include rule statistics
 */
export function getIncludeStats(): IncludeStats {
  const stats = new IncludeStats();
  stats.totalHandlebarsRules = handlebarsIncludeRules.length;
  stats.totalLiquidRules = liquidIncludeRules.length;
  
  // Count rule types
  // TODO: Add specific rule type counting logic
  
  return stats;
}

/**
 * Get Handlebars include rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getHandlebarsIncludeRuleByName(ruleName: string): string {
  for (let i = 0; i < handlebarsIncludeRules.length; i++) {
    if (handlebarsIncludeRules[i].name === ruleName) {
      return handlebarsIncludeRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get Liquid include rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getLiquidIncludeRuleByName(ruleName: string): string {
  for (let i = 0; i < liquidIncludeRules.length; i++) {
    if (liquidIncludeRules[i].name === ruleName) {
      return liquidIncludeRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get total number of Handlebars include rules
 * @returns Total count of Handlebars include rules
 */
export function getHandlebarsIncludeRuleCount(): number {
  return handlebarsIncludeRules.length;
}

/**
 * Get total number of Liquid include rules
 * @returns Total count of Liquid include rules
 */
export function getLiquidIncludeRuleCount(): number {
  return liquidIncludeRules.length;
}


// ================== Include Transformation Functions ==================

/**
 * Apply Handlebars include transformations
 * @param input - Handlebars template content
 * @returns Content with include transformations applied
 */
export function applyHandlebarsIncludeTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply include transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

/**
 * Apply Liquid include transformations
 * @param input - Liquid template content
 * @returns Content with include transformations applied
 */
export function applyLiquidIncludeTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply include transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}
