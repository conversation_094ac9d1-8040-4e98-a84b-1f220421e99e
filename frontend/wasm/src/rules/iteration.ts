/**
 * WASM ITERATION Rules for HBS2Sline Converter
 * AssemblyScript implementation of iteration conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/iteration/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T02:04:57.016Z
 */

// ================== Rule Structure ==================
class IterationRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Iteration Rules ==================
const handlebarsIterationRules: IterationRule[] = [];

function initializeHandlebarsIterationRules(): void {
  handlebarsIterationRules.push(new IterationRule(
    "each 循环转换 - 带别名和索引",
    "iteration",
    "转换 each 循环带别名和索引",
    "",
    "{{#for $2 in $1}}"
  ));
  
  // Example: {{#each products as |product index|}} -> {{#for product in products}}
  
  handlebarsIterationRules.push(new IterationRule(
    "each 循环转换 - 带别名",
    "iteration",
    "转换 each 循环带别名",
    "",
    "{{#for $2 in $1}}"
  ));
  
  // Example: {{#each products as |product|}} -> {{#for product in products}}
  
  handlebarsIterationRules.push(new IterationRule(
    "each 循环转换 - 基础",
    "iteration",
    "转换基础 each 循环",
    "",
    "{{#for item in $1}}"
  ));
  
  // Example: {{#each products}} -> {{#for item in products}}
  
  handlebarsIterationRules.push(new IterationRule(
    "each 结束标签",
    "iteration",
    "转换 each 结束标签",
    "",
    "{{/for}}"
  ));
  
  // Example: {{/each}} -> {{/for}}
  
  handlebarsIterationRules.push(new IterationRule(
    "循环索引变量转换",
    "iteration",
    "转换循环内的索引变量",
    "",
    "{{forloop.index0}}"
  ));
  
  // Example: {{@index}} -> {{forloop.index0}}
  
  handlebarsIterationRules.push(new IterationRule(
    "循环第一项变量转换",
    "iteration",
    "转换循环内的第一项检查",
    "",
    "{{forloop.first}}"
  ));
  
  // Example: {{@first}} -> {{forloop.first}}
  
  handlebarsIterationRules.push(new IterationRule(
    "循环最后项变量转换",
    "iteration",
    "转换循环内的最后项检查",
    "",
    "{{forloop.last}}"
  ));
  
  // Example: {{@last}} -> {{forloop.last}}
  
  handlebarsIterationRules.push(new IterationRule(
    "循环键值变量转换",
    "iteration",
    "转换循环内的键值访问",
    "",
    "{{forloop.key}}"
  ));
  
  // Example: {{@key}} -> {{forloop.key}}
  
  handlebarsIterationRules.push(new IterationRule(
    "当前项变量转换",
    "iteration",
    "转换循环内的当前项引用",
    "",
    "{{item}}"
  ));
  
  // Example: {{this}} -> {{item}}
  
}


// ================== Liquid Iteration Rules ==================
const liquidIterationRules: IterationRule[] = [];

function initializeLiquidIterationRules(): void {
  liquidIterationRules.push(new IterationRule(
    "Liquid for 标签转换 - 基础循环",
    "iteration",
    "转换 Liquid for 标签到 Sline for 标签",
    "",
    "{{#for product in collection.products}}"
  ));
  
  // Example: {% for product in collection.products %} -> {{#for product in collection.products}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid for 标签转换 - 带过滤器",
    "iteration",
    "转换带过滤器的 Liquid for 标签到 Sline for 标签",
    "",
    "{{#for product in collection.products|limit(5)}}"
  ));
  
  // Example: {% for product in collection.products limit: 5 %} -> {{#for product in collection.products|limit(5)}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid for 标签转换 - 范围循环",
    "iteration",
    "转换 Liquid 范围循环到 Sline for 标签",
    "",
    "{{#for i in 5|range(1)}}"
  ));
  
  // Example: {% for i in (1..5) %} -> {{#for i in 5|range(1)}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid endfor 标签转换",
    "iteration",
    "转换 Liquid endfor 标签到 Sline /for 标签",
    "",
    "{{/for}}"
  ));
  
  // Example: {% endfor %} -> {{/for}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid for-else 标签转换",
    "iteration",
    "转换 Liquid for 循环中的 else 标签到 Sline else 标签",
    "",
    "{{#else /}}"
  ));
  
  // Example: {% else %} -> {{#else /}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid break 标签转换",
    "iteration",
    "转换 Liquid break 标签到 Sline break 标签",
    "",
    "{{#break /}}"
  ));
  
  // Example: {% break %} -> {{#break /}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid continue 标签转换",
    "iteration",
    "转换 Liquid continue 标签到 Sline continue 标签",
    "",
    "{{#continue /}}"
  ));
  
  // Example: {% continue %} -> {{#continue /}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid tablerow 标签转换",
    "iteration",
    "转换 Liquid tablerow 标签到 Sline tablerow 标签",
    "",
    "{{#tablerow product in collection.products cols=\"3\"}}"
  ));
  
  // Example: {% tablerow product in collection.products cols: 3 %} -> {{#tablerow product in collection.products cols="3"}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid endtablerow 标签转换",
    "iteration",
    "转换 Liquid endtablerow 标签到 Sline /tablerow 标签",
    "",
    "{{/tablerow}}"
  ));
  
  // Example: {% endtablerow %} -> {{/tablerow}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid cycle 标签转换",
    "iteration",
    "转换 Liquid cycle 标签到 Sline cycle 标签",
    "",
    "{{#cycle \"odd\", \"even\"}}"
  ));
  
  // Example: {% cycle "odd", "even" %} -> {{#cycle "odd", "even"}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid forloop 变量转换",
    "iteration",
    "转换 Liquid forloop 变量到 Sline 循环变量",
    "",
    "{{@index}}"
  ));
  
  // Example: {{ forloop.index }} -> {{@index}}
  
  liquidIterationRules.push(new IterationRule(
    "Liquid tablerowloop 变量转换",
    "iteration",
    "转换 Liquid tablerowloop 变量到 Sline 循环变量",
    "",
    "{{@index}}"
  ));
  
  // Example: {{ tablerowloop.index }} -> {{@index}}
  
}


// ================== Statistics Class ==================
class IterationStats {
  totalHandlebarsRules: number;
  totalLiquidRules: number;
  forRules: number;
  eachRules: number;
  whileRules: number;
  tablerowRules: number;
  variableRules: number;
  controlRules: number;
  
  constructor() {
    this.totalHandlebarsRules = 0;
    this.totalLiquidRules = 0;
    this.forRules = 0;
    this.eachRules = 0;
    this.whileRules = 0;
    this.tablerowRules = 0;
    this.variableRules = 0;
    this.controlRules = 0;
  }
}


// ================== Exported Functions ==================

/**
 * Initialize the iteration rules system
 */
export function initializeIterationRules(): void {
  initializeHandlebarsIterationRules();
  initializeLiquidIterationRules();
}

/**
 * Get iteration rule statistics
 * @returns Iteration rule statistics
 */
export function getIterationStats(): IterationStats {
  const stats = new IterationStats();
  stats.totalHandlebarsRules = handlebarsIterationRules.length;
  stats.totalLiquidRules = liquidIterationRules.length;
  
  // Count rule types
  // TODO: Add specific rule type counting logic
  
  return stats;
}

/**
 * Get Handlebars iteration rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getHandlebarsIterationRuleByName(ruleName: string): string {
  for (let i = 0; i < handlebarsIterationRules.length; i++) {
    if (handlebarsIterationRules[i].name === ruleName) {
      return handlebarsIterationRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get Liquid iteration rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getLiquidIterationRuleByName(ruleName: string): string {
  for (let i = 0; i < liquidIterationRules.length; i++) {
    if (liquidIterationRules[i].name === ruleName) {
      return liquidIterationRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get total number of Handlebars iteration rules
 * @returns Total count of Handlebars iteration rules
 */
export function getHandlebarsIterationRuleCount(): number {
  return handlebarsIterationRules.length;
}

/**
 * Get total number of Liquid iteration rules
 * @returns Total count of Liquid iteration rules
 */
export function getLiquidIterationRuleCount(): number {
  return liquidIterationRules.length;
}


// ================== Iteration Transformation Functions ==================

/**
 * Apply Handlebars iteration transformations
 * @param input - Handlebars template content
 * @returns Content with iteration transformations applied
 */
export function applyHandlebarsIterationTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply iteration transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

/**
 * Apply Liquid iteration transformations
 * @param input - Liquid template content
 * @returns Content with iteration transformations applied
 */
export function applyLiquidIterationTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply iteration transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}
