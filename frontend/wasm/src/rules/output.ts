/**
 * WASM OUTPUT Rules for HBS2Sline Converter
 * AssemblyScript implementation of output conversion rules
 * 
 * ⚠️  AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
 * Generated from: frontend/rules/output/*.json
 * Generator: wasm/scripts/generate-rules.js
 * Generated at: 2025-08-06T02:04:57.017Z
 */

// ================== Rule Structure ==================
class OutputRule {
  name: string;
  category: string;
  description: string;
  pattern: string;
  replacement: string;
  
  constructor(name: string, category: string, description: string, pattern: string, replacement: string) {
    this.name = name;
    this.category = category;
    this.description = description;
    this.pattern = pattern;
    this.replacement = replacement;
  }
}

// ================== Handlebars Output Rules ==================
const handlebarsOutputRules: OutputRule[] = [];

function initializeHandlebarsOutputRules(): void {
  handlebarsOutputRules.push(new OutputRule(
    "原始输出转换",
    "output",
    "转换三重花括号原始输出到 raw 过滤器",
    "",
    "{{$1|raw()}}"
  ));
  
  // Example: {{{product.description}}} -> {{product.description|raw()}}
  
  handlebarsOutputRules.push(new OutputRule(
    "注释转换 - 块注释（已经是正确格式）",
    "output",
    "Handlebars 块注释已经是 Sline 兼容格式，无需转换",
    "",
    "{{!-- $1 --}}"
  ));
  
  // Example: {{!-- This is a comment --}} -> {{!-- This is a comment --}}
  
  handlebarsOutputRules.push(new OutputRule(
    "注释转换 - 简单注释",
    "output",
    "转换简单注释格式到 Sline",
    "",
    "{{!-- $1 --}}"
  ));
  
  // Example: {{! This is a comment}} -> {{!-- This is a comment --}}
  
}


// ================== Liquid Output Rules ==================
const liquidOutputRules: OutputRule[] = [];

function initializeLiquidOutputRules(): void {
  liquidOutputRules.push(new OutputRule(
    "Liquid 变量输出转换",
    "output",
    "转换 Liquid 变量输出到 Sline 变量输出",
    "",
    "{{ product.title|upcase() }}"
  ));
  
  // Example: {{ product.title | upcase }} -> {{ product.title|upcase() }}
  
  liquidOutputRules.push(new OutputRule(
    "Liquid 数学表达式输出转换",
    "output",
    "转换包含数学运算的 Liquid 变量输出",
    "",
    "{{ product.price|plus(10) }}"
  ));
  
  // Example: {{ product.price | plus: 10 }} -> {{ product.price|plus(10) }}
  
  liquidOutputRules.push(new OutputRule(
    "Liquid 字符串操作输出转换",
    "output",
    "转换包含字符串操作的 Liquid 变量输出",
    "",
    "{{ product.title|replace(\"old\", \"new\") }}"
  ));
  
  // Example: {{ product.title | replace: "old", "new" }} -> {{ product.title|replace("old", "new") }}
  
}


// ================== Statistics Class ==================
class OutputStats {
  totalHandlebarsRules: number;
  totalLiquidRules: number;
  rawOutputRules: number;
  commentRules: number;
  variableOutputRules: number;
  filterRules: number;
  mathRules: number;
  stringRules: number;
  
  constructor() {
    this.totalHandlebarsRules = 0;
    this.totalLiquidRules = 0;
    this.rawOutputRules = 0;
    this.commentRules = 0;
    this.variableOutputRules = 0;
    this.filterRules = 0;
    this.mathRules = 0;
    this.stringRules = 0;
  }
}


// ================== Exported Functions ==================

/**
 * Initialize the output rules system
 */
export function initializeOutputRules(): void {
  initializeHandlebarsOutputRules();
  initializeLiquidOutputRules();
}

/**
 * Get output rule statistics
 * @returns Output rule statistics
 */
export function getOutputStats(): OutputStats {
  const stats = new OutputStats();
  stats.totalHandlebarsRules = handlebarsOutputRules.length;
  stats.totalLiquidRules = liquidOutputRules.length;
  
  // Count rule types
  // TODO: Add specific rule type counting logic
  
  return stats;
}

/**
 * Get Handlebars output rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getHandlebarsOutputRuleByName(ruleName: string): string {
  for (let i = 0; i < handlebarsOutputRules.length; i++) {
    if (handlebarsOutputRules[i].name === ruleName) {
      return handlebarsOutputRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get Liquid output rule by name
 * @param ruleName - Rule name to find
 * @returns Rule replacement or empty string if not found
 */
export function getLiquidOutputRuleByName(ruleName: string): string {
  for (let i = 0; i < liquidOutputRules.length; i++) {
    if (liquidOutputRules[i].name === ruleName) {
      return liquidOutputRules[i].replacement;
    }
  }
  return "";
}

/**
 * Get total number of Handlebars output rules
 * @returns Total count of Handlebars output rules
 */
export function getHandlebarsOutputRuleCount(): number {
  return handlebarsOutputRules.length;
}

/**
 * Get total number of Liquid output rules
 * @returns Total count of Liquid output rules
 */
export function getLiquidOutputRuleCount(): number {
  return liquidOutputRules.length;
}


// ================== Output Transformation Functions ==================

/**
 * Apply Handlebars output transformations
 * @param input - Handlebars template content
 * @returns Content with output transformations applied
 */
export function applyHandlebarsOutputTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply output transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

/**
 * Apply Liquid output transformations
 * @param input - Liquid template content
 * @returns Content with output transformations applied
 */
export function applyLiquidOutputTransforms(input: string): string {
  // Handle null input
  if (input === null) {
    return "";
  }

  let output = input;

  // Apply output transformations
  // TODO: Implement specific transformation logic
  
  return output;
}

// ================== Helper Functions ==================

/**
 * Simple string replacement function
 * @param input - Input string
 * @param search - String to search for
 * @param replacement - Replacement string
 * @returns String with replacements
 */
function simpleReplace(input: string, search: string, replacement: string): string {
  let result = input;
  let index = result.indexOf(search);

  while (index !== -1) {
    result = result.substring(0, index) + replacement + result.substring(index + search.length);
    index = result.indexOf(search, index + replacement.length);
  }

  return result;
}
