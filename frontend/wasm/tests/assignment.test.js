/**
 * Unit tests for WASM assignment rules
 * Tests the assignment rule functionality in the HBS2Sline WASM converter
 */

const fs = require('fs');
const path = require('path');

describe('WASM Assignment Rules Tests', () => {
  let wasmModule;
  let wasmInstance;

  beforeAll(async () => {
    // Load WASM module
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    const wasmBuffer = fs.readFileSync(wasmPath);
    wasmModule = await WebAssembly.compile(wasmBuffer);
    wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });
  });

  test('should export assignment rule functions', () => {
    const { exports } = wasmInstance;
    
    expect(typeof exports.testHandlebarsAssignmentRule).toBe('function');
    expect(typeof exports.testLiquidAssignmentRule).toBe('function');
    expect(typeof exports.getAssignmentRuleStats).toBe('function');
    expect(typeof exports.testHandlebarsAssignmentTransformation).toBe('function');
    expect(typeof exports.testLiquidAssignmentTransformation).toBe('function');
  });

  test('should initialize converter with assignment rules without errors', () => {
    const { exports } = wasmInstance;
    
    expect(() => exports.initializeConverter()).not.toThrow();
  });

  test('should map basic Handlebars assignment rules correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test basic assignment rule mappings
    // Note: String handling in WASM requires proper setup
    // For now, we test that the functions exist and can be called
    expect(typeof exports.testHandlebarsAssignmentRule).toBe('function');
  });

  test('should map basic Liquid assignment rules correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test Liquid assignment rule mappings
    expect(typeof exports.testLiquidAssignmentRule).toBe('function');
  });

  test('should provide assignment rule statistics', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test assignment rule statistics function
    expect(typeof exports.getAssignmentRuleStats).toBe('function');
  });

  test('should handle assignment transformations', () => {
    const { exports } = wasmInstance;
    
    // Test assignment transformation functionality
    expect(typeof exports.testHandlebarsAssignmentTransformation).toBe('function');
    expect(typeof exports.testLiquidAssignmentTransformation).toBe('function');
  });

  test('should handle conversion with assignment rules', () => {
    const { exports } = wasmInstance;
    
    // Test conversion functionality with assignment rules
    expect(typeof exports.convertHbsToSline).toBe('function');
    expect(typeof exports.convertLiquidToSline).toBe('function');
  });

  test('should handle empty input gracefully', () => {
    const { exports } = wasmInstance;
    
    // Test empty string handling
    // Note: Actual string testing requires proper WASM string handling setup
    expect(() => exports.convertHbsToSline).not.toThrow();
    expect(() => exports.convertLiquidToSline).not.toThrow();
  });

  test('should provide comprehensive mapping statistics including assignments', () => {
    const { exports } = wasmInstance;
    
    // Test comprehensive statistics function
    expect(typeof exports.getAllMappingStats).toBe('function');
  });

  test('should handle assign/var transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test assign/var transformation functionality
    expect(typeof exports.testHandlebarsAssignmentTransformation).toBe('function');
    expect(typeof exports.testLiquidAssignmentTransformation).toBe('function');
  });

  test('should handle capture transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test capture transformation functionality
    expect(typeof exports.testLiquidAssignmentTransformation).toBe('function');
  });

  test('should handle increment/decrement transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test increment/decrement transformation functionality
    expect(typeof exports.testLiquidAssignmentTransformation).toBe('function');
  });

  test('should handle snippet/include transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test snippet/include transformation functionality
    expect(typeof exports.testHandlebarsAssignmentTransformation).toBe('function');
  });
});

// Integration test helper
async function testAssignmentRuleIntegration() {
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });
  
  const { exports } = wasmInstance;
  
  // Initialize converter
  exports.initializeConverter();
  
  console.log('✅ Assignment rule integration test completed');
  return true;
}

module.exports = {
  testAssignmentRuleIntegration
};
