/**
 * Performance benchmark tests for WASM HBS2Sline Converter
 * Tests conversion speed and memory usage
 */

const fs = require('fs');
const path = require('path');

async function runBenchmarks() {
  console.log('🚀 Starting WASM Performance Benchmarks...\n');

  // Load WASM module
  const wasmPath = path.join(__dirname, '../build/converter.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });

  const { exports } = wasmInstance;

  // Initialize converter
  console.log('📋 Initializing converter...');
  const initStart = performance.now();
  exports.initializeConverter();
  const initEnd = performance.now();
  console.log(`✅ Initialization completed in ${(initEnd - initStart).toFixed(2)}ms\n`);

  // Benchmark 1: Basic Math Operations
  console.log('🧮 Benchmark 1: Basic Math Operations');
  const mathStart = performance.now();
  let mathResult = 0;
  for (let i = 0; i < 100000; i++) {
    mathResult = exports.add(i, i + 1);
  }
  const mathEnd = performance.now();
  console.log(`   Operations: 100,000`);
  console.log(`   Time: ${(mathEnd - mathStart).toFixed(2)}ms`);
  console.log(`   Rate: ${(100000 / (mathEnd - mathStart) * 1000).toFixed(0)} ops/sec`);
  console.log(`   Last result: ${mathResult}\n`);

  // Benchmark 2: Statistics Generation
  console.log('📊 Benchmark 2: Statistics Generation');
  const statsStart = performance.now();
  let statsResult = 0;
  for (let i = 0; i < 1000; i++) {
    statsResult = exports.getAllMappingStats();
  }
  const statsEnd = performance.now();
  console.log(`   Operations: 1,000`);
  console.log(`   Time: ${(statsEnd - statsStart).toFixed(2)}ms`);
  console.log(`   Rate: ${(1000 / (statsEnd - statsStart) * 1000).toFixed(0)} ops/sec`);
  console.log(`   Result value: ${statsResult}\n`);

  // Benchmark 3: Rule Statistics
  console.log('📋 Benchmark 3: Rule Statistics');
  const ruleStatsStart = performance.now();
  for (let i = 0; i < 1000; i++) {
    exports.getConditionalRuleStats();
    exports.getIterationRuleStats();
    exports.getAssignmentRuleStats();
    exports.getIncludeRuleStats();
    exports.getCommentRuleStats();
    exports.getOutputRuleStats();
  }
  const ruleStatsEnd = performance.now();
  console.log(`   Operations: 6,000 (6 types × 1,000)`);
  console.log(`   Time: ${(ruleStatsEnd - ruleStatsStart).toFixed(2)}ms`);
  console.log(`   Rate: ${(6000 / (ruleStatsEnd - ruleStatsStart) * 1000).toFixed(0)} ops/sec\n`);

  // Benchmark 4: Version Calls
  console.log('🔢 Benchmark 4: Version Calls');
  const versionStart = performance.now();
  let version = 0;
  for (let i = 0; i < 10000; i++) {
    version = exports.getVersion();
  }
  const versionEnd = performance.now();
  console.log(`   Operations: 10,000`);
  console.log(`   Time: ${(versionEnd - versionStart).toFixed(2)}ms`);
  console.log(`   Rate: ${(10000 / (versionEnd - versionStart) * 1000).toFixed(0)} ops/sec`);
  console.log(`   Version: ${version}\n`);

  // Benchmark 5: Mixed Operations
  console.log('🔄 Benchmark 5: Mixed Operations');
  const mixedStart = performance.now();
  for (let i = 0; i < 1000; i++) {
    exports.add(i, i + 1);
    exports.getVersion();
    if (i % 10 === 0) {
      exports.getAllMappingStats();
    }
    if (i % 5 === 0) {
      exports.getConditionalRuleStats();
    }
  }
  const mixedEnd = performance.now();
  console.log(`   Operations: ~3,400 mixed calls`);
  console.log(`   Time: ${(mixedEnd - mixedStart).toFixed(2)}ms`);
  console.log(`   Rate: ${(3400 / (mixedEnd - mixedStart) * 1000).toFixed(0)} ops/sec\n`);

  // Memory Usage Test
  console.log('💾 Memory Usage Test');
  const memoryBefore = process.memoryUsage();
  
  // Create multiple instances to test memory
  const instances = [];
  for (let i = 0; i < 10; i++) {
    const instance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: () => {}
      }
    });
    instances.push(instance);
    instance.exports.initializeConverter();
  }
  
  const memoryAfter = process.memoryUsage();
  console.log(`   Memory before: ${(memoryBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Memory after: ${(memoryAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Memory per instance: ${((memoryAfter.heapUsed - memoryBefore.heapUsed) / 10 / 1024 / 1024).toFixed(2)} MB\n`);

  // Overall Performance Summary
  const totalTime = performance.now() - initStart;
  console.log('📈 Performance Summary');
  console.log(`   Total benchmark time: ${totalTime.toFixed(2)}ms`);
  console.log(`   WASM module size: ${(wasmBuffer.length / 1024).toFixed(2)} KB`);
  console.log(`   Initialization time: ${(initEnd - initStart).toFixed(2)}ms`);
  console.log(`   Average operation time: ${((mathEnd - mathStart) / 100000).toFixed(6)}ms`);
  
  // Performance Rating
  const mathOpsPerSec = 100000 / (mathEnd - mathStart) * 1000;
  let rating = 'Unknown';
  if (mathOpsPerSec > 1000000) rating = 'Excellent';
  else if (mathOpsPerSec > 500000) rating = 'Very Good';
  else if (mathOpsPerSec > 100000) rating = 'Good';
  else if (mathOpsPerSec > 50000) rating = 'Fair';
  else rating = 'Needs Improvement';
  
  console.log(`   Performance rating: ${rating}`);
  console.log(`   Math operations/sec: ${mathOpsPerSec.toFixed(0)}`);

  console.log('\n🎉 Benchmark completed successfully!');
}

// Error handling wrapper
async function runBenchmarksWithErrorHandling() {
  try {
    await runBenchmarks();
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
    process.exit(1);
  }
}

// Run benchmarks if called directly
if (require.main === module) {
  runBenchmarksWithErrorHandling();
}

module.exports = {
  runBenchmarks,
  runBenchmarksWithErrorHandling
};
