/**
 * Unit tests for WASM conditional rules
 * Tests the conditional rule functionality in the HBS2Sline WASM converter
 */

const fs = require('fs');
const path = require('path');

describe('WASM Conditional Rules Tests', () => {
  let wasmModule;
  let wasmInstance;

  beforeAll(async () => {
    // Load WASM module
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    const wasmBuffer = fs.readFileSync(wasmPath);
    wasmModule = await WebAssembly.compile(wasmBuffer);
    wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });
  });

  test('should export conditional rule functions', () => {
    const { exports } = wasmInstance;
    
    expect(typeof exports.testHandlebarsConditionalRule).toBe('function');
    expect(typeof exports.testLiquidConditionalRule).toBe('function');
    expect(typeof exports.getConditionalRuleStats).toBe('function');
    expect(typeof exports.testHandlebarsConditionalTransformation).toBe('function');
    expect(typeof exports.testLiquidConditionalTransformation).toBe('function');
  });

  test('should initialize converter with conditional rules without errors', () => {
    const { exports } = wasmInstance;
    
    expect(() => exports.initializeConverter()).not.toThrow();
  });

  test('should map basic Handlebars conditional rules correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test basic conditional rule mappings
    // Note: String handling in WASM requires proper setup
    // For now, we test that the functions exist and can be called
    expect(typeof exports.testHandlebarsConditionalRule).toBe('function');
  });

  test('should map basic Liquid conditional rules correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test Liquid conditional rule mappings
    expect(typeof exports.testLiquidConditionalRule).toBe('function');
  });

  test('should provide conditional rule statistics', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test conditional rule statistics function
    expect(typeof exports.getConditionalRuleStats).toBe('function');
  });

  test('should handle conditional transformations', () => {
    const { exports } = wasmInstance;
    
    // Test conditional transformation functionality
    expect(typeof exports.testHandlebarsConditionalTransformation).toBe('function');
    expect(typeof exports.testLiquidConditionalTransformation).toBe('function');
  });

  test('should handle conversion with conditional rules', () => {
    const { exports } = wasmInstance;
    
    // Test conversion functionality with conditional rules
    expect(typeof exports.convertHbsToSline).toBe('function');
    expect(typeof exports.convertLiquidToSline).toBe('function');
  });

  test('should handle empty input gracefully', () => {
    const { exports } = wasmInstance;
    
    // Test empty string handling
    // Note: Actual string testing requires proper WASM string handling setup
    expect(() => exports.convertHbsToSline).not.toThrow();
    expect(() => exports.convertLiquidToSline).not.toThrow();
  });

  test('should provide comprehensive mapping statistics including conditionals', () => {
    const { exports } = wasmInstance;
    
    // Test comprehensive statistics function
    expect(typeof exports.getAllMappingStats).toBe('function');
  });

  test('should handle if/unless transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test if/unless transformation functionality
    expect(typeof exports.testHandlebarsConditionalTransformation).toBe('function');
    expect(typeof exports.testLiquidConditionalTransformation).toBe('function');
  });

  test('should handle case/when transformations', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test case/when transformation functionality
    expect(typeof exports.testHandlebarsConditionalTransformation).toBe('function');
    expect(typeof exports.testLiquidConditionalTransformation).toBe('function');
  });

  test('should handle complex conditional expressions', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test complex conditional transformation functionality
    expect(typeof exports.testLiquidConditionalTransformation).toBe('function');
  });
});

// Integration test helper
async function testConditionalRuleIntegration() {
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });
  
  const { exports } = wasmInstance;
  
  // Initialize converter
  exports.initializeConverter();
  
  console.log('✅ Conditional rule integration test completed');
  return true;
}

module.exports = {
  testConditionalRuleIntegration
};
