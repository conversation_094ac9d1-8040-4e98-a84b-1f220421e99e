/**
 * Unit tests for WASM converter module
 * Tests the basic functionality of the HBS2Sline WASM converter
 */

const fs = require('fs');
const path = require('path');

describe('WASM Converter Tests', () => {
  let wasmModule;
  let wasmInstance;

  beforeAll(async () => {
    // Load WASM module
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    const wasmBuffer = fs.readFileSync(wasmPath);
    wasmModule = await WebAssembly.compile(wasmBuffer);
    wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });
  });

  test('should export required functions', () => {
    const { exports } = wasmInstance;
    
    expect(typeof exports.add).toBe('function');
    expect(typeof exports.convertHbsToSline).toBe('function');
    expect(typeof exports.getVersion).toBe('function');
    expect(typeof exports.allocate).toBe('function');
    expect(typeof exports.deallocate).toBe('function');
  });

  test('should perform basic math operations', () => {
    const { exports } = wasmInstance;
    
    expect(exports.add(5, 3)).toBe(8);
    expect(exports.add(0, 0)).toBe(0);
    expect(exports.add(-1, 1)).toBe(0);
    expect(exports.add(100, 200)).toBe(300);
  });

  test('should have memory allocation functions', () => {
    const { exports } = wasmInstance;
    
    // Test memory allocation
    const ptr = exports.allocate(1024);
    expect(typeof ptr).toBe('number');
    expect(ptr).toBeGreaterThan(0);
    
    // Test deallocation (should not throw)
    expect(() => exports.deallocate(ptr)).not.toThrow();
  });

  test('should have conversion function available', () => {
    const { exports } = wasmInstance;
    
    // Note: String handling in WASM requires proper setup
    // For now, just test that the function exists
    expect(typeof exports.convertHbsToSline).toBe('function');
  });

  test('should have version function available', () => {
    const { exports } = wasmInstance;
    
    // Note: String handling in WASM requires proper setup
    // For now, just test that the function exists
    expect(typeof exports.getVersion).toBe('function');
  });
});

module.exports = {
  testWasmModule: async () => {
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    const wasmBuffer = fs.readFileSync(wasmPath);
    const wasmModule = await WebAssembly.compile(wasmBuffer);
    const wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });
    
    return wasmInstance.exports;
  }
};
