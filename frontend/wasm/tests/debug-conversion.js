/**
 * Debug conversion functions specifically
 */

const fs = require('fs');
const path = require('path');

async function debugConversion() {
  console.log('🔍 Debugging Conversion Functions...\n');

  // Load WASM module
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });

  const { exports } = wasmInstance;

  // Initialize converter
  console.log('📋 Initializing converter...');
  exports.initializeConverter();
  console.log('✅ Initialization completed\n');

  // Test 1: Call with empty string
  console.log('🧪 Test 1: Call with empty string');
  try {
    const result1 = exports.convertHbsToSline("");
    console.log(`   ✅ convertHbsToSline("") = "${result1}"`);
  } catch (error) {
    console.log(`   ❌ convertHbsToSline("") failed: ${error.message}`);
  }

  try {
    const result2 = exports.convertLiquidToSline("");
    console.log(`   ✅ convertLiquidToSline("") = "${result2}"`);
  } catch (error) {
    console.log(`   ❌ convertLiquidToSline("") failed: ${error.message}`);
  }

  // Test 2: Call with simple string
  console.log('\n🧪 Test 2: Call with simple string');
  try {
    const result3 = exports.convertHbsToSline("hello");
    console.log(`   ✅ convertHbsToSline("hello") = "${result3}"`);
  } catch (error) {
    console.log(`   ❌ convertHbsToSline("hello") failed: ${error.message}`);
  }

  try {
    const result4 = exports.convertLiquidToSline("world");
    console.log(`   ✅ convertLiquidToSline("world") = "${result4}"`);
  } catch (error) {
    console.log(`   ❌ convertLiquidToSline("world") failed: ${error.message}`);
  }

  // Test 3: Call transformation functions with empty string
  console.log('\n🧪 Test 3: Call transformation functions with empty string');
  const transformFunctions = [
    'testHandlebarsConditionalTransformation',
    'testLiquidConditionalTransformation',
    'testHandlebarsIterationTransformation',
    'testLiquidIterationTransformation'
  ];

  for (const funcName of transformFunctions) {
    try {
      const result = exports[funcName]("");
      console.log(`   ✅ ${funcName}("") = "${result}"`);
    } catch (error) {
      console.log(`   ❌ ${funcName}("") failed: ${error.message}`);
    }
  }

  // Test 4: Call transformation functions with simple string
  console.log('\n🧪 Test 4: Call transformation functions with simple string');
  for (const funcName of transformFunctions) {
    try {
      const result = exports[funcName]("test");
      console.log(`   ✅ ${funcName}("test") = "${result}"`);
    } catch (error) {
      console.log(`   ❌ ${funcName}("test") failed: ${error.message}`);
    }
  }

  // Test 5: Test rule lookup functions
  console.log('\n🧪 Test 5: Test rule lookup functions');
  const ruleFunctions = [
    'testHandlebarsConditionalRule',
    'testLiquidConditionalRule'
  ];

  for (const funcName of ruleFunctions) {
    try {
      const result1 = exports[funcName]("");
      console.log(`   ✅ ${funcName}("") = "${result1}"`);
      
      const result2 = exports[funcName]("if");
      console.log(`   ✅ ${funcName}("if") = "${result2}"`);
    } catch (error) {
      console.log(`   ❌ ${funcName} failed: ${error.message}`);
    }
  }

  console.log('\n🎉 Debug completed!');
}

// Run debug
if (require.main === module) {
  debugConversion().catch(console.error);
}

module.exports = { debugConversion };
