/**
 * Debug test for statistics functions
 */

const fs = require('fs');
const path = require('path');

async function debugStats() {
  console.log('🔍 Debugging Statistics Functions...\n');

  // Load WASM module
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });

  const { exports } = wasmInstance;

  // Initialize converter
  console.log('📋 Initializing converter...');
  exports.initializeConverter();
  console.log('✅ Initialization completed\n');

  // Test getAllMappingStats
  console.log('📊 Testing getAllMappingStats...');
  try {
    const stats = exports.getAllMappingStats();
    console.log('   Type:', typeof stats);
    console.log('   Value:', stats);
    console.log('   Length:', stats ? stats.length : 'undefined');
    
    if (stats && typeof stats === 'string') {
      console.log('   First 100 chars:', stats.substring(0, 100));
      try {
        const parsed = JSON.parse(stats);
        console.log('   ✅ Valid JSON');
        console.log('   Objects:', parsed.objects);
      } catch (e) {
        console.log('   ❌ Invalid JSON:', e.message);
      }
    }
  } catch (error) {
    console.log('   ❌ Error calling getAllMappingStats:', error);
  }

  console.log('\n📋 Testing individual stats functions...');
  
  // Test individual stats functions
  const statsFunctions = [
    'getConditionalRuleStats',
    'getIterationRuleStats',
    'getAssignmentRuleStats',
    'getIncludeRuleStats',
    'getCommentRuleStats',
    'getOutputRuleStats'
  ];

  for (const funcName of statsFunctions) {
    try {
      if (typeof exports[funcName] === 'function') {
        const result = exports[funcName]();
        console.log(`   ${funcName}: ${typeof result} (length: ${result ? result.length : 'undefined'})`);
      } else {
        console.log(`   ${funcName}: function not found`);
      }
    } catch (error) {
      console.log(`   ${funcName}: error - ${error.message}`);
    }
  }

  console.log('\n🎉 Debug completed!');
}

// Run debug
if (require.main === module) {
  debugStats().catch(console.error);
}

module.exports = { debugStats };
