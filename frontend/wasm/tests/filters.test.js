/**
 * Unit tests for WASM filter mappings
 * Tests the filter mapping functionality in the HBS2Sline WASM converter
 */

const fs = require('fs');
const path = require('path');

describe('WASM Filter Mappings Tests', () => {
  let wasmModule;
  let wasmInstance;

  beforeAll(async () => {
    // Load WASM module
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    const wasmBuffer = fs.readFileSync(wasmPath);
    wasmModule = await WebAssembly.compile(wasmBuffer);
    wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });
  });

  test('should export filter mapping functions', () => {
    const { exports } = wasmInstance;
    
    expect(typeof exports.testFilterMapping).toBe('function');
    expect(typeof exports.testLiquidFilterMapping).toBe('function');
    expect(typeof exports.getFilterMappingStats).toBe('function');
    expect(typeof exports.getAllMappingStats).toBe('function');
    expect(typeof exports.testFilterTransformation).toBe('function');
    expect(typeof exports.testLiquidFilterTransformation).toBe('function');
  });

  test('should initialize converter with filters without errors', () => {
    const { exports } = wasmInstance;
    
    expect(() => exports.initializeConverter()).not.toThrow();
  });

  test('should map basic Handlebars filters correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test basic filter mappings
    // Note: String handling in WASM requires proper setup
    // For now, we test that the functions exist and can be called
    expect(typeof exports.testFilterMapping).toBe('function');
  });

  test('should map basic Liquid filters correctly', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test Liquid filter mappings
    expect(typeof exports.testLiquidFilterMapping).toBe('function');
  });

  test('should provide filter mapping statistics', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test filter statistics function
    expect(typeof exports.getFilterMappingStats).toBe('function');
  });

  test('should provide comprehensive mapping statistics', () => {
    const { exports } = wasmInstance;
    
    // Initialize converter first
    exports.initializeConverter();
    
    // Test comprehensive statistics function
    expect(typeof exports.getAllMappingStats).toBe('function');
  });

  test('should handle filter transformations', () => {
    const { exports } = wasmInstance;
    
    // Test filter transformation functionality
    expect(typeof exports.testFilterTransformation).toBe('function');
    expect(typeof exports.testLiquidFilterTransformation).toBe('function');
  });

  test('should handle conversion with filter mappings', () => {
    const { exports } = wasmInstance;
    
    // Test conversion functionality with filters
    expect(typeof exports.convertHbsToSline).toBe('function');
    expect(typeof exports.convertLiquidToSline).toBe('function');
  });

  test('should handle empty input gracefully', () => {
    const { exports } = wasmInstance;
    
    // Test empty string handling
    // Note: Actual string testing requires proper WASM string handling setup
    expect(() => exports.convertHbsToSline).not.toThrow();
    expect(() => exports.convertLiquidToSline).not.toThrow();
  });
});

// Integration test helper
async function testFilterMappingIntegration() {
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });
  
  const { exports } = wasmInstance;
  
  // Initialize converter
  exports.initializeConverter();
  
  console.log('✅ Filter mapping integration test completed');
  return true;
}

module.exports = {
  testFilterMappingIntegration
};
