/**
 * Comprehensive logic validation test
 * Tests all conversion rules and mappings for correctness
 */

const fs = require('fs');
const path = require('path');

async function validateLogic() {
  console.log('🔍 Comprehensive Logic Validation Test...\n');

  // Load WASM module
  const wasmPath = path.join(__dirname, '../build/converter.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });

  const { exports } = wasmInstance;

  // Initialize converter
  console.log('📋 Initializing converter...');
  exports.initializeConverter();
  console.log('✅ Initialization completed\n');

  let totalTests = 0;
  let passedTests = 0;

  // Helper function to run test
  function runTest(testName, testFn) {
    totalTests++;
    try {
      const result = testFn();
      if (result) {
        console.log(`   ✅ ${testName}`);
        passedTests++;
      } else {
        console.log(`   ❌ ${testName}`);
      }
    } catch (error) {
      console.log(`   ❌ ${testName} - Error: ${error.message}`);
    }
  }

  // Test 1: Basic Math Operations
  console.log('🧮 Testing Basic Math Operations...');
  runTest('add(5, 3) = 8', () => exports.add(5, 3) === 8);
  runTest('add(0, 0) = 0', () => exports.add(0, 0) === 0);
  runTest('add(-5, 5) = 0', () => exports.add(-5, 5) === 0);
  runTest('add(100, 200) = 300', () => exports.add(100, 200) === 300);

  // Test 2: Version Function
  console.log('\n🔢 Testing Version Function...');
  runTest('getVersion() returns number', () => typeof exports.getVersion() === 'number');
  runTest('getVersion() > 0', () => exports.getVersion() > 0);

  // Test 3: Function Existence
  console.log('\n🔄 Testing Function Existence...');
  const requiredFunctions = [
    'initializeConverter',
    'convertHbsToSline',
    'convertLiquidToSline',
    'getAllMappingStats',
    'getConditionalRuleStats',
    'getIterationRuleStats',
    'getAssignmentRuleStats',
    'getIncludeRuleStats',
    'getCommentRuleStats',
    'getOutputRuleStats',
    'testHandlebarsConditionalRule',
    'testLiquidConditionalRule',
    'testHandlebarsIterationRule',
    'testLiquidIterationRule',
    'testHandlebarsAssignmentRule',
    'testLiquidAssignmentRule',
    'testHandlebarsIncludeRule',
    'testLiquidIncludeRule',
    'testHandlebarsCommentRule',
    'testLiquidCommentRule',
    'testHandlebarsOutputRule',
    'testLiquidOutputRule'
  ];

  for (const funcName of requiredFunctions) {
    runTest(`${funcName} exists`, () => typeof exports[funcName] === 'function');
  }

  // Test 4: Statistics Functions Return Values
  console.log('\n📊 Testing Statistics Functions...');
  const statsFunctions = [
    'getAllMappingStats',
    'getConditionalRuleStats',
    'getIterationRuleStats',
    'getAssignmentRuleStats',
    'getIncludeRuleStats',
    'getCommentRuleStats',
    'getOutputRuleStats'
  ];

  for (const funcName of statsFunctions) {
    runTest(`${funcName} returns value`, () => {
      const result = exports[funcName]();
      return result !== undefined && result !== null;
    });
  }

  // Test 5: Transformation Functions
  console.log('\n🔄 Testing Transformation Functions...');
  const transformFunctions = [
    'testHandlebarsConditionalTransformation',
    'testLiquidConditionalTransformation',
    'testHandlebarsIterationTransformation',
    'testLiquidIterationTransformation',
    'testHandlebarsAssignmentTransformation',
    'testLiquidAssignmentTransformation',
    'testHandlebarsIncludeTransformation',
    'testLiquidIncludeTransformation',
    'testHandlebarsCommentTransformation',
    'testLiquidCommentTransformation',
    'testHandlebarsOutputTransformation',
    'testLiquidOutputTransformation'
  ];

  for (const funcName of transformFunctions) {
    if (typeof exports[funcName] === 'function') {
      runTest(`${funcName} callable`, () => {
        // Test with empty string - should not crash
        const result = exports[funcName]("");
        return result !== undefined;
      });
    }
  }

  // Test 6: Rule Lookup Functions
  console.log('\n🔍 Testing Rule Lookup Functions...');
  const ruleLookupFunctions = [
    'testHandlebarsConditionalRule',
    'testLiquidConditionalRule',
    'testHandlebarsIterationRule',
    'testLiquidIterationRule',
    'testHandlebarsAssignmentRule',
    'testLiquidAssignmentRule',
    'testHandlebarsIncludeRule',
    'testLiquidIncludeRule',
    'testHandlebarsCommentRule',
    'testLiquidCommentRule',
    'testHandlebarsOutputRule',
    'testLiquidOutputRule'
  ];

  for (const funcName of ruleLookupFunctions) {
    if (typeof exports[funcName] === 'function') {
      runTest(`${funcName} callable`, () => {
        // Test with empty string - should return empty string or value
        const result = exports[funcName]("");
        return result !== undefined;
      });
    }
  }

  // Test 7: Main Conversion Functions
  console.log('\n🔄 Testing Main Conversion Functions...');
  runTest('convertHbsToSline callable', () => {
    const result = exports.convertHbsToSline("");
    return result !== undefined;
  });

  runTest('convertLiquidToSline callable', () => {
    const result = exports.convertLiquidToSline("");
    return result !== undefined;
  });

  // Test 8: Performance Validation
  console.log('\n⚡ Testing Performance...');
  runTest('Math operations are fast', () => {
    const start = Date.now();
    for (let i = 0; i < 10000; i++) {
      exports.add(i, i + 1);
    }
    const end = Date.now();
    const duration = end - start;
    return duration < 100; // Should complete in less than 100ms
  });

  runTest('Version calls are fast', () => {
    const start = Date.now();
    for (let i = 0; i < 1000; i++) {
      exports.getVersion();
    }
    const end = Date.now();
    const duration = end - start;
    return duration < 50; // Should complete in less than 50ms
  });

  // Test 9: Memory Stability
  console.log('\n💾 Testing Memory Stability...');
  runTest('Multiple initializations', () => {
    for (let i = 0; i < 10; i++) {
      exports.initializeConverter();
    }
    return true; // Should not crash
  });

  runTest('Multiple statistics calls', () => {
    for (let i = 0; i < 100; i++) {
      exports.getAllMappingStats();
    }
    return true; // Should not crash
  });

  // Summary
  console.log('\n📈 Test Summary:');
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${totalTests - passedTests}`);
  console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Logic validation successful!');
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
    return false;
  }
}

// Run validation
if (require.main === module) {
  validateLogic().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

module.exports = { validateLogic };
