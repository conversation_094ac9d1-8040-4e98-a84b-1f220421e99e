/**
 * Simple functionality test
 */

const fs = require('fs');
const path = require('path');

async function simpleTest() {
  console.log('🧪 Simple Functionality Test...\n');

  // Load WASM module
  const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
  const wasmBuffer = fs.readFileSync(wasmPath);
  const wasmModule = await WebAssembly.compile(wasmBuffer);
  const wasmInstance = await WebAssembly.instantiate(wasmModule, {
    env: {
      abort: (message, fileName, lineNumber, columnNumber) => {
        console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
      }
    }
  });

  const { exports } = wasmInstance;

  // Test basic math
  console.log('🧮 Testing basic math...');
  const mathResult = exports.add(10, 20);
  console.log(`   add(10, 20) = ${mathResult}`);
  console.log(`   ✅ ${mathResult === 30 ? 'PASSED' : 'FAILED'}\n`);

  // Test version
  console.log('🔢 Testing version...');
  const version = exports.getVersion();
  console.log(`   getVersion() = ${version}`);
  console.log(`   Type: ${typeof version}`);
  console.log(`   ✅ ${typeof version === 'number' ? 'PASSED' : 'FAILED'}\n`);

  // Test initialization
  console.log('📋 Testing initialization...');
  try {
    exports.initializeConverter();
    console.log('   ✅ initializeConverter() completed without error\n');
  } catch (error) {
    console.log(`   ❌ initializeConverter() failed: ${error}\n`);
  }

  // Test conversion functions exist
  console.log('🔄 Testing conversion functions...');
  const conversionFunctions = [
    'convertHbsToSline',
    'convertLiquidToSline'
  ];

  for (const funcName of conversionFunctions) {
    const exists = typeof exports[funcName] === 'function';
    console.log(`   ${funcName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
  }

  // Test statistics functions exist
  console.log('\n📊 Testing statistics functions...');
  const statsFunctions = [
    'getAllMappingStats',
    'getConditionalRuleStats',
    'getIterationRuleStats',
    'getAssignmentRuleStats',
    'getIncludeRuleStats',
    'getCommentRuleStats',
    'getOutputRuleStats'
  ];

  for (const funcName of statsFunctions) {
    const exists = typeof exports[funcName] === 'function';
    console.log(`   ${funcName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
  }

  // Test rule lookup functions exist
  console.log('\n🔍 Testing rule lookup functions...');
  const ruleFunctions = [
    'testHandlebarsConditionalRule',
    'testLiquidConditionalRule',
    'testHandlebarsIterationRule',
    'testLiquidIterationRule'
  ];

  for (const funcName of ruleFunctions) {
    const exists = typeof exports[funcName] === 'function';
    console.log(`   ${funcName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
  }

  console.log('\n🎉 Simple test completed!');
}

// Run test
if (require.main === module) {
  simpleTest().catch(console.error);
}

module.exports = { simpleTest };
