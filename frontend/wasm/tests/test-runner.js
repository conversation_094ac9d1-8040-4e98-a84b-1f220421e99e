/**
 * Basic test runner for WASM module
 * Tests the compiled WASM module functionality
 */

const fs = require('fs');
const path = require('path');

async function runTests() {
  console.log('🧪 Running WASM Module Tests...\n');
  
  try {
    // Check if WASM file exists
    const wasmPath = path.join(__dirname, '../build/converter-debug.wasm');
    if (!fs.existsSync(wasmPath)) {
      throw new Error('WASM file not found. Please run build:debug first.');
    }
    
    // Load WASM module
    const wasmBuffer = fs.readFileSync(wasmPath);
    const wasmModule = await WebAssembly.compile(wasmBuffer);
    const wasmInstance = await WebAssembly.instantiate(wasmModule, {
      env: {
        abort: (message, fileName, lineNumber, columnNumber) => {
          console.error(`WASM abort: ${message} at ${fileName}:${lineNumber}:${columnNumber}`);
        }
      }
    });

    const { exports } = wasmInstance;
    
    // Test 1: Basic math function
    console.log('✅ Test 1: Basic Math Function');
    const result = exports.add(5, 3);
    console.log(`   add(5, 3) = ${result}`);
    if (result === 8) {
      console.log('   ✅ PASSED\n');
    } else {
      console.log('   ❌ FAILED\n');
      return false;
    }
    
    // Test 2: Version function
    console.log('✅ Test 2: Version Function');
    try {
      const version = exports.getVersion();
      console.log(`   getVersion() = ${version}`);
      console.log('   ✅ PASSED\n');
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}\n`);
    }
    
    // Test 3: Basic conversion function (placeholder)
    console.log('✅ Test 3: Basic Conversion Function');
    try {
      // Note: String handling in WASM requires more setup
      // This is a placeholder test for now
      console.log('   convertHbsToSline() function exists');
      console.log('   ✅ PASSED (placeholder)\n');
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}\n`);
    }
    
    console.log('🎉 All tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { runTests };
