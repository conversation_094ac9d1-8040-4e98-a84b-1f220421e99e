/**
 * Liquid 赋值标签转换规则
 * 处理 assign、capture、increment、decrement 等赋值语句的转换
 */

// ================== Liquid 赋值标签转换规则 ==================
const LIQUID_ASSIGNMENT_TAG_RULES = [
  {
    name: 'Liquid assign 标签转换',
    category: 'assignment',
    description: '转换 Liquid assign 标签到 Sline var 标签',
    pattern: /{%-?\s*assign\s+(\w+)\s*=\s*([^%}]+?)\s*-?%}/g,
    replacement: (match, varName, value) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';

      // 处理值中的过滤器
      let processedValue = value.trim();

      // 先转换带参数的过滤器
      processedValue = processedValue.replace(/\|\s*(\w+):\s*([^|]+)/g, '|$1($2)');
      // 然后转换无参数的过滤器，避免重复转换
      processedValue = processedValue.replace(/\|\s*(\w+)(?!\()/g, '|$1()');

      return `${whitespaceStart}#var ${varName} = ${processedValue} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% assign price = product.price %}',
      sline: '{{#var price = product.price /}}'
    }
  },
  {
    name: 'Liquid assign 标签转换 - 带过滤器',
    category: 'assignment',
    description: '转换带过滤器的 Liquid assign 标签到 Sline var 标签',
    pattern: /{%-?\s*assign\s+(\w+)\s*=\s*([^%}]+?\|[^%}]+?)\s*-?%}/g,
    replacement: (match, varName, valueWithFilters) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理复杂的过滤器链
      let processedValue = valueWithFilters.trim();
      
      // 转换常见的 Liquid 过滤器
      processedValue = processedValue.replace(/\|\s*upcase/g, '|upcase()');
      processedValue = processedValue.replace(/\|\s*downcase/g, '|downcase()');
      processedValue = processedValue.replace(/\|\s*capitalize/g, '|capitalize()');
      processedValue = processedValue.replace(/\|\s*strip/g, '|trim()');
      processedValue = processedValue.replace(/\|\s*lstrip/g, '|trim_left()');
      processedValue = processedValue.replace(/\|\s*rstrip/g, '|trim_right()');
      processedValue = processedValue.replace(/\|\s*size/g, '|size()');
      processedValue = processedValue.replace(/\|\s*first/g, '|first()');
      processedValue = processedValue.replace(/\|\s*last/g, '|last()');
      processedValue = processedValue.replace(/\|\s*reverse/g, '|reverse()');
      processedValue = processedValue.replace(/\|\s*sort/g, '|sort()');
      processedValue = processedValue.replace(/\|\s*uniq/g, '|uniq()');
      
      // 转换带参数的过滤器
      processedValue = processedValue.replace(/\|\s*truncate:\s*(\d+)/g, '|truncate($1)');
      processedValue = processedValue.replace(/\|\s*truncatewords:\s*(\d+)/g, '|truncatewords($1)');
      processedValue = processedValue.replace(/\|\s*slice:\s*(\d+),?\s*(\d+)?/g, (match, start, length) => {
        return length ? `|slice(${start}, ${length})` : `|slice(${start})`;
      });
      processedValue = processedValue.replace(/\|\s*replace:\s*['"]([^'"]*)['"]\s*,\s*['"]([^'"]*)['"]/g, '|replace("$1", "$2")');
      processedValue = processedValue.replace(/\|\s*remove:\s*['"]([^'"]*)['"]/g, '|remove("$1")');
      processedValue = processedValue.replace(/\|\s*append:\s*['"]([^'"]*)['"]/g, '|append("$1")');
      processedValue = processedValue.replace(/\|\s*prepend:\s*['"]([^'"]*)['"]/g, '|prepend("$1")');
      processedValue = processedValue.replace(/\|\s*split:\s*['"]([^'"]*)['"]/g, '|split("$1")');
      processedValue = processedValue.replace(/\|\s*join:\s*['"]([^'"]*)['"]/g, '|join("$1")');
      processedValue = processedValue.replace(/\|\s*default:\s*([^|]+)/g, '|default($1)');
      
      // 转换数学过滤器
      processedValue = processedValue.replace(/\|\s*plus:\s*([^|]+)/g, '|plus($1)');
      processedValue = processedValue.replace(/\|\s*minus:\s*([^|]+)/g, '|minus($1)');
      processedValue = processedValue.replace(/\|\s*times:\s*([^|]+)/g, '|times($1)');
      processedValue = processedValue.replace(/\|\s*divided_by:\s*([^|]+)/g, '|divided_by($1)');
      processedValue = processedValue.replace(/\|\s*modulo:\s*([^|]+)/g, '|modulo($1)');
      
      return `${whitespaceStart}#var ${varName} = ${processedValue} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% assign title = product.title | upcase %}',
      sline: '{{#var title = product.title|upcase() /}}'
    }
  },
  {
    name: 'Liquid capture 标签转换',
    category: 'assignment',
    description: '转换 Liquid capture 标签到 Sline capture 标签',
    pattern: /{%-?\s*capture\s+(\w+)\s*-?%}/g,
    replacement: (match, varName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#capture ${varName}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% capture my_variable %}',
      sline: '{{#capture my_variable}}'
    }
  },
  {
    name: 'Liquid endcapture 标签转换',
    category: 'assignment',
    description: '转换 Liquid endcapture 标签到 Sline /capture 标签',
    pattern: /{%-?\s*endcapture\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/capture${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endcapture %}',
      sline: '{{/capture}}'
    }
  },
  {
    name: 'Liquid increment 标签转换',
    category: 'assignment',
    description: '转换 Liquid increment 标签到 Sline increment 标签',
    pattern: /{%-?\s*increment\s+(\w+)\s*-?%}/g,
    replacement: (match, varName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#increment ${varName} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% increment counter %}',
      sline: '{{#increment counter /}}'
    }
  },
  {
    name: 'Liquid decrement 标签转换',
    category: 'assignment',
    description: '转换 Liquid decrement 标签到 Sline decrement 标签',
    pattern: /{%-?\s*decrement\s+(\w+)\s*-?%}/g,
    replacement: (match, varName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#decrement ${varName} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% decrement counter %}',
      sline: '{{#decrement counter /}}'
    }
  },
  {
    name: 'Liquid assign 标签转换 - 字符串拼接',
    category: 'assignment',
    description: '转换包含字符串拼接的 Liquid assign 标签',
    pattern: /{%-?\s*assign\s+(\w+)\s*=\s*(['"][^'"]*['"])\s*\|\s*append:\s*([^%}]+?)\s*-?%}/g,
    replacement: (match, varName, baseString, appendValue) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理 append 值
      let processedAppendValue = appendValue.trim();
      if (!processedAppendValue.startsWith('"') && !processedAppendValue.startsWith("'") && 
          !/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedAppendValue)) {
        processedAppendValue = `"${processedAppendValue}"`;
      }
      
      return `${whitespaceStart}#var ${varName} = ${baseString}|append(${processedAppendValue}) /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% assign url = "/products/" | append: product.handle %}',
      sline: '{{#var url = "/products/"|append(product.handle) /}}'
    }
  },
  {
    name: 'Liquid assign 标签转换 - 数组操作',
    category: 'assignment',
    description: '转换包含数组操作的 Liquid assign 标签',
    pattern: /{%-?\s*assign\s+(\w+)\s*=\s*([^%}]+?)\s*\|\s*(map|where|sort|uniq|reverse|first|last)(?::\s*([^%}|]+?))?\s*-?%}/g,
    replacement: (match, varName, collection, operation, parameter) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      let operationStr = operation;
      if (parameter) {
        const processedParam = parameter.trim().replace(/['"]/g, '"');
        operationStr = `${operation}(${processedParam})`;
      } else {
        operationStr = `${operation}()`;
      }
      
      return `${whitespaceStart}#var ${varName} = ${collection.trim()}|${operationStr} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% assign product_titles = collection.products | map: "title" %}',
      sline: '{{#var product_titles = collection.products|map("title") /}}'
    }
  },
  {
    name: 'Liquid assign 标签转换 - 条件赋值',
    category: 'assignment',
    description: '转换包含条件的 Liquid assign 标签',
    pattern: /{%-?\s*assign\s+(\w+)\s*=\s*([^%}]+?)\s*\|\s*default:\s*([^%}]+?)\s*-?%}/g,
    replacement: (match, varName, value, defaultValue) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理默认值
      let processedDefault = defaultValue.trim();
      if (!processedDefault.startsWith('"') && !processedDefault.startsWith("'") && 
          !/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedDefault)) {
        processedDefault = `"${processedDefault}"`;
      }
      
      return `${whitespaceStart}#var ${varName} = ${value.trim()}|default(${processedDefault}) /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% assign title = product.title | default: "No Title" %}',
      sline: '{{#var title = product.title|default("No Title") /}}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取赋值标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidAssignmentTagStats() {
  return {
    totalRules: LIQUID_ASSIGNMENT_TAG_RULES.length,
    rulesByType: {
      assign: LIQUID_ASSIGNMENT_TAG_RULES.filter(rule => rule.name.includes('assign')).length,
      capture: LIQUID_ASSIGNMENT_TAG_RULES.filter(rule => rule.name.includes('capture')).length,
      increment: LIQUID_ASSIGNMENT_TAG_RULES.filter(rule => rule.name.includes('increment')).length,
      decrement: LIQUID_ASSIGNMENT_TAG_RULES.filter(rule => rule.name.includes('decrement')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_ASSIGNMENT_TAG_RULES,
  getLiquidAssignmentTagStats
};
