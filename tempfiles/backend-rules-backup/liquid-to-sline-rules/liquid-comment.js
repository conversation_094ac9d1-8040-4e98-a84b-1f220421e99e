/**
 * Liquid 注释标签转换规则
 * 处理 comment、raw 等注释语句的转换
 */

// ================== Liquid 注释标签转换规则 ==================
const LIQUID_COMMENT_TAG_RULES = [
  {
    name: 'Liquid comment 标签转换',
    category: 'comment',
    description: '转换 Liquid comment 标签到 Sline 注释语法',
    pattern: /{%-?\s*comment\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      return `${whitespaceStart}!--`;
    },
    examples: {
      liquid: '{% comment %}',
      sline: '{{!--'
    }
  },
  {
    name: 'Liquid endcomment 标签转换',
    category: 'comment',
    description: '转换 Liquid endcomment 标签到 Sline 注释结束语法',
    pattern: /{%-?\s*endcomment\s*-?%}/g,
    replacement: (match) => {
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `--${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endcomment %}',
      sline: '--}}'
    }
  },
  {
    name: 'Liquid 完整注释块转换',
    category: 'comment',
    description: '转换完整的 Liquid 注释块到 Sline 注释语法',
    pattern: /{%-?\s*comment\s*-?%}([\s\S]*?){%-?\s*endcomment\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 保持注释内容的原始格式
      return `${startWhitespace}!--${content}--${endWhitespace}`;
    },
    examples: {
      liquid: '{% comment %}This is a comment{% endcomment %}',
      sline: '{{!--This is a comment--}}'
    }
  },
  {
    name: 'Liquid raw 标签转换',
    category: 'comment',
    description: '转换 Liquid raw 标签到 Sline raw 标签',
    pattern: /{%-?\s*raw\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#raw${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% raw %}',
      sline: '{{#raw}}'
    }
  },
  {
    name: 'Liquid endraw 标签转换',
    category: 'comment',
    description: '转换 Liquid endraw 标签到 Sline /raw 标签',
    pattern: /{%-?\s*endraw\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/raw${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endraw %}',
      sline: '{{/raw}}'
    }
  },
  {
    name: 'Liquid 完整 raw 块转换',
    category: 'comment',
    description: '转换完整的 Liquid raw 块到 Sline raw 语法',
    pattern: /{%-?\s*raw\s*-?%}([\s\S]*?){%-?\s*endraw\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 保持 raw 内容的原始格式，不进行任何转换
      return `${startWhitespace}#raw${endWhitespace.replace('~}}', '}}')}${content}${startWhitespace.replace('{{~', '{{')}}/raw${endWhitespace}`;
    },
    examples: {
      liquid: '{% raw %}{{ some_liquid_code }}{% endraw %}',
      sline: '{{#raw}}{{ some_liquid_code }}{{/raw}}'
    }
  },
  {
    name: 'Liquid 单行注释转换',
    category: 'comment',
    description: '转换 Liquid 单行注释到 Sline 注释语法',
    pattern: /{%-?\s*comment\s*-?%}([^{]*?){%-?\s*endcomment\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理单行注释
      const trimmedContent = content.trim();
      return `${startWhitespace}!-- ${trimmedContent} --${endWhitespace}`;
    },
    examples: {
      liquid: '{% comment %} This is a single line comment {% endcomment %}',
      sline: '{{!-- This is a single line comment --}}'
    }
  },
  {
    name: 'Liquid 多行注释转换',
    category: 'comment',
    description: '转换 Liquid 多行注释到 Sline 注释语法',
    pattern: /{%-?\s*comment\s*-?%}\s*\n([\s\S]*?)\n\s*{%-?\s*endcomment\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理多行注释，保持缩进
      const lines = content.split('\n');
      const processedLines = lines.map(line => {
        const trimmed = line.trim();
        return trimmed ? `   ${trimmed}` : '';
      });
      
      const processedContent = processedLines.join('\n');
      
      return `${startWhitespace}!--\n${processedContent}\n--${endWhitespace}`;
    },
    examples: {
      liquid: '{% comment %}\n  This is a\n  multi-line comment\n{% endcomment %}',
      sline: '{{!--\n   This is a\n   multi-line comment\n--}}'
    }
  },
  {
    name: 'Liquid 注释中的 assign 转换',
    category: 'comment',
    description: '转换注释中包含的 assign 语句（特殊情况）',
    pattern: /{%-?\s*comment\s*-?%}([\s\S]*?{%\s*assign\s+[^%]+%}[\s\S]*?){%-?\s*endcomment\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 在注释中查找并转换 assign 语句
      let processedContent = content;
      
      // 转换注释中的 assign 语句
      processedContent = processedContent.replace(
        /{%\s*assign\s+(\w+)\s*=\s*([^%]+)%}/g,
        '{{#var $1 = $2 /}}'
      );
      
      return `${startWhitespace}!--${processedContent}--${endWhitespace}`;
    },
    examples: {
      liquid: '{% comment %}{% assign test = "value" %}{% endcomment %}',
      sline: '{{!--{{#var test = "value" /}}--}}'
    }
  },
  {
    name: 'Liquid 条件注释转换',
    category: 'comment',
    description: '转换包含条件逻辑的 Liquid 注释',
    pattern: /{%-?\s*comment\s*-?%}([\s\S]*?{%\s*(?:if|unless|case)\s+[^%]+%}[\s\S]*?){%-?\s*endcomment\s*-?%}/g,
    replacement: (match, content) => {
      const startWhitespace = match.startsWith('{%-') ? '{{~' : '{{';
      const endWhitespace = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 保持条件逻辑在注释中的原始格式
      // 这种情况通常是临时注释掉的代码
      return `${startWhitespace}!-- COMMENTED CODE:\n${content}\n--${endWhitespace}`;
    },
    examples: {
      liquid: '{% comment %}{% if product.available %}Available{% endif %}{% endcomment %}',
      sline: '{{!-- COMMENTED CODE:\n{% if product.available %}Available{% endif %}\n--}}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取注释标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidCommentTagStats() {
  return {
    totalRules: LIQUID_COMMENT_TAG_RULES.length,
    rulesByType: {
      comment: LIQUID_COMMENT_TAG_RULES.filter(rule => rule.name.includes('comment')).length,
      raw: LIQUID_COMMENT_TAG_RULES.filter(rule => rule.name.includes('raw')).length,
      singleLine: LIQUID_COMMENT_TAG_RULES.filter(rule => rule.name.includes('单行')).length,
      multiLine: LIQUID_COMMENT_TAG_RULES.filter(rule => rule.name.includes('多行')).length,
      special: LIQUID_COMMENT_TAG_RULES.filter(rule => rule.name.includes('assign') || rule.name.includes('条件')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_COMMENT_TAG_RULES,
  getLiquidCommentTagStats
};
