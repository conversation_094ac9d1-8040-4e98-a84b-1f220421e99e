/**
 * Liquid 条件标签转换规则
 * 处理 if、unless、case、when 等条件语句的转换
 */

const { convertLiquidCondition, negateLiquidCondition, isLiquidSingleVariable } = require('../mappings/variables');

// ================== Liquid 条件标签转换规则 ==================
const LIQUID_CONDITIONAL_TAG_RULES = [
  {
    name: 'Liquid if 标签转换',
    category: 'conditional',
    description: '转换 Liquid if 标签到 Sline if 标签',
    pattern: /{%-?\s*if\s+(.*?)\s*-?%}/g,
    replacement: (match, condition) => {
      const convertedCondition = convertLiquidCondition(condition);
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#if ${convertedCondition}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% if product.available %}',
      sline: '{{#if product.available}}'
    }
  },
  {
    name: 'Liquid elsif 标签转换',
    category: 'conditional',
    description: '转换 Liquid elsif 标签到 Sline elsif 标签',
    pattern: /{%-?\s*elsif\s+(.*?)\s*-?%}/g,
    replacement: (match, condition) => {
      const convertedCondition = convertLiquidCondition(condition);
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#elsif ${convertedCondition}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% elsif product.price > 100 %}',
      sline: '{{#elsif product.price > 100}}'
    }
  },
  {
    name: 'Liquid else 标签转换',
    category: 'conditional',
    description: '转换 Liquid else 标签到 Sline else 标签',
    pattern: /{%-?\s*else\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#else /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% else %}',
      sline: '{{#else /}}'
    }
  },
  {
    name: 'Liquid endif 标签转换',
    category: 'conditional',
    description: '转换 Liquid endif 标签到 Sline /if 标签',
    pattern: /{%-?\s*endif\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/if${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endif %}',
      sline: '{{/if}}'
    }
  },
  {
    name: 'Liquid unless 标签转换',
    category: 'conditional',
    description: '转换 Liquid unless 标签到 Sline if 标签（否定条件）',
    pattern: /{%-?\s*unless\s+(.*?)\s*-?%}/g,
    replacement: (match, condition) => {
      const negatedCondition = negateLiquidCondition(condition);
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#if ${negatedCondition}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% unless product.available %}',
      sline: '{{#if !product.available}}'
    }
  },
  {
    name: 'Liquid endunless 标签转换',
    category: 'conditional',
    description: '转换 Liquid endunless 标签到 Sline /if 标签',
    pattern: /{%-?\s*endunless\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/if${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endunless %}',
      sline: '{{/if}}'
    }
  },
  {
    name: 'Liquid case 标签转换',
    category: 'conditional',
    description: '转换 Liquid case 标签到 Sline switch 标签',
    pattern: /{%-?\s*case\s+(.*?)\s*-?%}/g,
    replacement: (match, variable) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#switch ${variable}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% case product.type %}',
      sline: '{{#switch product.type}}'
    }
  },
  {
    name: 'Liquid when 标签转换 - 字符串值',
    category: 'conditional',
    description: '转换 Liquid when 标签到 Sline case 自闭合标签（字符串值）',
    pattern: /{%-?\s*when\s+['"]([^'"]+)['"]\s*-?%}/g,
    replacement: (match, value) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#case "${value}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% when "shirt" %}',
      sline: '{{#case "shirt" /}}'
    }
  },
  {
    name: 'Liquid when 标签转换 - 变量值',
    category: 'conditional',
    description: '转换 Liquid when 标签到 Sline case 自闭合标签（变量值）',
    pattern: /{%-?\s*when\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s*-?%}/g,
    replacement: (match, value) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#case ${value} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% when shirt %}',
      sline: '{{#case shirt /}}'
    }
  },
  {
    name: 'Liquid when 标签转换 - 多个值',
    category: 'conditional',
    description: '转换 Liquid when 标签到 Sline case 自闭合标签（多个值）',
    pattern: /{%-?\s*when\s+(.*?)\s*-?%}/g,
    replacement: (match, values) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理多个值的情况，用逗号分隔
      const processedValues = values.split(',').map(v => {
        const trimmed = v.trim();
        // 如果是字符串字面量，保持引号
        if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
          return trimmed;
        }
        if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
          return `"${trimmed.slice(1, -1)}"`;
        }
        // 如果是变量，不加引号
        return trimmed;
      }).join(', ');
      
      return `${whitespaceStart}#case ${processedValues} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% when "shirt", "pants" %}',
      sline: '{{#case "shirt", "pants" /}}'
    }
  },
  {
    name: 'Liquid endcase 标签转换',
    category: 'conditional',
    description: '转换 Liquid endcase 标签到 Sline /switch 标签',
    pattern: /{%-?\s*endcase\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/switch${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endcase %}',
      sline: '{{/switch}}'
    }
  },
  {
    name: 'Liquid 复杂条件表达式转换',
    category: 'conditional',
    description: '转换包含 contains、size 等复杂条件的表达式',
    pattern: /{%-?\s*if\s+(.*?(?:contains|size|and|or).*?)\s*-?%}/g,
    replacement: (match, condition) => {
      let convertedCondition = condition;
      
      // 转换 contains 操作
      convertedCondition = convertedCondition.replace(
        /(\w+(?:\.\w+)*)\s+contains\s+(['"]?)([^'"]+)\2/g,
        '$1|contains("$3")'
      );
      
      // 转换 size 属性
      convertedCondition = convertedCondition.replace(
        /(\w+(?:\.\w+)*)\.size/g,
        '$1|size()'
      );
      
      // 转换逻辑运算符
      convertedCondition = convertedCondition.replace(/\s+and\s+/g, ' && ');
      convertedCondition = convertedCondition.replace(/\s+or\s+/g, ' || ');
      
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#if ${convertedCondition}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% if product.tags contains "sale" and product.available %}',
      sline: '{{#if product.tags|contains("sale") && product.available}}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取条件标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidConditionalTagStats() {
  return {
    totalRules: LIQUID_CONDITIONAL_TAG_RULES.length,
    rulesByType: {
      if: LIQUID_CONDITIONAL_TAG_RULES.filter(rule => rule.name.includes('if')).length,
      unless: LIQUID_CONDITIONAL_TAG_RULES.filter(rule => rule.name.includes('unless')).length,
      case: LIQUID_CONDITIONAL_TAG_RULES.filter(rule => rule.name.includes('case') || rule.name.includes('when')).length,
      complex: LIQUID_CONDITIONAL_TAG_RULES.filter(rule => rule.name.includes('复杂')).length
    }
  };
}

/**
 * 根据名称获取条件规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getLiquidConditionalRuleByName(ruleName) {
  return LIQUID_CONDITIONAL_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

// ================== 导出 ==================
module.exports = {
  LIQUID_CONDITIONAL_TAG_RULES,
  getLiquidConditionalTagStats,
  getLiquidConditionalRuleByName
};
