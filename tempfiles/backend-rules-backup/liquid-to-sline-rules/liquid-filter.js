/**
 * Liquid 过滤器标签转换规则
 * 处理复杂过滤器和过滤器组合的转换
 */

// ================== Liquid 过滤器标签转换规则 ==================
const LIQUID_FILTER_TAG_RULES = [
  {
    name: 'Liquid 图片过滤器转换',
    category: 'filter',
    description: '转换 Liquid 图片相关过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*img_url:\s*['"]([^'"]*)['"]\s*-?\}\}/g,
    replacement: (match, variable, size) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|img_url("${size}")${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.featured_image | img_url: "300x300" }}',
      sline: '{{ product.featured_image|img_url("300x300") }}'
    }
  },
  {
    name: 'Liquid 货币过滤器转换',
    category: 'filter',
    description: '转换 Liquid 货币相关过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*(money|money_with_currency|money_without_currency|money_without_trailing_zeros)\s*-?\}\}/g,
    replacement: (match, variable, filterName) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|${filterName}()${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.price | money }}',
      sline: '{{ product.price|money() }}'
    }
  },
  {
    name: 'Liquid 日期过滤器转换',
    category: 'filter',
    description: '转换 Liquid 日期相关过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*date:\s*['"]([^'"]*)['"]\s*-?\}\}/g,
    replacement: (match, variable, format) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|date("${format}")${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ article.created_at | date: "%B %d, %Y" }}',
      sline: '{{ article.created_at|date("%B %d, %Y") }}'
    }
  },
  {
    name: 'Liquid 数组过滤器转换 - map',
    category: 'filter',
    description: '转换 Liquid map 过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*map:\s*['"]([^'"]*)['"]\s*-?\}\}/g,
    replacement: (match, variable, property) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|map("${property}")${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ collection.products | map: "title" }}',
      sline: '{{ collection.products|map("title") }}'
    }
  },
  {
    name: 'Liquid 数组过滤器转换 - where',
    category: 'filter',
    description: '转换 Liquid where 过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*where:\s*['"]([^'"]*)['"]\s*,\s*(['"]?[^'"]*['"]?)\s*-?\}\}/g,
    replacement: (match, variable, property, value) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      // 处理值的引号
      let processedValue = value.trim();
      if (!processedValue.startsWith('"') && !processedValue.startsWith("'")) {
        if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedValue) && !/^\d+(\.\d+)?$/.test(processedValue)) {
          processedValue = `"${processedValue}"`;
        }
      } else {
        processedValue = processedValue.replace(/'/g, '"');
      }
      
      return `${whitespaceStart}${variable.trim()}|where("${property}", ${processedValue})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ collection.products | where: "available", true }}',
      sline: '{{ collection.products|where("available", true) }}'
    }
  },
  {
    name: 'Liquid 数组过滤器转换 - sort_by',
    category: 'filter',
    description: '转换 Liquid sort_by 过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*sort_by:\s*['"]([^'"]*)['"]\s*-?\}\}/g,
    replacement: (match, variable, property) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|sort_by("${property}")${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ collection.products | sort_by: "price" }}',
      sline: '{{ collection.products|sort_by("price") }}'
    }
  },

  {
    name: 'Liquid 条件过滤器转换',
    category: 'filter',
    description: '转换 Liquid 条件相关过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*default:\s*(['"]?)([^|'"]+)\2\s*-?\}\}/g,
    replacement: (match, variable, quote, defaultValue) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      // 处理默认值
      let processedDefault = defaultValue;
      if (!quote && !/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedDefault) && !/^\d+(\.\d+)?$/.test(processedDefault)) {
        processedDefault = `"${processedDefault}"`;
      } else if (quote) {
        processedDefault = `"${processedDefault}"`;
      }
      
      return `${whitespaceStart}${variable.trim()}|default(${processedDefault})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.title | default: "No Title" }}',
      sline: '{{ product.title|default("No Title") }}'
    }
  },
  {
    name: 'Liquid URL 过滤器转换',
    category: 'filter',
    description: '转换 Liquid URL 相关过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*(url_encode|url_decode|asset_url|file_url)\s*-?\}\}/g,
    replacement: (match, variable, filterName) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      return `${whitespaceStart}${variable.trim()}|${filterName}()${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ "style.css" | asset_url }}',
      sline: '{{ "style.css"|asset_url() }}'
    }
  },
  {
    name: 'Liquid 高级过滤器转换',
    category: 'filter',
    description: '转换 Liquid 高级过滤器到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*(highlight|link_to|script_tag|stylesheet_tag)(?::\s*(['"]?)([^|'"]+)\3)?\s*-?\}\}/g,
    replacement: (match, variable, filterName, quote, param) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      if (param) {
        // 处理参数
        let processedParam = param;
        if (!quote && !/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedParam) && !/^\d+(\.\d+)?$/.test(processedParam)) {
          processedParam = `"${processedParam}"`;
        } else if (quote) {
          processedParam = `"${processedParam}"`;
        }
        
        return `${whitespaceStart}${variable.trim()}|${filterName}(${processedParam})${whitespaceEnd}`;
      } else {
        return `${whitespaceStart}${variable.trim()}|${filterName}()${whitespaceEnd}`;
      }
    },
    examples: {
      liquid: '{{ search.terms | highlight: search.terms }}',
      sline: '{{ search.terms|highlight(search.terms) }}'
    }
  },
  {
    name: 'Liquid 数学过滤器组合转换',
    category: 'filter',
    description: '转换 Liquid 数学过滤器组合到 Sline 过滤器',
    pattern: /\{\{-?\s*([^}|]+?)\s*\|\s*(plus|minus|times|divided_by|modulo):\s*([^|]+?)\s*\|\s*(plus|minus|times|divided_by|modulo):\s*([^}]+?)\s*-?\}\}/g,
    replacement: (match, variable, op1, val1, op2, val2) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      // 处理两个数学操作
      let processedVal1 = val1.trim();
      let processedVal2 = val2.trim();
      
      // 确保数值参数格式正确
      if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedVal1) && !/^\d+(\.\d+)?$/.test(processedVal1)) {
        processedVal1 = `"${processedVal1}"`;
      }
      if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedVal2) && !/^\d+(\.\d+)?$/.test(processedVal2)) {
        processedVal2 = `"${processedVal2}"`;
      }
      
      return `${whitespaceStart}${variable.trim()}|${op1}(${processedVal1})|${op2}(${processedVal2})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.price | plus: 10 | times: 1.1 }}',
      sline: '{{ product.price|plus(10)|times(1.1) }}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取过滤器标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidFilterTagStats() {
  return {
    totalRules: LIQUID_FILTER_TAG_RULES.length,
    rulesByType: {
      image: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('图片')).length,
      money: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('货币')).length,
      date: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('日期')).length,
      array: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('数组')).length,
      chain: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('链式')).length,
      conditional: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('条件')).length,
      url: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('URL')).length,
      advanced: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('高级')).length,
      math: LIQUID_FILTER_TAG_RULES.filter(rule => rule.name.includes('数学')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_FILTER_TAG_RULES,
  getLiquidFilterTagStats
};
