/**
 * Liquid 过滤器映射表
 * 定义 Liquid 过滤器到 Sline 过滤器的映射关系
 */

// ================== Liquid 过滤器映射表 ==================
const LIQUID_FILTER_MAPPINGS = {
  // 字符串过滤器
  'capitalize': 'capitalize',
  'downcase': 'downcase',
  'upcase': 'upcase',
  'escape': 'escape',
  'escape_once': 'escape_once',
  'newline_to_br': 'newline_to_br',
  'strip_html': 'strip_html',
  'strip_newlines': 'strip_newlines',
  'truncate': 'truncate',
  'truncatewords': 'truncatewords',
  'lstrip': 'trim_left',
  'rstrip': 'trim_right',
  'strip': 'trim',
  'replace': 'replace',
  'replace_first': 'replace_first',
  'remove': 'remove',
  'remove_first': 'remove_first',
  'append': 'append',
  'prepend': 'prepend',
  'split': 'split',
  'slice': 'slice',
  
  // 数字过滤器
  'abs': 'abs',
  'ceil': 'ceil',
  'floor': 'floor',
  'round': 'round',
  'plus': 'plus',
  'minus': 'minus',
  'times': 'times',
  'divided_by': 'divided_by',
  'modulo': 'modulo',
  'at_most': 'at_most',
  'at_least': 'at_least',
  
  // 数组过滤器
  'join': 'join',
  'first': 'first',
  'last': 'last',
  'concat': 'concat',
  'index': 'index',
  'map': 'map',
  'reverse': 'reverse',
  'size': 'size',
  'sort': 'sort',
  'sort_natural': 'sort_natural',
  'uniq': 'uniq',
  'where': 'where',
  'group_by': 'group_by',
  'compact': 'compact',
  'flatten': 'flatten',
  
  // 日期过滤器
  'date': 'date',
  'strftime': 'strftime',
  
  // URL 过滤器
  'url_encode': 'url_encode',
  'url_decode': 'url_decode',
  'url_escape': 'url_escape',
  'url_param_escape': 'url_param_escape',
  
  // 货币过滤器
  'money': 'money',
  'money_with_currency': 'money_with_currency',
  'money_without_currency': 'money_without_currency',
  'money_without_trailing_zeros': 'money_without_trailing_zeros',
  
  // 图片过滤器
  'img_url': 'img_url',
  'img_tag': 'img_tag',
  'asset_url': 'asset_url',
  'asset_img_url': 'asset_img_url',
  'file_url': 'file_url',
  'file_img_url': 'file_img_url',
  'product_img_url': 'product_img_url',
  'collection_img_url': 'collection_img_url',
  
  // 颜色过滤器
  'color_to_rgb': 'color_to_rgb',
  'color_to_hsl': 'color_to_hsl',
  'color_to_hex': 'color_to_hex',
  'color_extract': 'color_extract',
  'color_brightness': 'color_brightness',
  'color_modify': 'color_modify',
  'color_lighten': 'color_lighten',
  'color_darken': 'color_darken',
  'color_saturate': 'color_saturate',
  'color_desaturate': 'color_desaturate',
  'color_mix': 'color_mix',
  'color_difference': 'color_difference',
  
  // 字体过滤器
  'font_face': 'font_face',
  'font_url': 'font_url',
  'font_modify': 'font_modify',
  
  // 重量过滤器
  'weight_with_unit': 'weight_with_unit',
  
  // 格式化过滤器
  'default': 'default',
  'json': 'json',
  'base64_encode': 'base64_encode',
  'base64_decode': 'base64_decode',
  'md5': 'md5',
  'sha1': 'sha1',
  'sha256': 'sha256',
  'hmac_sha1': 'hmac_sha1',
  'hmac_sha256': 'hmac_sha256',
  
  // 高级过滤器
  'highlight': 'highlight',
  'highlight_active_tag': 'highlight_active_tag',
  'link_to': 'link_to',
  'link_to_vendor': 'link_to_vendor',
  'link_to_type': 'link_to_type',
  'link_to_tag': 'link_to_tag',
  'link_to_add_tag': 'link_to_add_tag',
  'link_to_remove_tag': 'link_to_remove_tag',
  'payment_type_img_url': 'payment_type_img_url',
  'shopify_asset_url': 'shopify_asset_url',
  'global_asset_url': 'global_asset_url',
  'script_tag': 'script_tag',
  'stylesheet_tag': 'stylesheet_tag',
  'placeholder_svg_tag': 'placeholder_svg_tag',
  'customer_login_link': 'customer_login_link',
  'customer_logout_link': 'customer_logout_link',
  'customer_register_link': 'customer_register_link',
  
  // 表单过滤器
  'form': 'form',
  'endform': 'endform',
  
  // 分页过滤器
  'paginate': 'paginate',
  'endpaginate': 'endpaginate',
  
  // 循环过滤器
  'cycle': 'cycle',
  'tablerow': 'tablerow',
  'endtablerow': 'endtablerow',
  
  // 条件过滤器
  'if': 'if',
  'unless': 'unless',
  'elsif': 'elsif',
  'else': 'else',
  'endif': 'endif',
  'endunless': 'endunless',
  'case': 'case',
  'when': 'when',
  'endcase': 'endcase',
  
  // 循环过滤器
  'for': 'for',
  'endfor': 'endfor',
  'break': 'break',
  'continue': 'continue',
  
  // 变量过滤器
  'assign': 'assign',
  'capture': 'capture',
  'endcapture': 'endcapture',
  'increment': 'increment',
  'decrement': 'decrement',
  
  // 包含过滤器
  'include': 'include',
  'render': 'render',
  'section': 'section',
  
  // 注释过滤器
  'comment': 'comment',
  'endcomment': 'endcomment',
  'raw': 'raw',
  'endraw': 'endraw',
  
  // 布局过滤器
  'layout': 'layout',
  'content_for': 'content_for',
  'yield': 'yield',
  
  // 国际化过滤器
  't': 't',
  'translate': 'translate',
  
  // 自定义过滤器
  'pluralize': 'pluralize',
  'within': 'within',
  'contains': 'contains'
};

// ================== 过滤器分类 ==================
const LIQUID_FILTER_CATEGORIES = [
  {
    name: 'string',
    description: '字符串处理过滤器',
    filters: ['capitalize', 'downcase', 'upcase', 'escape', 'escape_once', 'newline_to_br', 'strip_html', 'strip_newlines', 'truncate', 'truncatewords', 'lstrip', 'rstrip', 'strip', 'replace', 'replace_first', 'remove', 'remove_first', 'append', 'prepend', 'split', 'slice']
  },
  {
    name: 'number',
    description: '数字处理过滤器',
    filters: ['abs', 'ceil', 'floor', 'round', 'plus', 'minus', 'times', 'divided_by', 'modulo', 'at_most', 'at_least']
  },
  {
    name: 'array',
    description: '数组处理过滤器',
    filters: ['join', 'first', 'last', 'concat', 'index', 'map', 'reverse', 'size', 'sort', 'sort_natural', 'uniq', 'where', 'group_by', 'compact', 'flatten']
  },
  {
    name: 'date',
    description: '日期处理过滤器',
    filters: ['date', 'strftime']
  },
  {
    name: 'url',
    description: 'URL 处理过滤器',
    filters: ['url_encode', 'url_decode', 'url_escape', 'url_param_escape']
  },
  {
    name: 'money',
    description: '货币处理过滤器',
    filters: ['money', 'money_with_currency', 'money_without_currency', 'money_without_trailing_zeros']
  },
  {
    name: 'image',
    description: '图片处理过滤器',
    filters: ['img_url', 'img_tag', 'asset_url', 'asset_img_url', 'file_url', 'file_img_url', 'product_img_url', 'collection_img_url']
  },
  {
    name: 'color',
    description: '颜色处理过滤器',
    filters: ['color_to_rgb', 'color_to_hsl', 'color_to_hex', 'color_extract', 'color_brightness', 'color_modify', 'color_lighten', 'color_darken', 'color_saturate', 'color_desaturate', 'color_mix', 'color_difference']
  },
  {
    name: 'format',
    description: '格式化过滤器',
    filters: ['default', 'json', 'base64_encode', 'base64_decode', 'md5', 'sha1', 'sha256', 'hmac_sha1', 'hmac_sha256']
  },
  {
    name: 'advanced',
    description: '高级过滤器',
    filters: ['highlight', 'highlight_active_tag', 'link_to', 'link_to_vendor', 'link_to_type', 'link_to_tag', 'link_to_add_tag', 'link_to_remove_tag', 'payment_type_img_url', 'shopify_asset_url', 'global_asset_url', 'script_tag', 'stylesheet_tag', 'placeholder_svg_tag', 'customer_login_link', 'customer_logout_link', 'customer_register_link']
  }
];

// ================== 辅助函数 ==================

/**
 * 根据名称获取过滤器映射
 * @param {string} filterName - 过滤器名称
 * @returns {string|null} 映射后的过滤器名称或 null
 */
function getLiquidFilterByName(filterName) {
  return LIQUID_FILTER_MAPPINGS[filterName] || null;
}

/**
 * 根据分类获取过滤器
 * @param {string} category - 分类名称
 * @returns {Array} 过滤器数组
 */
function getLiquidFiltersByCategory(category) {
  const categoryObj = LIQUID_FILTER_CATEGORIES.find(cat => cat.name === category);
  return categoryObj ? categoryObj.filters : [];
}

/**
 * 获取过滤器统计信息
 * @returns {Object} 统计信息
 */
function getLiquidFilterStats() {
  return {
    totalFilters: Object.keys(LIQUID_FILTER_MAPPINGS).length,
    categories: LIQUID_FILTER_CATEGORIES.length,
    filtersByCategory: LIQUID_FILTER_CATEGORIES.reduce((acc, category) => {
      acc[category.name] = category.filters.length;
      return acc;
    }, {})
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_FILTER_MAPPINGS,
  LIQUID_FILTER_CATEGORIES,
  getLiquidFilterByName,
  getLiquidFiltersByCategory,
  getLiquidFilterStats
};
