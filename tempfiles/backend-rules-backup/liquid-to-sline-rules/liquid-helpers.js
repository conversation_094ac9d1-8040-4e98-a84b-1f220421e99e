/**
 * Liquid 转换辅助工具函数
 * 提供各种辅助功能来支持 Liquid 到 Sline 的转换
 */

// ================== 空白字符处理 ==================

/**
 * 处理 Liquid 空白字符控制
 * @param {string} match - 匹配的原始标签
 * @param {string} replacement - 替换内容
 * @returns {string} 处理后的标签
 */
function handleLiquidWhitespace(match, replacement) {
  const hasStartTrim = match.startsWith('{%-') || match.startsWith('{{-');
  const hasEndTrim = match.endsWith('-%}') || match.endsWith('-}}');
  
  let prefix = '{{';
  let suffix = '}}';
  
  if (hasStartTrim) {
    prefix = '{{~';
  }
  
  if (hasEndTrim) {
    suffix = '~}}';
  }
  
  return `${prefix}${replacement}${suffix}`;
}

// ================== 行号处理 ==================

/**
 * 获取指定位置在文本中的行号
 * @param {string} text - 文本内容
 * @param {number} position - 字符位置
 * @returns {number} 行号（从1开始）
 */
function getLiquidLineNumber(text, position) {
  if (position < 0 || position >= text.length) {
    return 1;
  }
  
  const beforePosition = text.substring(0, position);
  const lines = beforePosition.split('\n');
  return lines.length;
}

/**
 * 获取指定行的内容
 * @param {string} text - 文本内容
 * @param {number} lineNumber - 行号（从1开始）
 * @returns {string} 行内容
 */
function getLiquidLineContent(text, lineNumber) {
  const lines = text.split('\n');
  if (lineNumber < 1 || lineNumber > lines.length) {
    return '';
  }
  return lines[lineNumber - 1];
}

// ================== 标签参数解析 ==================

/**
 * 解析 Liquid 标签参数
 * @param {string} paramString - 参数字符串
 * @returns {Object} 解析后的参数对象
 */
function parseLiquidTagParameters(paramString) {
  const params = {};
  const paramPattern = /(\w+):\s*([^,]+)/g;
  let match;
  
  while ((match = paramPattern.exec(paramString)) !== null) {
    const [, key, value] = match;
    
    // 处理不同类型的值
    let processedValue = value.trim();
    
    // 移除引号
    if ((processedValue.startsWith('"') && processedValue.endsWith('"')) ||
        (processedValue.startsWith("'") && processedValue.endsWith("'"))) {
      processedValue = processedValue.slice(1, -1);
    }
    
    // 尝试转换为数字
    if (/^\d+$/.test(processedValue)) {
      processedValue = parseInt(processedValue, 10);
    } else if (/^\d+\.\d+$/.test(processedValue)) {
      processedValue = parseFloat(processedValue);
    } else if (processedValue === 'true') {
      processedValue = true;
    } else if (processedValue === 'false') {
      processedValue = false;
    }
    
    params[key] = processedValue;
  }
  
  return params;
}

/**
 * 将参数对象转换为 Sline 格式的参数字符串
 * @param {Object} params - 参数对象
 * @returns {string} Sline 格式的参数字符串
 */
function formatSlineParameters(params) {
  return Object.entries(params).map(([key, value]) => {
    if (typeof value === 'string') {
      return `${key}="${value}"`;
    } else {
      return `${key}=${value}`;
    }
  }).join(' ');
}

// ================== 变量名验证 ==================

/**
 * 验证 Liquid 变量名是否有效
 * @param {string} variableName - 变量名
 * @returns {boolean} 是否有效
 */
function isValidLiquidVariableName(variableName) {
  // Liquid 变量名规则：字母或下划线开头，后跟字母、数字、下划线或点
  return /^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(variableName);
}

/**
 * 清理变量名，使其符合 Sline 规范
 * @param {string} variableName - 原始变量名
 * @returns {string} 清理后的变量名
 */
function cleanLiquidVariableName(variableName) {
  // 移除非法字符，保留字母、数字、下划线和点
  return variableName.replace(/[^a-zA-Z0-9_.]/g, '');
}

// ================== 字符串处理 ==================

/**
 * 提取字符串中的引号内容
 * @param {string} text - 文本
 * @returns {Array} 引号内容数组
 */
function extractLiquidQuotedStrings(text) {
  const strings = [];
  
  // 匹配双引号字符串
  const doubleQuotePattern = /"([^"\\]*(\\.[^"\\]*)*)"/g;
  let match;
  
  while ((match = doubleQuotePattern.exec(text)) !== null) {
    strings.push({
      content: match[1],
      quote: 'double',
      start: match.index,
      end: match.index + match[0].length
    });
  }
  
  // 匹配单引号字符串
  const singleQuotePattern = /'([^'\\]*(\\.[^'\\]*)*)'/g;
  
  while ((match = singleQuotePattern.exec(text)) !== null) {
    strings.push({
      content: match[1],
      quote: 'single',
      start: match.index,
      end: match.index + match[0].length
    });
  }
  
  return strings.sort((a, b) => a.start - b.start);
}

/**
 * 标准化引号使用（统一为双引号）
 * @param {string} text - 文本
 * @returns {string} 标准化后的文本
 */
function normalizeLiquidQuotes(text) {
  return text.replace(/'([^']*)'/g, '"$1"');
}

// ================== 标签内容清理 ==================

/**
 * 清理 Liquid 标签内容
 * @param {string} content - 标签内容
 * @returns {string} 清理后的内容
 */
function cleanLiquidTagContent(content) {
  // 移除首尾空白
  let cleaned = content.trim();
  
  // 标准化空白字符
  cleaned = cleaned.replace(/\s+/g, ' ');
  
  // 标准化运算符周围的空格
  cleaned = cleaned.replace(/\s*(==|!=|>=|<=|>|<|&&|\|\|)\s*/g, ' $1 ');
  
  // 标准化逗号后的空格
  cleaned = cleaned.replace(/,\s*/g, ', ');
  
  return cleaned;
}

// ================== 表达式复杂度分析 ==================

/**
 * 分析 Liquid 表达式的复杂度
 * @param {string} expression - 表达式
 * @returns {Object} 复杂度分析结果
 */
function analyzeLiquidExpressionComplexity(expression) {
  const analysis = {
    score: 0,
    factors: [],
    isSimple: true,
    isComplex: false
  };
  
  // 检查变量访问深度
  const dotCount = (expression.match(/\./g) || []).length;
  if (dotCount > 0) {
    analysis.score += dotCount;
    analysis.factors.push(`属性访问深度: ${dotCount}`);
  }
  
  // 检查数组访问
  const arrayAccess = (expression.match(/\[/g) || []).length;
  if (arrayAccess > 0) {
    analysis.score += arrayAccess * 2;
    analysis.factors.push(`数组访问: ${arrayAccess}`);
  }
  
  // 检查过滤器数量
  const filterCount = (expression.match(/\|/g) || []).length;
  if (filterCount > 0) {
    analysis.score += filterCount;
    analysis.factors.push(`过滤器数量: ${filterCount}`);
  }
  
  // 检查逻辑运算符
  const logicalOps = (expression.match(/(&&|\|\||and|or)/g) || []).length;
  if (logicalOps > 0) {
    analysis.score += logicalOps * 2;
    analysis.factors.push(`逻辑运算符: ${logicalOps}`);
  }
  
  // 检查比较运算符
  const comparisonOps = (expression.match(/(==|!=|>=|<=|>|<|contains)/g) || []).length;
  if (comparisonOps > 0) {
    analysis.score += comparisonOps;
    analysis.factors.push(`比较运算符: ${comparisonOps}`);
  }
  
  // 检查函数调用
  const functionCalls = (expression.match(/\w+\(/g) || []).length;
  if (functionCalls > 0) {
    analysis.score += functionCalls * 2;
    analysis.factors.push(`函数调用: ${functionCalls}`);
  }
  
  // 判断复杂度级别
  if (analysis.score === 0) {
    analysis.level = 'trivial';
  } else if (analysis.score <= 3) {
    analysis.level = 'simple';
  } else if (analysis.score <= 8) {
    analysis.level = 'moderate';
    analysis.isSimple = false;
  } else {
    analysis.level = 'complex';
    analysis.isSimple = false;
    analysis.isComplex = true;
  }
  
  return analysis;
}

// ================== 路径处理 ==================

/**
 * 标准化文件路径
 * @param {string} path - 文件路径
 * @returns {string} 标准化后的路径
 */
function normalizeLiquidPath(path) {
  // 移除 .liquid 扩展名
  let normalized = path.replace(/\.liquid$/, '');
  
  // 标准化路径分隔符
  normalized = normalized.replace(/\\/g, '/');
  
  // 移除开头的斜杠
  normalized = normalized.replace(/^\/+/, '');
  
  return normalized;
}

/**
 * 检查文件扩展名是否有效
 * @param {string} filename - 文件名
 * @returns {boolean} 是否有效
 */
function hasValidLiquidExtension(filename) {
  const validExtensions = ['.liquid', '.html', '.htm', '.xml', '.json', '.js', '.css'];
  return validExtensions.some(ext => filename.toLowerCase().endsWith(ext));
}

// ================== 调试工具 ==================

/**
 * 创建调试信息
 * @param {string} context - 上下文
 * @param {string} message - 消息
 * @param {Object} data - 附加数据
 * @returns {Object} 调试信息对象
 */
function createLiquidDebugInfo(context, message, data = {}) {
  return {
    timestamp: new Date().toISOString(),
    context: context,
    message: message,
    data: data,
    level: data.level || 'info'
  };
}

/**
 * 格式化调试输出
 * @param {Object} debugInfo - 调试信息
 * @returns {string} 格式化的调试字符串
 */
function formatLiquidDebugOutput(debugInfo) {
  const { timestamp, context, message, data, level } = debugInfo;
  const prefix = `[${level.toUpperCase()}] ${timestamp} [${context}]`;
  
  if (Object.keys(data).length === 0) {
    return `${prefix} ${message}`;
  } else {
    return `${prefix} ${message}\n${JSON.stringify(data, null, 2)}`;
  }
}

// ================== 导出 ==================
module.exports = {
  // 空白字符处理
  handleLiquidWhitespace,
  
  // 行号处理
  getLiquidLineNumber,
  getLiquidLineContent,
  
  // 参数解析
  parseLiquidTagParameters,
  formatSlineParameters,
  
  // 变量名处理
  isValidLiquidVariableName,
  cleanLiquidVariableName,
  
  // 字符串处理
  extractLiquidQuotedStrings,
  normalizeLiquidQuotes,
  
  // 标签内容处理
  cleanLiquidTagContent,
  
  // 表达式分析
  analyzeLiquidExpressionComplexity,
  
  // 路径处理
  normalizeLiquidPath,
  hasValidLiquidExtension,
  
  // 调试工具
  createLiquidDebugInfo,
  formatLiquidDebugOutput
};
