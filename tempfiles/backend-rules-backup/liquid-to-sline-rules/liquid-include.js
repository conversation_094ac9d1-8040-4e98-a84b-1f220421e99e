/**
 * Liquid 包含标签转换规则
 * 处理 include、render、section 等包含语句的转换
 */

// ================== Liquid 包含标签转换规则 ==================
const LIQUID_INCLUDE_TAG_RULES = [
  {
    name: 'Liquid include 标签转换',
    category: 'include',
    description: '转换 Liquid include 标签到 Sline include 标签',
    pattern: /{%-?\s*include\s+['"]([^'"]+?)(?:\.liquid)?['"]\s*-?%}/g,
    replacement: (match, templateName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      return `${whitespaceStart}#include "${cleanTemplateName}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% include "product-card.liquid" %}',
      sline: '{{#include "product-card" /}}'
    }
  },
  {
    name: 'Liquid include 标签转换 - 带参数',
    category: 'include',
    description: '转换带参数的 Liquid include 标签到 Sline include 标签',
    pattern: /{%-?\s*include\s+['"]([^'"]+?)(?:\.liquid)?['"]\s*,\s*([^%}]+?)\s*-?%}/g,
    replacement: (match, templateName, params) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      // 处理参数
      let processedParams = params.trim();
      
      // 转换参数格式：key: value 到 key=value
      processedParams = processedParams.replace(/(\w+):\s*([^,]+)/g, '$1=$2');
      
      return `${whitespaceStart}#include "${cleanTemplateName}" ${processedParams} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% include "product-card.liquid", product: featured_product %}',
      sline: '{{#include "product-card" product=featured_product /}}'
    }
  },
  {
    name: 'Liquid render 标签转换',
    category: 'include',
    description: '转换 Liquid render 标签到 Sline include 标签',
    pattern: /{%-?\s*render\s+['"]([^'"]+?)(?:\.liquid)?['"]\s*-?%}/g,
    replacement: (match, templateName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      return `${whitespaceStart}#include "${cleanTemplateName}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% render "product-card" %}',
      sline: '{{#include "product-card" /}}'
    }
  },
  {
    name: 'Liquid render 标签转换 - 带参数',
    category: 'include',
    description: '转换带参数的 Liquid render 标签到 Sline include 标签',
    pattern: /{%-?\s*render\s+['"]([^'"]+?)(?:\.liquid)?['"]\s*,\s*([^%}]+?)\s*-?%}/g,
    replacement: (match, templateName, params) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      // 处理参数
      let processedParams = params.trim();
      
      // 转换参数格式：key: value 到 key=value
      processedParams = processedParams.replace(/(\w+):\s*([^,]+)/g, '$1=$2');
      
      return `${whitespaceStart}#include "${cleanTemplateName}" ${processedParams} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% render "product-card", product: featured_product %}',
      sline: '{{#include "product-card" product=featured_product /}}'
    }
  },
  {
    name: 'Liquid render 标签转换 - with 语法',
    category: 'include',
    description: '转换使用 with 语法的 Liquid render 标签',
    pattern: /{%-?\s*render\s+['"]([^'"]+?)(?:\.liquid)?['"]\s+with\s+([^%}]+?)\s*-?%}/g,
    replacement: (match, templateName, variable) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      // 处理 with 变量
      const processedVariable = variable.trim();
      
      return `${whitespaceStart}#include "${cleanTemplateName}" with=${processedVariable} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% render "product-card" with featured_product %}',
      sline: '{{#include "product-card" with=featured_product /}}'
    }
  },
  {
    name: 'Liquid render 标签转换 - as 语法',
    category: 'include',
    description: '转换使用 as 语法的 Liquid render 标签',
    pattern: /{%-?\s*render\s+['"]([^'"]+?)(?:\.liquid)?['"]\s+with\s+([^%}]+?)\s+as\s+(\w+)\s*-?%}/g,
    replacement: (match, templateName, variable, alias) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 移除 .liquid 扩展名（如果存在）
      const cleanTemplateName = templateName.replace(/\.liquid$/, '');
      
      // 处理变量和别名
      const processedVariable = variable.trim();
      
      return `${whitespaceStart}#include "${cleanTemplateName}" ${alias}=${processedVariable} /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% render "product-card" with featured_product as product %}',
      sline: '{{#include "product-card" product=featured_product /}}'
    }
  },
  {
    name: 'Liquid section 标签转换',
    category: 'include',
    description: '转换 Liquid section 标签到 Sline include 标签',
    pattern: /{%-?\s*section\s+['"]([^'"]+?)['"]\s*-?%}/g,
    replacement: (match, sectionName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      return `${whitespaceStart}#include "${sectionName}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% section "header" %}',
      sline: '{{#include "header" /}}'
    }
  },
  {
    name: 'Liquid 特殊包含标签转换',
    category: 'include',
    description: '转换特殊的 Liquid 包含标签（如 render_section）',
    pattern: /{%-?\s*(render_section|include_global_section|render_footer|render_header|render_announcement)\s*-?%}/g,
    replacement: (match, tagName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      return `${whitespaceStart}#include "${tagName}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% render_footer %}',
      sline: '{{#include "render_footer" /}}'
    }
  },
  {
    name: 'Liquid layout 标签转换',
    category: 'include',
    description: '转换 Liquid layout 标签到 Sline layout 标签',
    pattern: /{%-?\s*layout\s+['"]([^'"]+?)['"]\s*-?%}/g,
    replacement: (match, layoutName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      return `${whitespaceStart}#layout "${layoutName}" /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% layout "theme" %}',
      sline: '{{#layout "theme" /}}'
    }
  },
  {
    name: 'Liquid content_for 标签转换',
    category: 'include',
    description: '转换 Liquid content_for 标签到 Sline content_for 标签',
    pattern: /{%-?\s*content_for\s+['"]([^'"]+?)['"]\s*-?%}/g,
    replacement: (match, contentName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      return `${whitespaceStart}#content_for "${contentName}"${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% content_for "header" %}',
      sline: '{{#content_for "header"}}'
    }
  },
  {
    name: 'Liquid endcontent_for 标签转换',
    category: 'include',
    description: '转换 Liquid endcontent_for 标签到 Sline /content_for 标签',
    pattern: /{%-?\s*endcontent_for\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      return `${whitespaceStart}/content_for${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endcontent_for %}',
      sline: '{{/content_for}}'
    }
  },
  {
    name: 'Liquid yield 标签转换',
    category: 'include',
    description: '转换 Liquid yield 标签到 Sline yield 标签',
    pattern: /{%-?\s*yield\s*(?:['"]([^'"]+?)['"]\s*)?-?%}/g,
    replacement: (match, yieldName) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      if (yieldName) {
        return `${whitespaceStart}#yield "${yieldName}" /${whitespaceEnd}`;
      } else {
        return `${whitespaceStart}#yield /${whitespaceEnd}`;
      }
    },
    examples: {
      liquid: '{% yield %}',
      sline: '{{#yield /}}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取包含标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidIncludeTagStats() {
  return {
    totalRules: LIQUID_INCLUDE_TAG_RULES.length,
    rulesByType: {
      include: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('include')).length,
      render: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('render')).length,
      section: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('section')).length,
      layout: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('layout')).length,
      content: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('content')).length,
      yield: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('yield')).length,
      special: LIQUID_INCLUDE_TAG_RULES.filter(rule => rule.name.includes('特殊')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_INCLUDE_TAG_RULES,
  getLiquidIncludeTagStats
};
