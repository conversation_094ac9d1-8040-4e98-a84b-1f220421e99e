/**
 * Liquid2Sline 模块化规则系统 - 主入口文件
 * 
 * 这个文件汇总所有 Liquid 到 Sline 的转换规则和工具函数，提供统一的接口
 * 基于 handlebars-to-sline 规则系统的架构设计
 * 
 * 模块结构：
 * - mappings/     - 映射表配置
 * - tags/         - 标签转换规则
 * - processing/   - 预处理和后处理
 * - utils/        - 辅助工具函数
 */

// ================== 导入映射表 ==================
const { 
  LIQUID_OBJECT_MAPPINGS, 
  LIQUID_OBJECT_CATEGORIES, 
  getLiquidObjectByName, 
  getLiquidObjectStats 
} = require('./mappings/objects');

const { 
  LIQUID_FILTER_MAPPINGS, 
  LIQUID_FILTER_CATEGORIES, 
  getLiquidFilterByName, 
  getLiquidFilterStats, 
  getLiquidFiltersByCategory 
} = require('./mappings/filters');

const { 
  LIQUID_VARIABLE_MAPPINGS, 
  LIQUID_OPERATOR_MAPPINGS, 
  getLiquidVariableByName, 
  getLiquidOperatorByName, 
  getLiquidVariableStats, 
  isLiquidLoopVariable, 
  isLiquidConvertibleOperator 
} = require('./mappings/variables');

// ================== 导入标签转换规则 ==================
const { 
  LIQUID_CONDITIONAL_TAG_RULES, 
  getLiquidConditionalTagStats, 
  getLiquidConditionalRuleByName 
} = require('./tags/conditional');

const { 
  LIQUID_ITERATION_TAG_RULES, 
  getLiquidIterationTagStats, 
  getLiquidIterationRuleByName, 
  getLiquidLoopVariableRules 
} = require('./tags/iteration');

const { 
  LIQUID_ASSIGNMENT_TAG_RULES, 
  getLiquidAssignmentTagStats 
} = require('./tags/assignment');

const { 
  LIQUID_OUTPUT_TAG_RULES, 
  getLiquidOutputTagStats 
} = require('./tags/output');

const { 
  LIQUID_INCLUDE_TAG_RULES, 
  getLiquidIncludeTagStats 
} = require('./tags/include');

const { 
  LIQUID_COMMENT_TAG_RULES, 
  getLiquidCommentTagStats 
} = require('./tags/comment');

const { 
  LIQUID_FILTER_TAG_RULES, 
  getLiquidFilterTagStats 
} = require('./tags/filter');

// ================== 导入处理规则 ==================
const { 
  LIQUID_PREPROCESS_RULES, 
  applyLiquidPreprocessRules, 
  getLiquidPreprocessStats 
} = require('./processing/preprocess');

const { 
  LIQUID_POSTPROCESS_RULES, 
  applyLiquidPostprocessRules, 
  getLiquidPostprocessStats, 
  cleanupLiquidConversionResult 
} = require('./processing/postprocess');

// ================== 导入辅助工具 ==================
const { 
  handleLiquidWhitespace, 
  getLiquidLineNumber, 
  parseLiquidTagParameters, 
  isValidLiquidVariableName, 
  extractLiquidQuotedStrings, 
  cleanLiquidTagContent, 
  analyzeLiquidExpressionComplexity 
} = require('./utils/helpers');

const { 
  validateLiquidConversion, 
  getLiquidConversionStats, 
  analyzeLiquidCodeComplexity, 
  checkLiquidSyntaxValidity, 
  generateLiquidConversionReport 
} = require('./utils/validation');

// ================== 组合所有规则 ==================
const ALL_LIQUID_TAG_RULES = [
  ...LIQUID_CONDITIONAL_TAG_RULES,
  ...LIQUID_ITERATION_TAG_RULES,
  ...LIQUID_ASSIGNMENT_TAG_RULES,
  ...LIQUID_OUTPUT_TAG_RULES,
  ...LIQUID_INCLUDE_TAG_RULES,
  ...LIQUID_COMMENT_TAG_RULES,
  ...LIQUID_FILTER_TAG_RULES
];

// ================== 高级接口函数 ==================

/**
 * 根据分类获取规则
 * @param {string} category - 规则分类
 * @returns {Array} 规则数组
 */
function getLiquidRulesByCategory(category) {
  switch(category.toLowerCase()) {
    case 'conditional':
      return LIQUID_CONDITIONAL_TAG_RULES;
    case 'iteration':
      return LIQUID_ITERATION_TAG_RULES;
    case 'assignment':
      return LIQUID_ASSIGNMENT_TAG_RULES;
    case 'output':
      return LIQUID_OUTPUT_TAG_RULES;
    case 'include':
      return LIQUID_INCLUDE_TAG_RULES;
    case 'comment':
      return LIQUID_COMMENT_TAG_RULES;
    case 'filter':
      return LIQUID_FILTER_TAG_RULES;
    default:
      return ALL_LIQUID_TAG_RULES.filter(rule => rule.category === category);
  }
}

/**
 * 获取所有可用的分类
 * @returns {Array} 分类数组
 */
function getAllLiquidCategories() {
  return [
    { name: 'conditional', description: 'Liquid 条件判断标签', count: LIQUID_CONDITIONAL_TAG_RULES.length },
    { name: 'iteration', description: 'Liquid 循环迭代标签', count: LIQUID_ITERATION_TAG_RULES.length },
    { name: 'assignment', description: 'Liquid 变量赋值标签', count: LIQUID_ASSIGNMENT_TAG_RULES.length },
    { name: 'output', description: 'Liquid 输出格式标签', count: LIQUID_OUTPUT_TAG_RULES.length },
    { name: 'include', description: 'Liquid 包含模板标签', count: LIQUID_INCLUDE_TAG_RULES.length },
    { name: 'comment', description: 'Liquid 注释标签', count: LIQUID_COMMENT_TAG_RULES.length },
    { name: 'filter', description: 'Liquid 过滤器处理标签', count: LIQUID_FILTER_TAG_RULES.length }
  ];
}

/**
 * 根据名称获取特定规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getLiquidRuleByName(ruleName) {
  return ALL_LIQUID_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

/**
 * 获取系统完整统计信息
 * @returns {Object} 详细统计信息
 */
function getLiquidStats() {
  return {
    // 规则统计
    totalRules: ALL_LIQUID_TAG_RULES.length,
    rulesByCategory: {
      conditional: LIQUID_CONDITIONAL_TAG_RULES.length,
      iteration: LIQUID_ITERATION_TAG_RULES.length,
      assignment: LIQUID_ASSIGNMENT_TAG_RULES.length,
      output: LIQUID_OUTPUT_TAG_RULES.length,
      include: LIQUID_INCLUDE_TAG_RULES.length,
      comment: LIQUID_COMMENT_TAG_RULES.length,
      filter: LIQUID_FILTER_TAG_RULES.length
    },
    
    // 映射统计
    mappings: {
      objects: Object.keys(LIQUID_OBJECT_MAPPINGS).length,
      filters: Object.keys(LIQUID_FILTER_MAPPINGS).length,
      variables: Object.keys(LIQUID_VARIABLE_MAPPINGS).length,
      operators: Object.keys(LIQUID_OPERATOR_MAPPINGS).length
    },
    
    // 处理规则统计
    processing: {
      preprocess: LIQUID_PREPROCESS_RULES.length,
      postprocess: LIQUID_POSTPROCESS_RULES.length
    },
    
    // 分类统计
    categories: {
      objects: LIQUID_OBJECT_CATEGORIES.length,
      filters: LIQUID_FILTER_CATEGORIES.length
    }
  };
}

/**
 * 获取系统信息
 * @returns {Object} 系统信息
 */
function getLiquidSystemInfo() {
  const stats = getLiquidStats();
  
  return {
    version: '1.0.0',
    architecture: 'modular',
    lastUpdated: new Date().toISOString(),
    totalComponents: 7,
    description: 'Liquid2Sline 模块化转换规则系统',
    
    modules: {
      mappings: ['objects', 'filters', 'variables'],
      tags: ['conditional', 'iteration', 'assignment', 'output', 'include', 'comment', 'filter'],
      processing: ['preprocess', 'postprocess'],
      utils: ['helpers', 'validation']
    },
    
    stats: stats,
    
    features: [
      '🔄 Liquid 到 Sline 转换',
      '🆕 模块化架构',
      '🔧 可独立测试',
      '📊 详细统计',
      '✅ 质量验证',
      '🚀 易于扩展',
      '📝 完整文档'
    ]
  };
}

// ================== 核心转换功能 ==================
/**
 * 应用 Liquid 转换规则到文本
 * @param {string} text - 要转换的 Liquid 文本
 * @param {Object} options - 转换选项
 * @returns {Object} 转换结果 {text, stats, warnings}
 */
function applyLiquidConversionRules(text, options = {}) {
  const {
    logLevel = 'error',
    includeStats = true,
    includeWarnings = true
  } = options;
  
  let output = text;
  const appliedRules = [];
  const warnings = [];
  
  try {
    // 预处理
    output = applyLiquidPreprocessRules(output);

    // 按分类应用规则，避免重复处理
    const categories = ['conditional', 'iteration', 'assignment', 'include', 'comment', 'output', 'filter'];

    for (const category of categories) {
      const rules = getLiquidRulesByCategory(category);
      let categoryApplied = 0;

      rules.forEach(rule => {
        if (rule.pattern && rule.replacement) {
          const beforeText = output;
          output = output.replace(rule.pattern, rule.replacement);
          if (output !== beforeText) {
            appliedRules.push({
              category,
              name: rule.name || 'unnamed',
              matches: 1
            });
            categoryApplied++;
          }
        } else if (rule.apply && typeof rule.apply === 'function') {
          const beforeText = output;
          output = rule.apply(output);
          if (output !== beforeText) {
            appliedRules.push({
              category,
              name: rule.name || 'custom function',
              matches: 1
            });
            categoryApplied++;
          }
        }
      });

      if (logLevel === 'info' && categoryApplied > 0) {
        console.log(`[INFO] ${category} 分类转换完成，应用了 ${categoryApplied} 个规则`);
      }
    }

    // 后处理
    output = applyLiquidPostprocessRules(output);

    const result = {
      text: output,
      success: true
    };
    
    if (includeStats) {
      result.stats = {
        totalRulesApplied: appliedRules.length,
        rulesByCategory: appliedRules.reduce((acc, rule) => {
          acc[rule.category] = (acc[rule.category] || 0) + 1;
          return acc;
        }, {}),
        appliedRules
      };
    }
    
    if (includeWarnings && warnings.length > 0) {
      result.warnings = warnings;
    }
    
    return result;
    
  } catch (error) {
    return {
      text: text,
      success: false,
      error: error.message
    };
  }
}

// ================== 导出所有内容 ==================
module.exports = {
  // 主要映射表
  LIQUID_OBJECT_MAPPINGS,
  LIQUID_FILTER_MAPPINGS,
  LIQUID_VARIABLE_MAPPINGS,
  LIQUID_OPERATOR_MAPPINGS,
  
  // 分类结构
  LIQUID_FILTER_CATEGORIES,
  LIQUID_OBJECT_CATEGORIES,
  
  // 标签转换规则
  LIQUID_CONDITIONAL_TAG_RULES,
  LIQUID_ITERATION_TAG_RULES,
  LIQUID_ASSIGNMENT_TAG_RULES,
  LIQUID_OUTPUT_TAG_RULES,
  LIQUID_INCLUDE_TAG_RULES,
  LIQUID_COMMENT_TAG_RULES,
  LIQUID_FILTER_TAG_RULES,
  
  // 组合规则
  ALL_LIQUID_TAG_RULES,
  LIQUID_PREPROCESS_RULES,
  LIQUID_POSTPROCESS_RULES,
  
  // 高级接口函数
  getLiquidRulesByCategory,
  getAllLiquidCategories,
  getLiquidRuleByName,
  getLiquidFilterByName,
  getLiquidObjectByName,
  getLiquidVariableByName,
  getLiquidOperatorByName,
  
  // 统计函数
  getLiquidStats,
  getLiquidSystemInfo,
  getLiquidObjectStats,
  getLiquidFilterStats,
  getLiquidVariableStats,
  getLiquidConditionalTagStats,
  getLiquidIterationTagStats,
  getLiquidAssignmentTagStats,
  getLiquidOutputTagStats,
  getLiquidIncludeTagStats,
  getLiquidCommentTagStats,
  getLiquidFilterTagStats,
  getLiquidPreprocessStats,
  getLiquidPostprocessStats,
  
  // 处理函数
  applyLiquidPreprocessRules,
  applyLiquidPostprocessRules,
  
  // 核心转换功能
  applyLiquidConversionRules,
  
  // 验证函数
  validateLiquidConversion,
  getLiquidConversionStats,
  analyzeLiquidCodeComplexity,
  checkLiquidSyntaxValidity,
  generateLiquidConversionReport,
  
  // 辅助函数
  handleLiquidWhitespace,
  getLiquidLineNumber,
  parseLiquidTagParameters,
  isValidLiquidVariableName,
  extractLiquidQuotedStrings,
  cleanLiquidTagContent,
  analyzeLiquidExpressionComplexity,
  isLiquidLoopVariable,
  isLiquidConvertibleOperator,
  
  // 专用函数
  getLiquidFiltersByCategory,
  getLiquidConditionalRuleByName,
  getLiquidIterationRuleByName,
  getLiquidLoopVariableRules
};
