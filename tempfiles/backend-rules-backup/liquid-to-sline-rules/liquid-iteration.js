/**
 * Liquid 循环标签转换规则
 * 处理 for、endfor、break、continue 等循环语句的转换
 */

const { LIQUID_VARIABLE_MAPPINGS } = require('../mappings/variables');

// ================== Liquid 循环标签转换规则 ==================
const LIQUID_ITERATION_TAG_RULES = [
  {
    name: 'Liquid for 标签转换 - 基础循环',
    category: 'iteration',
    description: '转换 Liquid for 标签到 Sline for 标签',
    pattern: /{%-?\s*for\s+(\w+)\s+in\s+([a-zA-Z][\w.-]*(?:\.[a-zA-Z][\w.-]*)*)\s*-?%}/g,
    replacement: (match, variable, collection) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#for ${variable} in ${collection}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% for product in collection.products %}',
      sline: '{{#for product in collection.products}}'
    }
  },
  {
    name: 'Liquid for 标签转换 - 带过滤器',
    category: 'iteration',
    description: '转换带过滤器的 Liquid for 标签到 Sline for 标签',
    pattern: /{%-?\s*for\s+(\w+)\s+in\s+([a-zA-Z][\w.-]*(?:\.[a-zA-Z][\w.-]*)*)\s+([^%}]+)\s*-?%}/g,
    replacement: (match, variable, collection, filters) => {
      // 处理过滤器
      let processedFilters = filters.trim();
      
      // 转换常见的过滤器
      processedFilters = processedFilters.replace(/limit:\s*(\d+)/g, '|limit($1)');
      processedFilters = processedFilters.replace(/offset:\s*(\d+)/g, '|offset($1)');
      processedFilters = processedFilters.replace(/reversed/g, '|reversed');
      
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#for ${variable} in ${collection}${processedFilters}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% for product in collection.products limit: 5 %}',
      sline: '{{#for product in collection.products|limit(5)}}'
    }
  },
  {
    name: 'Liquid for 标签转换 - 范围循环',
    category: 'iteration',
    description: '转换 Liquid 范围循环到 Sline for 标签',
    pattern: /{%-?\s*for\s+(\w+)\s+in\s+\((\d+|[\w.]+)\.\.(\d+|[\w.]+)\)\s*-?%}/g,
    replacement: (match, variable, start, end) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#for ${variable} in ${end}|range(${start})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% for i in (1..5) %}',
      sline: '{{#for i in 5|range(1)}}'
    }
  },
  {
    name: 'Liquid endfor 标签转换',
    category: 'iteration',
    description: '转换 Liquid endfor 标签到 Sline /for 标签',
    pattern: /{%-?\s*endfor\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/for${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endfor %}',
      sline: '{{/for}}'
    }
  },
  {
    name: 'Liquid for-else 标签转换',
    category: 'iteration',
    description: '转换 Liquid for 循环中的 else 标签到 Sline else 标签',
    pattern: /{%-?\s*else\s*-?%}(?=[\s\S]*?{%-?\s*endfor\s*-?%})/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#else /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% else %}',
      sline: '{{#else /}}'
    }
  },
  {
    name: 'Liquid break 标签转换',
    category: 'iteration',
    description: '转换 Liquid break 标签到 Sline break 标签',
    pattern: /{%-?\s*break\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#break /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% break %}',
      sline: '{{#break /}}'
    }
  },
  {
    name: 'Liquid continue 标签转换',
    category: 'iteration',
    description: '转换 Liquid continue 标签到 Sline continue 标签',
    pattern: /{%-?\s*continue\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}#continue /${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% continue %}',
      sline: '{{#continue /}}'
    }
  },
  {
    name: 'Liquid tablerow 标签转换',
    category: 'iteration',
    description: '转换 Liquid tablerow 标签到 Sline tablerow 标签',
    pattern: /{%-?\s*tablerow\s+(\w+)\s+in\s+([a-zA-Z][\w.-]*(?:\.[a-zA-Z][\w.-]*)*)\s*(?:cols:\s*(\d+))?\s*-?%}/g,
    replacement: (match, variable, collection, cols) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      const colsAttr = cols ? ` cols="${cols}"` : '';
      return `${whitespaceStart}#tablerow ${variable} in ${collection}${colsAttr}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% tablerow product in collection.products cols: 3 %}',
      sline: '{{#tablerow product in collection.products cols="3"}}'
    }
  },
  {
    name: 'Liquid endtablerow 标签转换',
    category: 'iteration',
    description: '转换 Liquid endtablerow 标签到 Sline /tablerow 标签',
    pattern: /{%-?\s*endtablerow\s*-?%}/g,
    replacement: (match) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      return `${whitespaceStart}/tablerow${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% endtablerow %}',
      sline: '{{/tablerow}}'
    }
  },
  {
    name: 'Liquid cycle 标签转换',
    category: 'iteration',
    description: '转换 Liquid cycle 标签到 Sline cycle 标签',
    pattern: /{%-?\s*cycle\s+(.*?)\s*-?%}/g,
    replacement: (match, values) => {
      const whitespaceStart = match.startsWith('{%-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-%}') ? '~}}' : '}}';
      
      // 处理 cycle 的值列表
      const processedValues = values.split(',').map(v => {
        const trimmed = v.trim();
        // 如果已经有引号，保持原样
        if ((trimmed.startsWith('"') && trimmed.endsWith('"')) ||
            (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
          return trimmed.replace(/'/g, '"');
        }
        // 如果是变量，不加引号
        if (/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(trimmed)) {
          return trimmed;
        }
        // 其他情况加双引号
        return `"${trimmed}"`;
      }).join(', ');
      
      return `${whitespaceStart}#cycle ${processedValues}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{% cycle "odd", "even" %}',
      sline: '{{#cycle "odd", "even"}}'
    }
  },
  {
    name: 'Liquid forloop 变量转换',
    category: 'iteration',
    description: '转换 Liquid forloop 变量到 Sline 循环变量',
    pattern: /\{\{\s*forloop\.(\w+)\s*\}\}/g,
    replacement: (match, property) => {
      const slineVar = LIQUID_VARIABLE_MAPPINGS[`forloop.${property}`];
      if (slineVar) {
        return `{{${slineVar}}}`;
      }
      return match; // 如果没有映射，保持原样
    },
    examples: {
      liquid: '{{ forloop.index }}',
      sline: '{{@index}}'
    }
  },
  {
    name: 'Liquid tablerowloop 变量转换',
    category: 'iteration',
    description: '转换 Liquid tablerowloop 变量到 Sline 循环变量',
    pattern: /\{\{\s*tablerowloop\.(\w+)\s*\}\}/g,
    replacement: (match, property) => {
      const slineVar = LIQUID_VARIABLE_MAPPINGS[`tablerowloop.${property}`];
      if (slineVar) {
        return `{{${slineVar}}}`;
      }
      return match; // 如果没有映射，保持原样
    },
    examples: {
      liquid: '{{ tablerowloop.index }}',
      sline: '{{@index}}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取循环标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidIterationTagStats() {
  return {
    totalRules: LIQUID_ITERATION_TAG_RULES.length,
    rulesByType: {
      for: LIQUID_ITERATION_TAG_RULES.filter(rule => rule.name.includes('for') && !rule.name.includes('tablerow')).length,
      tablerow: LIQUID_ITERATION_TAG_RULES.filter(rule => rule.name.includes('tablerow')).length,
      control: LIQUID_ITERATION_TAG_RULES.filter(rule => rule.name.includes('break') || rule.name.includes('continue')).length,
      variables: LIQUID_ITERATION_TAG_RULES.filter(rule => rule.name.includes('变量')).length,
      cycle: LIQUID_ITERATION_TAG_RULES.filter(rule => rule.name.includes('cycle')).length
    }
  };
}

/**
 * 根据名称获取循环规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getLiquidIterationRuleByName(ruleName) {
  return LIQUID_ITERATION_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

/**
 * 获取循环变量规则
 * @returns {Array} 循环变量规则数组
 */
function getLiquidLoopVariableRules() {
  return LIQUID_ITERATION_TAG_RULES.filter(rule => 
    rule.name.includes('forloop') || rule.name.includes('tablerowloop')
  );
}

// ================== 导出 ==================
module.exports = {
  LIQUID_ITERATION_TAG_RULES,
  getLiquidIterationTagStats,
  getLiquidIterationRuleByName,
  getLiquidLoopVariableRules
};
