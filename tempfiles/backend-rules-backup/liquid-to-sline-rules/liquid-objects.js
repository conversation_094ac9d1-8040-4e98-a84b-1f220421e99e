/**
 * Liquid 对象映射表
 * 定义 Liquid 对象到 Sline 对象的映射关系
 */

// ================== Liquid 对象映射表 ==================
const LIQUID_OBJECT_MAPPINGS = {
  // 商店相关对象
  'shop': 'shop',
  'shop.name': 'shop.name',
  'shop.domain': 'shop.domain',
  'shop.url': 'shop.url',
  'shop.email': 'shop.email',
  'shop.description': 'shop.description',
  'shop.money_format': 'shop.money_format',
  'shop.currency': 'shop.currency',
  'shop.timezone': 'shop.timezone',
  'shop.address': 'shop.address',
  'shop.phone': 'shop.phone',
  
  // 产品相关对象
  'product': 'product',
  'product.id': 'product.id',
  'product.title': 'product.title',
  'product.handle': 'product.handle',
  'product.description': 'product.description',
  'product.content': 'product.content',
  'product.excerpt': 'product.excerpt',
  'product.vendor': 'product.vendor',
  'product.type': 'product.type',
  'product.price': 'product.price',
  'product.price_min': 'product.price_min',
  'product.price_max': 'product.price_max',
  'product.compare_at_price': 'product.compare_at_price',
  'product.compare_at_price_min': 'product.compare_at_price_min',
  'product.compare_at_price_max': 'product.compare_at_price_max',
  'product.available': 'product.available',
  'product.tags': 'product.tags',
  'product.variants': 'product.variants',
  'product.images': 'product.images',
  'product.featured_image': 'product.featured_image',
  'product.url': 'product.url',
  'product.created_at': 'product.created_at',
  'product.updated_at': 'product.updated_at',
  
  // 变体相关对象
  'variant': 'variant',
  'variant.id': 'variant.id',
  'variant.title': 'variant.title',
  'variant.option1': 'variant.option1',
  'variant.option2': 'variant.option2',
  'variant.option3': 'variant.option3',
  'variant.price': 'variant.price',
  'variant.compare_at_price': 'variant.compare_at_price',
  'variant.sku': 'variant.sku',
  'variant.barcode': 'variant.barcode',
  'variant.inventory_quantity': 'variant.inventory_quantity',
  'variant.available': 'variant.available',
  'variant.weight': 'variant.weight',
  'variant.image': 'variant.image',
  
  // 集合相关对象
  'collection': 'collection',
  'collection.id': 'collection.id',
  'collection.title': 'collection.title',
  'collection.handle': 'collection.handle',
  'collection.description': 'collection.description',
  'collection.image': 'collection.image',
  'collection.url': 'collection.url',
  'collection.products': 'collection.products',
  'collections': 'collections',
  
  // 购物车相关对象
  'cart': 'cart',
  'cart.item_count': 'cart.item_count',
  'cart.total_price': 'cart.total_price',
  'cart.total_weight': 'cart.total_weight',
  'cart.items': 'cart.items',
  'cart.note': 'cart.note',
  'cart.attributes': 'cart.attributes',
  
  // 购物车项目
  'item': 'item',
  'item.id': 'item.id',
  'item.product_id': 'item.product_id',
  'item.variant_id': 'item.variant_id',
  'item.title': 'item.title',
  'item.price': 'item.price',
  'item.line_price': 'item.line_price',
  'item.quantity': 'item.quantity',
  'item.sku': 'item.sku',
  'item.vendor': 'item.vendor',
  'item.product_title': 'item.product_title',
  'item.variant_title': 'item.variant_title',
  'item.image': 'item.image',
  'item.url': 'item.url',
  'item.properties': 'item.properties',
  
  // 客户相关对象
  'customer': 'customer',
  'customer.id': 'customer.id',
  'customer.email': 'customer.email',
  'customer.first_name': 'customer.first_name',
  'customer.last_name': 'customer.last_name',
  'customer.name': 'customer.name',
  'customer.phone': 'customer.phone',
  'customer.accepts_marketing': 'customer.accepts_marketing',
  'customer.tags': 'customer.tags',
  'customer.orders_count': 'customer.orders_count',
  'customer.total_spent': 'customer.total_spent',
  'customer.addresses': 'customer.addresses',
  'customer.default_address': 'customer.default_address',
  
  // 地址相关对象
  'address': 'address',
  'address.first_name': 'address.first_name',
  'address.last_name': 'address.last_name',
  'address.company': 'address.company',
  'address.address1': 'address.address1',
  'address.address2': 'address.address2',
  'address.city': 'address.city',
  'address.province': 'address.province',
  'address.country': 'address.country',
  'address.zip': 'address.zip',
  'address.phone': 'address.phone',
  
  // 订单相关对象
  'order': 'order',
  'order.id': 'order.id',
  'order.name': 'order.name',
  'order.email': 'order.email',
  'order.created_at': 'order.created_at',
  'order.updated_at': 'order.updated_at',
  'order.cancelled_at': 'order.cancelled_at',
  'order.cancel_reason': 'order.cancel_reason',
  'order.total_price': 'order.total_price',
  'order.subtotal_price': 'order.subtotal_price',
  'order.total_tax': 'order.total_tax',
  'order.tax_lines': 'order.tax_lines',
  'order.shipping_price': 'order.shipping_price',
  'order.shipping_methods': 'order.shipping_methods',
  'order.line_items': 'order.line_items',
  'order.billing_address': 'order.billing_address',
  'order.shipping_address': 'order.shipping_address',
  'order.customer': 'order.customer',
  'order.note': 'order.note',
  'order.attributes': 'order.attributes',
  
  // 页面相关对象
  'page': 'page',
  'page.id': 'page.id',
  'page.title': 'page.title',
  'page.content': 'page.content',
  'page.excerpt': 'page.excerpt',
  'page.handle': 'page.handle',
  'page.url': 'page.url',
  'page.created_at': 'page.created_at',
  'page.updated_at': 'page.updated_at',
  'page.author': 'page.author',
  'pages': 'pages',
  
  // 博客相关对象
  'blog': 'blog',
  'blog.id': 'blog.id',
  'blog.title': 'blog.title',
  'blog.handle': 'blog.handle',
  'blog.url': 'blog.url',
  'blogs': 'blogs',
  
  // 文章相关对象
  'article': 'article',
  'article.id': 'article.id',
  'article.title': 'article.title',
  'article.content': 'article.content',
  'article.excerpt': 'article.excerpt',
  'article.author': 'article.author',
  'article.created_at': 'article.created_at',
  'article.updated_at': 'article.updated_at',
  'article.tags': 'article.tags',
  'article.url': 'article.url',
  'article.image': 'article.image',
  
  // 图片相关对象
  'image': 'image',
  'image.alt': 'image.alt',
  'image.width': 'image.width',
  'image.height': 'image.height',
  'image.url': 'image.url',
  
  // 链接列表相关对象
  'linklists': 'linklists',
  'linklist': 'linklist',
  'link': 'link',
  'link.title': 'link.title',
  'link.url': 'link.url',
  'link.type': 'link.type',
  'link.object': 'link.object',
  
  // 表单相关对象
  'form': 'form',
  'form.posted_successfully?': 'form.posted_successfully',
  'form.errors': 'form.errors',
  'form.id': 'form.id',
  
  // 搜索相关对象
  'search': 'search',
  'search.performed': 'search.performed',
  'search.results': 'search.results',
  'search.results_count': 'search.results_count',
  'search.terms': 'search.terms',
  
  // 请求相关对象
  'request': 'request',
  'request.host': 'request.host',
  'request.origin': 'request.origin',
  'request.page_type': 'request.page_type',
  
  // 模板相关对象
  'template': 'template',
  'template.name': 'template.name',
  'template.directory': 'template.directory',
  'template.suffix': 'template.suffix',
  
  // 设置相关对象
  'settings': 'settings',
  
  // 全局对象
  'canonical_url': 'canonical_url',
  'content_for_header': 'content_for_header',
  'content_for_layout': 'content_for_layout',
  'page_title': 'page_title',
  'page_description': 'page_description'
};

// ================== 对象分类 ==================
const LIQUID_OBJECT_CATEGORIES = [
  {
    name: 'shop',
    description: '商店信息对象',
    objects: ['shop', 'settings']
  },
  {
    name: 'product',
    description: '产品相关对象',
    objects: ['product', 'variant', 'collection', 'collections']
  },
  {
    name: 'cart',
    description: '购物车相关对象',
    objects: ['cart', 'item']
  },
  {
    name: 'customer',
    description: '客户相关对象',
    objects: ['customer', 'address', 'order']
  },
  {
    name: 'content',
    description: '内容相关对象',
    objects: ['page', 'pages', 'blog', 'blogs', 'article']
  },
  {
    name: 'navigation',
    description: '导航相关对象',
    objects: ['linklists', 'linklist', 'link']
  },
  {
    name: 'media',
    description: '媒体相关对象',
    objects: ['image']
  },
  {
    name: 'system',
    description: '系统相关对象',
    objects: ['request', 'template', 'form', 'search']
  },
  {
    name: 'global',
    description: '全局对象',
    objects: ['canonical_url', 'content_for_header', 'content_for_layout', 'page_title', 'page_description']
  }
];

// ================== 辅助函数 ==================

/**
 * 根据名称获取对象映射
 * @param {string} objectName - 对象名称
 * @returns {string|null} 映射后的对象名称或 null
 */
function getLiquidObjectByName(objectName) {
  return LIQUID_OBJECT_MAPPINGS[objectName] || null;
}

/**
 * 获取对象统计信息
 * @returns {Object} 统计信息
 */
function getLiquidObjectStats() {
  return {
    totalObjects: Object.keys(LIQUID_OBJECT_MAPPINGS).length,
    categories: LIQUID_OBJECT_CATEGORIES.length,
    objectsByCategory: LIQUID_OBJECT_CATEGORIES.reduce((acc, category) => {
      acc[category.name] = category.objects.length;
      return acc;
    }, {})
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_OBJECT_MAPPINGS,
  LIQUID_OBJECT_CATEGORIES,
  getLiquidObjectByName,
  getLiquidObjectStats
};
