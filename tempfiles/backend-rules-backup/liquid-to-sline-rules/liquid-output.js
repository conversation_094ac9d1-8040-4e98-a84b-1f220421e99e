/**
 * Liquid 输出标签转换规则
 * 处理变量输出和过滤器的转换
 */

// ================== Liquid 输出标签转换规则 ==================
const LIQUID_OUTPUT_TAG_RULES = [
  {
    name: 'Liquid 变量输出转换',
    category: 'output',
    description: '转换 Liquid 变量输出到 Sline 变量输出',
    pattern: /\{\{-?\s*([^}#]+?)\s*-?\}\}/g,
    replacement: (match, content) => {
      // 跳过已经转换的 Sline 标签（包含 # 的）
      if (content.includes('#')) {
        return match;
      }

      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';

      // 处理内容中的过滤器
      let processedContent = content.trim();

      // 先转换带参数的过滤器，使用更精确的正则表达式
      processedContent = processedContent.replace(/\|\s*(\w+):\s*([^|}]+?)(?=\s*\||$)/g, (match, filterName, params) => {
        // 处理参数
        const processedParams = params.trim().split(',').map(param => {
          const trimmedParam = param.trim();
          // 如果参数是字符串字面量，保持引号
          if ((trimmedParam.startsWith('"') && trimmedParam.endsWith('"')) ||
              (trimmedParam.startsWith("'") && trimmedParam.endsWith("'"))) {
            return trimmedParam.replace(/'/g, '"');
          }
          // 如果是数字，保持原样
          if (/^\d+(\.\d+)?$/.test(trimmedParam)) {
            return trimmedParam;
          }
          // 如果是变量，保持原样
          if (/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(trimmedParam)) {
            return trimmedParam;
          }
          // 其他情况加双引号
          return `"${trimmedParam}"`;
        }).join(', ');

        return `|${filterName}(${processedParams})`;
      });

      // 然后转换无参数的过滤器，使用更精确的负向前瞻
      processedContent = processedContent.replace(/\|\s*(\w+)(?!\([^)]*\))(?!\w)/g, '|$1()');

      return `${whitespaceStart}${processedContent}${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.title | upcase }}',
      sline: '{{ product.title|upcase() }}'
    }
  },



  {
    name: 'Liquid 数学表达式输出转换',
    category: 'output',
    description: '转换包含数学运算的 Liquid 变量输出',
    pattern: /\{\{-?\s*([^}]+?)\s*\|\s*(plus|minus|times|divided_by|modulo):\s*([^}|]+?)\s*-?\}\}/g,
    replacement: (match, variable, operation, operand) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      // 处理操作数
      let processedOperand = operand.trim();
      if (!processedOperand.startsWith('"') && !processedOperand.startsWith("'") && 
          !/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedOperand) &&
          !/^\d+(\.\d+)?$/.test(processedOperand)) {
        processedOperand = `"${processedOperand}"`;
      }
      
      return `${whitespaceStart}${variable.trim()}|${operation}(${processedOperand})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.price | plus: 10 }}',
      sline: '{{ product.price|plus(10) }}'
    }
  },
  {
    name: 'Liquid 字符串操作输出转换',
    category: 'output',
    description: '转换包含字符串操作的 Liquid 变量输出',
    pattern: /\{\{-?\s*([^}]+?)\s*\|\s*(replace|remove|append|prepend|split|join):\s*([^}|]+?)\s*-?\}\}/g,
    replacement: (match, variable, operation, params) => {
      const whitespaceStart = match.startsWith('{{-') ? '{{~' : '{{';
      const whitespaceEnd = match.endsWith('-}}') ? '~}}' : '}}';
      
      // 处理参数
      let processedParams = params.trim();
      
      if (operation === 'replace') {
        // replace 需要两个参数
        const paramMatch = processedParams.match(/['"]([^'"]*)['"]\s*,\s*['"]([^'"]*)['"]/);
        if (paramMatch) {
          processedParams = `"${paramMatch[1]}", "${paramMatch[2]}"`;
        }
      } else {
        // 其他操作通常只需要一个参数
        if (!processedParams.startsWith('"') && !processedParams.startsWith("'")) {
          if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(processedParams)) {
            processedParams = `"${processedParams}"`;
          }
        } else {
          processedParams = processedParams.replace(/'/g, '"');
        }
      }
      
      return `${whitespaceStart}${variable.trim()}|${operation}(${processedParams})${whitespaceEnd}`;
    },
    examples: {
      liquid: '{{ product.title | replace: "old", "new" }}',
      sline: '{{ product.title|replace("old", "new") }}'
    }
  }
];

// ================== 辅助函数 ==================

/**
 * 获取输出标签统计信息
 * @returns {Object} 统计信息
 */
function getLiquidOutputTagStats() {
  return {
    totalRules: LIQUID_OUTPUT_TAG_RULES.length,
    rulesByType: {
      basic: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('变量输出')).length,
      filters: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('过滤器')).length,
      complex: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('复杂')).length,
      conditional: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('条件')).length,
      math: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('数学')).length,
      string: LIQUID_OUTPUT_TAG_RULES.filter(rule => rule.name.includes('字符串')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_OUTPUT_TAG_RULES,
  getLiquidOutputTagStats
};
