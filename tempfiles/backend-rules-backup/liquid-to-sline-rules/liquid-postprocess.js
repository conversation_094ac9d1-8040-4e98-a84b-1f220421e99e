/**
 * Liquid 后处理规则
 * 在主要转换之后进行的后处理操作
 */

// ================== Liquid 后处理规则 ==================
const LIQUID_POSTPROCESS_RULES = [
  {
    name: '清理转换标记',
    category: 'cleanup',
    description: '清理转换过程中可能留下的临时标记',
    apply: (text) => {
      // 只移除明确的转换标记，不改变其他格式
      text = text.replace(/<!--\s*LIQUID_CONVERTED\s*-->/g, '');
      text = text.replace(/<!--\s*TODO:\s*[^>]*-->/g, '');

      return text;
    }
  },
  {
    name: '标准化行尾',
    category: 'formatting',
    description: '标准化行尾字符，不改变其他格式',
    apply: (text) => {
      // 只标准化行尾字符，保持所有其他格式
      text = text.replace(/\r\n/g, '\n');
      text = text.replace(/\r/g, '\n');

      return text;
    }
  }
];

// ================== 后处理函数 ==================

/**
 * 应用所有后处理规则
 * @param {string} text - 要后处理的文本
 * @param {Object} options - 后处理选项
 * @returns {string} 后处理后的文本
 */
function applyLiquidPostprocessRules(text, options = {}) {
  const {
    skipCategories = [],
    onlyCategories = null,
    logLevel = 'error'
  } = options;
  
  let result = text;
  let appliedRules = 0;
  
  try {
    LIQUID_POSTPROCESS_RULES.forEach(rule => {
      // 检查是否应该跳过此规则
      if (skipCategories.includes(rule.category)) {
        return;
      }
      
      // 检查是否只处理特定分类
      if (onlyCategories && !onlyCategories.includes(rule.category)) {
        return;
      }
      
      const beforeText = result;
      result = rule.apply(result);
      
      if (result !== beforeText) {
        appliedRules++;
        if (logLevel === 'info') {
          console.log(`[POSTPROCESS] 应用规则: ${rule.name}`);
        }
      }
    });
    
    if (logLevel === 'info') {
      console.log(`[POSTPROCESS] 完成，应用了 ${appliedRules} 个后处理规则`);
    }
    
    return result;
    
  } catch (error) {
    console.error('[POSTPROCESS] 后处理过程中出错:', error);
    return text; // 出错时返回原始文本
  }
}

/**
 * 清理转换结果
 * @param {string} text - 转换后的文本
 * @returns {string} 清理后的文本
 */
function cleanupLiquidConversionResult(text) {
  return applyLiquidPostprocessRules(text, {
    onlyCategories: ['cleanup', 'formatting']
  });
}

/**
 * 获取后处理统计信息
 * @returns {Object} 统计信息
 */
function getLiquidPostprocessStats() {
  const categoryCounts = {};
  
  LIQUID_POSTPROCESS_RULES.forEach(rule => {
    categoryCounts[rule.category] = (categoryCounts[rule.category] || 0) + 1;
  });
  
  return {
    totalRules: LIQUID_POSTPROCESS_RULES.length,
    categories: Object.keys(categoryCounts).length,
    rulesByCategory: categoryCounts,
    availableCategories: Object.keys(categoryCounts)
  };
}

/**
 * 验证后处理结果
 * @param {string} text - 后处理的文本
 * @returns {Object} 验证结果
 */
function validateLiquidPostprocessResult(text) {
  const issues = [];
  
  // 检查标签平衡
  const openTags = [];
  const tagPattern = /{{(#|\/)?(\w+)(?:\s[^}]*)?}}/g;
  let match;
  
  while ((match = tagPattern.exec(text)) !== null) {
    const [fullMatch, prefix, tagName] = match;
    
    if (prefix === '#' && !fullMatch.includes(' /}}')) {
      openTags.push({ tag: tagName, position: match.index });
    } else if (prefix === '/') {
      const lastOpen = openTags.pop();
      if (!lastOpen || lastOpen.tag !== tagName) {
        issues.push(`标签不匹配: 在位置 ${match.index} 期望 ${lastOpen?.tag || 'none'}, 实际 ${tagName}`);
      }
    }
  }
  
  // 检查未闭合的标签
  openTags.forEach(openTag => {
    issues.push(`未闭合的标签: ${openTag.tag} 在位置 ${openTag.position}`);
  });
  
  // 检查语法错误
  const syntaxErrors = [];
  
  // 检查不完整的标签
  if (text.includes('{{') && !text.includes('}}')) {
    syntaxErrors.push('存在不完整的变量输出标签');
  }
  
  // 检查不完整的过滤器
  const incompleteFilters = text.match(/\|[a-zA-Z_][a-zA-Z0-9_]*\(/g);
  if (incompleteFilters) {
    incompleteFilters.forEach(filter => {
      if (!text.includes(filter.replace('(', ')('))) {
        syntaxErrors.push(`不完整的过滤器: ${filter}`);
      }
    });
  }
  
  return {
    isValid: issues.length === 0 && syntaxErrors.length === 0,
    tagIssues: issues,
    syntaxErrors: syntaxErrors,
    totalIssues: issues.length + syntaxErrors.length
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_POSTPROCESS_RULES,
  applyLiquidPostprocessRules,
  cleanupLiquidConversionResult,
  getLiquidPostprocessStats,
  validateLiquidPostprocessResult
};
