/**
 * Liquid 预处理规则
 * 在主要转换之前进行的预处理操作
 */

// ================== Liquid 预处理规则 ==================
const LIQUID_PREPROCESS_RULES = [
  {
    name: '标准化空白字符处理',
    category: 'whitespace',
    description: '标准化 Liquid 标签中的空白字符处理语法',
    apply: (text) => {
      // 标准化 {%- 和 -%} 语法
      text = text.replace(/{%-\s*/g, '{%-');
      text = text.replace(/\s*-%}/g, '-%}');
      text = text.replace(/{{-\s*/g, '{{-');
      text = text.replace(/\s*-}}/g, '-}}');
      return text;
    }
  },
  {
    name: '保守的空白字符标准化',
    category: 'whitespace',
    description: '只标准化明显多余的空白字符，保持格式',
    apply: (text) => {
      // 只处理明显的多余空格，不改变整体格式
      // 标准化标签开始处的多个空格为单个空格
      text = text.replace(/{%\s{2,}/g, '{% ');
      text = text.replace(/\s{2,}%}/g, ' %}');
      text = text.replace(/{{\s{2,}/g, '{{ ');
      text = text.replace(/\s{2,}}}/g, ' }}');
      return text;
    }
  },
  {
    name: '标准化引号使用',
    category: 'quotes',
    description: '将单引号统一转换为双引号',
    apply: (text) => {
      // 在 Liquid 标签内将单引号转换为双引号
      text = text.replace(/{%([^%]*)'([^']*)'([^%]*)%}/g, (match, before, content, after) => {
        return `{%${before}"${content}"${after}%}`;
      });
      text = text.replace(/{{([^}]*)'([^']*)'([^}]*)}}/g, (match, before, content, after) => {
        return `{{${before}"${content}"${after}}}`;
      });
      return text;
    }
  },
  {
    name: '标准化过滤器语法',
    category: 'filters',
    description: '标准化 Liquid 过滤器的语法格式',
    apply: (text) => {
      // 标准化过滤器参数的空格
      text = text.replace(/\|\s*(\w+)\s*:\s*/g, '| $1: ');
      text = text.replace(/\|\s*(\w+)\s*(?!\:)/g, '| $1');
      return text;
    }
  },
  {
    name: '预处理嵌套标签',
    category: 'nesting',
    description: '预处理可能存在的嵌套标签结构',
    apply: (text) => {
      // 标记嵌套的 if 语句
      let depth = 0;
      text = text.replace(/{%\s*if\s/g, (match) => {
        depth++;
        return `{% if `;
      });
      text = text.replace(/{%\s*endif\s*%}/g, (match) => {
        depth--;
        return `{% endif %}`;
      });
      return text;
    }
  },
  {
    name: '预处理特殊字符',
    category: 'special',
    description: '预处理 Liquid 中的特殊字符和转义序列',
    apply: (text) => {
      // 处理 HTML 实体
      text = text.replace(/&lt;/g, '<');
      text = text.replace(/&gt;/g, '>');
      text = text.replace(/&amp;/g, '&');
      text = text.replace(/&quot;/g, '"');
      text = text.replace(/&#39;/g, "'");
      return text;
    }
  },
  {
    name: '标准化变量名',
    category: 'variables',
    description: '标准化 Liquid 变量名的格式',
    apply: (text) => {
      // 确保变量名符合标准格式
      text = text.replace(/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\.\s*([a-zA-Z_][a-zA-Z0-9_]*)/g, '$1.$2');
      return text;
    }
  },
  {
    name: '预处理注释',
    category: 'comments',
    description: '预处理 Liquid 注释，确保格式正确',
    apply: (text) => {
      // 标准化注释格式
      text = text.replace(/{%\s*comment\s*%}/g, '{% comment %}');
      text = text.replace(/{%\s*endcomment\s*%}/g, '{% endcomment %}');
      return text;
    }
  },
  {
    name: '预处理循环变量',
    category: 'loops',
    description: '预处理 Liquid 循环变量的引用',
    apply: (text) => {
      // 标准化 forloop 变量
      text = text.replace(/\bforloop\s*\.\s*(\w+)/g, 'forloop.$1');
      text = text.replace(/\btablerowloop\s*\.\s*(\w+)/g, 'tablerowloop.$1');
      return text;
    }
  },
  {
    name: '预处理条件表达式',
    category: 'conditions',
    description: '预处理 Liquid 条件表达式的格式',
    apply: (text) => {
      // 只在特定的条件标签内标准化运算符，避免影响HTML内容
      // 处理 if 标签中的条件
      text = text.replace(/{%\s*if\s+([^%]+?)%}/g, (match, condition) => {
        let processed = condition.trim();
        processed = processed.replace(/\s+and\s+/g, ' and ');
        processed = processed.replace(/\s+or\s+/g, ' or ');
        processed = processed.replace(/\s+contains\s+/g, ' contains ');
        processed = processed.replace(/\s*(==|!=|>=|<=|>|<)\s*/g, ' $1 ');
        return `{% if ${processed} %}`;
      });

      // 处理 elsif 标签中的条件
      text = text.replace(/{%\s*elsif\s+([^%]+?)%}/g, (match, condition) => {
        let processed = condition.trim();
        processed = processed.replace(/\s+and\s+/g, ' and ');
        processed = processed.replace(/\s+or\s+/g, ' or ');
        processed = processed.replace(/\s+contains\s+/g, ' contains ');
        processed = processed.replace(/\s*(==|!=|>=|<=|>|<)\s*/g, ' $1 ');
        return `{% elsif ${processed} %}`;
      });

      // 处理 unless 标签中的条件
      text = text.replace(/{%\s*unless\s+([^%]+?)%}/g, (match, condition) => {
        let processed = condition.trim();
        processed = processed.replace(/\s+and\s+/g, ' and ');
        processed = processed.replace(/\s+or\s+/g, ' or ');
        processed = processed.replace(/\s+contains\s+/g, ' contains ');
        processed = processed.replace(/\s*(==|!=|>=|<=|>|<)\s*/g, ' $1 ');
        return `{% unless ${processed} %}`;
      });

      return text;
    }
  },
  {
    name: '预处理数组访问',
    category: 'arrays',
    description: '预处理 Liquid 数组访问语法',
    apply: (text) => {
      // 标准化数组索引访问
      text = text.replace(/\[\s*(\d+)\s*\]/g, '[$1]');
      text = text.replace(/\[\s*"([^"]+)"\s*\]/g, '["$1"]');
      text = text.replace(/\[\s*'([^']+)'\s*\]/g, '["$1"]');
      return text;
    }
  },
  {
    name: '预处理范围表达式',
    category: 'ranges',
    description: '预处理 Liquid 范围表达式',
    apply: (text) => {
      // 标准化范围表达式
      text = text.replace(/\(\s*(\d+|[\w.]+)\s*\.\.\s*(\d+|[\w.]+)\s*\)/g, '($1..$2)');
      return text;
    }
  },
  {
    name: '预处理 assign 语句',
    category: 'assignment',
    description: '预处理 Liquid assign 语句的格式',
    apply: (text) => {
      // 标准化 assign 语句
      text = text.replace(/{%\s*assign\s+(\w+)\s*=\s*/g, '{% assign $1 = ');
      return text;
    }
  },
  {
    name: '预处理包含语句',
    category: 'includes',
    description: '预处理 Liquid include 和 render 语句',
    apply: (text) => {
      // 标准化 include 和 render 语句
      text = text.replace(/{%\s*(include|render)\s+/g, '{% $1 ');
      return text;
    }
  },
  {
    name: '预处理分页标签',
    category: 'pagination',
    description: '预处理 Liquid 分页相关标签',
    apply: (text) => {
      // 标准化分页标签
      text = text.replace(/{%\s*paginate\s+/g, '{% paginate ');
      text = text.replace(/{%\s*endpaginate\s*%}/g, '{% endpaginate %}');
      return text;
    }
  }
];

// ================== 预处理函数 ==================

/**
 * 应用所有预处理规则
 * @param {string} text - 要预处理的 Liquid 文本
 * @param {Object} options - 预处理选项
 * @returns {string} 预处理后的文本
 */
function applyLiquidPreprocessRules(text, options = {}) {
  const {
    skipCategories = [],
    onlyCategories = null,
    logLevel = 'error'
  } = options;
  
  let result = text;
  let appliedRules = 0;
  
  try {
    LIQUID_PREPROCESS_RULES.forEach(rule => {
      // 检查是否应该跳过此规则
      if (skipCategories.includes(rule.category)) {
        return;
      }
      
      // 检查是否只处理特定分类
      if (onlyCategories && !onlyCategories.includes(rule.category)) {
        return;
      }
      
      const beforeText = result;
      result = rule.apply(result);
      
      if (result !== beforeText) {
        appliedRules++;
        if (logLevel === 'info') {
          console.log(`[PREPROCESS] 应用规则: ${rule.name}`);
        }
      }
    });
    
    if (logLevel === 'info') {
      console.log(`[PREPROCESS] 完成，应用了 ${appliedRules} 个预处理规则`);
    }
    
    return result;
    
  } catch (error) {
    console.error('[PREPROCESS] 预处理过程中出错:', error);
    return text; // 出错时返回原始文本
  }
}

/**
 * 获取预处理统计信息
 * @returns {Object} 统计信息
 */
function getLiquidPreprocessStats() {
  const categoryCounts = {};
  
  LIQUID_PREPROCESS_RULES.forEach(rule => {
    categoryCounts[rule.category] = (categoryCounts[rule.category] || 0) + 1;
  });
  
  return {
    totalRules: LIQUID_PREPROCESS_RULES.length,
    categories: Object.keys(categoryCounts).length,
    rulesByCategory: categoryCounts,
    availableCategories: Object.keys(categoryCounts)
  };
}

/**
 * 验证预处理结果
 * @param {string} originalText - 原始文本
 * @param {string} processedText - 处理后的文本
 * @returns {Object} 验证结果
 */
function validateLiquidPreprocessResult(originalText, processedText) {
  const issues = [];
  
  // 检查标签是否平衡
  const originalTags = (originalText.match(/{%[^%]*%}/g) || []).length;
  const processedTags = (processedText.match(/{%[^%]*%}/g) || []).length;
  
  if (originalTags !== processedTags) {
    issues.push(`标签数量不匹配: 原始 ${originalTags}, 处理后 ${processedTags}`);
  }
  
  // 检查变量输出是否平衡
  const originalOutputs = (originalText.match(/{{[^}]*}}/g) || []).length;
  const processedOutputs = (processedText.match(/{{[^}]*}}/g) || []).length;
  
  if (originalOutputs !== processedOutputs) {
    issues.push(`变量输出数量不匹配: 原始 ${originalOutputs}, 处理后 ${processedOutputs}`);
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    originalLength: originalText.length,
    processedLength: processedText.length,
    changeRatio: (processedText.length - originalText.length) / originalText.length
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_PREPROCESS_RULES,
  applyLiquidPreprocessRules,
  getLiquidPreprocessStats,
  validateLiquidPreprocessResult
};
