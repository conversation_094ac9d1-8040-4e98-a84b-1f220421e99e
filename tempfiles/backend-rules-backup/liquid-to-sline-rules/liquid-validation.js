/**
 * Liquid 转换验证工具
 * 提供转换质量验证和统计分析功能
 */

// ================== 转换验证 ==================

/**
 * 验证 Liquid 到 Sline 的转换结果
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @param {Object} options - 验证选项
 * @returns {Object} 验证结果
 */
function validateLiquidConversion(originalLiquid, convertedSline, options = {}) {
  const {
    strictMode = false,
    checkSyntax = true,
    checkSemantics = true,
    checkPerformance = false
  } = options;
  
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
    score: 100,
    details: {}
  };
  
  try {
    // 语法验证
    if (checkSyntax) {
      const syntaxResult = validateLiquidSyntax(originalLiquid, convertedSline);
      validation.details.syntax = syntaxResult;
      
      if (!syntaxResult.isValid) {
        validation.isValid = false;
        validation.errors.push(...syntaxResult.errors);
        validation.score -= syntaxResult.errors.length * 10;
      }
      
      validation.warnings.push(...syntaxResult.warnings);
      validation.score -= syntaxResult.warnings.length * 2;
    }
    
    // 语义验证
    if (checkSemantics) {
      const semanticResult = validateLiquidSemantics(originalLiquid, convertedSline);
      validation.details.semantics = semanticResult;
      
      if (!semanticResult.isValid) {
        validation.isValid = false;
        validation.errors.push(...semanticResult.errors);
        validation.score -= semanticResult.errors.length * 15;
      }
      
      validation.warnings.push(...semanticResult.warnings);
      validation.score -= semanticResult.warnings.length * 3;
    }
    
    // 性能验证
    if (checkPerformance) {
      const performanceResult = validateLiquidPerformance(originalLiquid, convertedSline);
      validation.details.performance = performanceResult;
      validation.suggestions.push(...performanceResult.suggestions);
    }
    
    // 确保分数不低于0
    validation.score = Math.max(0, validation.score);
    
    return validation;
    
  } catch (error) {
    validation.isValid = false;
    validation.errors.push(`验证过程中出错: ${error.message}`);
    validation.score = 0;
    return validation;
  }
}

/**
 * 验证语法正确性
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 语法验证结果
 */
function validateLiquidSyntax(originalLiquid, convertedSline) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    tagBalance: true,
    quoteBalance: true
  };
  
  // 检查标签平衡
  const tagBalanceResult = checkLiquidTagBalance(convertedSline);
  if (!tagBalanceResult.isBalanced) {
    result.isValid = false;
    result.tagBalance = false;
    result.errors.push(...tagBalanceResult.errors);
  }
  
  // 检查引号平衡
  const quoteBalanceResult = checkLiquidQuoteBalance(convertedSline);
  if (!quoteBalanceResult.isBalanced) {
    result.isValid = false;
    result.quoteBalance = false;
    result.errors.push(...quoteBalanceResult.errors);
  }
  
  // 检查 Sline 语法规范
  const slineSyntaxResult = checkSlineSyntaxCompliance(convertedSline);
  if (!slineSyntaxResult.isCompliant) {
    result.warnings.push(...slineSyntaxResult.warnings);
  }
  
  return result;
}

/**
 * 验证语义正确性
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 语义验证结果
 */
function validateLiquidSemantics(originalLiquid, convertedSline) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    variableConsistency: true,
    logicConsistency: true
  };
  
  // 检查变量一致性
  const variableResult = checkLiquidVariableConsistency(originalLiquid, convertedSline);
  if (!variableResult.isConsistent) {
    result.variableConsistency = false;
    result.warnings.push(...variableResult.warnings);
  }
  
  // 检查逻辑一致性
  const logicResult = checkLiquidLogicConsistency(originalLiquid, convertedSline);
  if (!logicResult.isConsistent) {
    result.logicConsistency = false;
    result.errors.push(...logicResult.errors);
    result.isValid = false;
  }
  
  return result;
}

/**
 * 验证性能影响
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 性能验证结果
 */
function validateLiquidPerformance(originalLiquid, convertedSline) {
  const result = {
    suggestions: [],
    complexity: 'low',
    optimizations: []
  };
  
  // 分析复杂度
  const complexityResult = analyzeLiquidCodeComplexity(convertedSline);
  result.complexity = complexityResult.level;
  
  if (complexityResult.level === 'high') {
    result.suggestions.push('代码复杂度较高，建议考虑重构');
  }
  
  // 检查性能优化机会
  const optimizations = findLiquidOptimizations(convertedSline);
  result.optimizations = optimizations;
  result.suggestions.push(...optimizations.map(opt => opt.suggestion));
  
  return result;
}

// ================== 辅助验证函数 ==================

/**
 * 检查标签平衡
 * @param {string} code - 代码
 * @returns {Object} 标签平衡检查结果
 */
function checkLiquidTagBalance(code) {
  const result = {
    isBalanced: true,
    errors: [],
    openTags: []
  };
  
  const tagPattern = /{{(#|\/)?(\w+)(?:\s[^}]*)?}}/g;
  const stack = [];
  let match;
  
  while ((match = tagPattern.exec(code)) !== null) {
    const [fullMatch, prefix, tagName] = match;
    const position = match.index;
    
    if (prefix === '#') {
      // 开始标签
      if (!fullMatch.includes(' /}}')) {
        stack.push({ tag: tagName, position, match: fullMatch });
      }
    } else if (prefix === '/') {
      // 结束标签
      const lastOpen = stack.pop();
      if (!lastOpen) {
        result.isBalanced = false;
        result.errors.push(`多余的结束标签 ${tagName} 在位置 ${position}`);
      } else if (lastOpen.tag !== tagName) {
        result.isBalanced = false;
        result.errors.push(`标签不匹配: 期望 ${lastOpen.tag}, 实际 ${tagName} 在位置 ${position}`);
      }
    }
  }
  
  // 检查未闭合的标签
  stack.forEach(openTag => {
    result.isBalanced = false;
    result.errors.push(`未闭合的标签 ${openTag.tag} 在位置 ${openTag.position}`);
  });
  
  result.openTags = stack;
  return result;
}

/**
 * 检查引号平衡
 * @param {string} code - 代码
 * @returns {Object} 引号平衡检查结果
 */
function checkLiquidQuoteBalance(code) {
  const result = {
    isBalanced: true,
    errors: []
  };
  
  // 检查双引号
  const doubleQuotes = (code.match(/"/g) || []).length;
  if (doubleQuotes % 2 !== 0) {
    result.isBalanced = false;
    result.errors.push('双引号不平衡');
  }
  
  // 检查单引号
  const singleQuotes = (code.match(/'/g) || []).length;
  if (singleQuotes % 2 !== 0) {
    result.isBalanced = false;
    result.errors.push('单引号不平衡');
  }
  
  return result;
}

/**
 * 检查 Sline 语法规范
 * @param {string} code - Sline 代码
 * @returns {Object} 语法规范检查结果
 */
function checkSlineSyntaxCompliance(code) {
  const result = {
    isCompliant: true,
    warnings: []
  };
  
  // 检查自闭合标签格式
  const selfClosingPattern = /{{#\w+[^}]*(?<!\s)\/}}/g;
  const selfClosingMatches = code.match(selfClosingPattern);
  if (selfClosingMatches) {
    selfClosingMatches.forEach(match => {
      if (!match.includes(' /}}')) {
        result.warnings.push(`自闭合标签格式不规范: ${match}`);
      }
    });
  }
  
  // 检查过滤器格式
  const filterPattern = /\|(\w+)(?!\()/g;
  const filterMatches = code.match(filterPattern);
  if (filterMatches) {
    filterMatches.forEach(match => {
      result.warnings.push(`过滤器缺少括号: ${match}`);
    });
  }
  
  return result;
}

/**
 * 检查变量一致性
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 变量一致性检查结果
 */
function checkLiquidVariableConsistency(originalLiquid, convertedSline) {
  const result = {
    isConsistent: true,
    warnings: []
  };
  
  // 提取原始变量
  const originalVars = extractLiquidVariables(originalLiquid);
  const convertedVars = extractLiquidVariables(convertedSline);
  
  // 检查变量数量
  if (originalVars.length !== convertedVars.length) {
    result.warnings.push(`变量数量不一致: 原始 ${originalVars.length}, 转换后 ${convertedVars.length}`);
  }
  
  return result;
}

/**
 * 检查逻辑一致性
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 逻辑一致性检查结果
 */
function checkLiquidLogicConsistency(originalLiquid, convertedSline) {
  const result = {
    isConsistent: true,
    errors: []
  };
  
  // 检查条件语句数量
  const originalConditions = (originalLiquid.match(/{%\s*if\s/g) || []).length;
  const convertedConditions = (convertedSline.match(/{{#if\s/g) || []).length;
  
  if (originalConditions !== convertedConditions) {
    result.isConsistent = false;
    result.errors.push(`条件语句数量不一致: 原始 ${originalConditions}, 转换后 ${convertedConditions}`);
  }
  
  return result;
}

// ================== 统计分析 ==================

/**
 * 获取转换统计信息
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @returns {Object} 统计信息
 */
function getLiquidConversionStats(originalLiquid, convertedSline) {
  return {
    original: {
      length: originalLiquid.length,
      lines: originalLiquid.split('\n').length,
      tags: (originalLiquid.match(/{%[^%]*%}/g) || []).length,
      outputs: (originalLiquid.match(/{{[^}]*}}/g) || []).length
    },
    converted: {
      length: convertedSline.length,
      lines: convertedSline.split('\n').length,
      tags: (convertedSline.match(/{{#[^}]*}}/g) || []).length,
      outputs: (convertedSline.match(/{{[^#/][^}]*}}/g) || []).length
    },
    changes: {
      lengthDiff: convertedSline.length - originalLiquid.length,
      lengthRatio: convertedSline.length / originalLiquid.length
    }
  };
}

/**
 * 分析代码复杂度
 * @param {string} code - 代码
 * @returns {Object} 复杂度分析结果
 */
function analyzeLiquidCodeComplexity(code) {
  let score = 0;
  const factors = [];
  
  // 嵌套深度
  const maxNesting = calculateLiquidNestingDepth(code);
  score += maxNesting * 2;
  factors.push(`最大嵌套深度: ${maxNesting}`);
  
  // 条件语句数量
  const conditions = (code.match(/{{#if|{{#unless|{{#case/g) || []).length;
  score += conditions;
  factors.push(`条件语句: ${conditions}`);
  
  // 循环数量
  const loops = (code.match(/{{#for|{{#tablerow/g) || []).length;
  score += loops * 2;
  factors.push(`循环: ${loops}`);
  
  // 过滤器数量
  const filters = (code.match(/\|/g) || []).length;
  score += Math.floor(filters / 2);
  factors.push(`过滤器: ${filters}`);
  
  let level = 'low';
  if (score > 20) {
    level = 'high';
  } else if (score > 10) {
    level = 'medium';
  }
  
  return {
    score,
    level,
    factors,
    maxNesting
  };
}

/**
 * 计算嵌套深度
 * @param {string} code - 代码
 * @returns {number} 最大嵌套深度
 */
function calculateLiquidNestingDepth(code) {
  let maxDepth = 0;
  let currentDepth = 0;
  
  const tagPattern = /{{(#|\/)?(\w+)/g;
  let match;
  
  while ((match = tagPattern.exec(code)) !== null) {
    const [, prefix] = match;
    
    if (prefix === '#') {
      currentDepth++;
      maxDepth = Math.max(maxDepth, currentDepth);
    } else if (prefix === '/') {
      currentDepth = Math.max(0, currentDepth - 1);
    }
  }
  
  return maxDepth;
}

/**
 * 提取变量
 * @param {string} code - 代码
 * @returns {Array} 变量数组
 */
function extractLiquidVariables(code) {
  const variables = [];
  const varPattern = /{{([^#/][^}]*)}}/g;
  let match;
  
  while ((match = varPattern.exec(code)) !== null) {
    const content = match[1].trim();
    if (!content.startsWith('!--')) {
      variables.push(content);
    }
  }
  
  return variables;
}

/**
 * 查找优化机会
 * @param {string} code - 代码
 * @returns {Array} 优化建议数组
 */
function findLiquidOptimizations(code) {
  const optimizations = [];
  
  // 检查重复的过滤器链
  const filterChains = code.match(/{{[^}]*\|[^}]*}}/g) || [];
  const chainCounts = {};
  
  filterChains.forEach(chain => {
    chainCounts[chain] = (chainCounts[chain] || 0) + 1;
  });
  
  Object.entries(chainCounts).forEach(([chain, count]) => {
    if (count > 2) {
      optimizations.push({
        type: 'duplicate_filter_chain',
        suggestion: `考虑将重复的过滤器链 "${chain}" 提取为变量`,
        occurrences: count
      });
    }
  });
  
  return optimizations;
}

/**
 * 检查语法有效性
 * @param {string} code - 代码
 * @returns {boolean} 是否有效
 */
function checkLiquidSyntaxValidity(code) {
  try {
    const tagBalance = checkLiquidTagBalance(code);
    const quoteBalance = checkLiquidQuoteBalance(code);
    
    return tagBalance.isBalanced && quoteBalance.isBalanced;
  } catch (error) {
    return false;
  }
}

/**
 * 生成转换报告
 * @param {string} originalLiquid - 原始 Liquid 代码
 * @param {string} convertedSline - 转换后的 Sline 代码
 * @param {Object} validationResult - 验证结果
 * @returns {string} Markdown 格式的报告
 */
function generateLiquidConversionReport(originalLiquid, convertedSline, validationResult) {
  const stats = getLiquidConversionStats(originalLiquid, convertedSline);
  const complexity = analyzeLiquidCodeComplexity(convertedSline);
  
  const report = [
    '# Liquid 到 Sline 转换报告',
    '',
    '## 转换统计',
    `- 原始代码长度: ${stats.original.length} 字符`,
    `- 转换后长度: ${stats.converted.length} 字符`,
    `- 长度变化: ${stats.changes.lengthDiff > 0 ? '+' : ''}${stats.changes.lengthDiff} 字符`,
    `- 原始标签数: ${stats.original.tags}`,
    `- 转换后标签数: ${stats.converted.tags}`,
    '',
    '## 验证结果',
    `- 验证状态: ${validationResult.isValid ? '✅ 通过' : '❌ 失败'}`,
    `- 质量评分: ${validationResult.score}/100`,
    `- 错误数量: ${validationResult.errors.length}`,
    `- 警告数量: ${validationResult.warnings.length}`,
    '',
    '## 复杂度分析',
    `- 复杂度级别: ${complexity.level}`,
    `- 复杂度评分: ${complexity.score}`,
    `- 最大嵌套深度: ${complexity.maxNesting}`,
    ''
  ];
  
  if (validationResult.errors.length > 0) {
    report.push('## 错误详情');
    validationResult.errors.forEach((error, index) => {
      report.push(`${index + 1}. ${error}`);
    });
    report.push('');
  }
  
  if (validationResult.warnings.length > 0) {
    report.push('## 警告详情');
    validationResult.warnings.forEach((warning, index) => {
      report.push(`${index + 1}. ${warning}`);
    });
    report.push('');
  }
  
  return report.join('\n');
}

// ================== 导出 ==================
module.exports = {
  validateLiquidConversion,
  getLiquidConversionStats,
  analyzeLiquidCodeComplexity,
  checkLiquidSyntaxValidity,
  generateLiquidConversionReport,
  checkLiquidTagBalance,
  checkLiquidQuoteBalance,
  calculateLiquidNestingDepth,
  extractLiquidVariables,
  findLiquidOptimizations
};
