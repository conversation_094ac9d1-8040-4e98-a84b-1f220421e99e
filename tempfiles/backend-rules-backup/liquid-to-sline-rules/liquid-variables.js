/**
 * Liquid 变量和运算符映射表
 * 定义 Liquid 变量和运算符到 Sline 的映射关系
 */

// ================== Liquid 循环变量映射表 ==================
const LIQUID_VARIABLE_MAPPINGS = {
  // forloop 变量映射
  'forloop.index': '@index',
  'forloop.index0': '@index0',
  'forloop.rindex': '@rindex',
  'forloop.rindex0': '@rindex0',
  'forloop.first': '@first',
  'forloop.last': '@last',
  'forloop.length': '@length',
  
  // tablerowloop 变量映射
  'tablerowloop.index': '@index',
  'tablerowloop.index0': '@index0',
  'tablerowloop.rindex': '@rindex',
  'tablerowloop.rindex0': '@rindex0',
  'tablerowloop.first': '@first',
  'tablerowloop.last': '@last',
  'tablerowloop.length': '@length',
  'tablerowloop.col': '@col',
  'tablerowloop.col0': '@col0',
  'tablerowloop.col_first': '@col_first',
  'tablerowloop.col_last': '@col_last',
  'tablerowloop.row': '@row',
  
  // paginate 变量映射
  'paginate.current_page': 'paginate.current_page',
  'paginate.current_offset': 'paginate.current_offset',
  'paginate.items': 'paginate.items',
  'paginate.parts': 'paginate.parts',
  'paginate.pages': 'paginate.pages',
  'paginate.previous': 'paginate.previous',
  'paginate.next': 'paginate.next',
  
  // 特殊变量
  'blank': 'null',
  'empty': 'empty',
  'nil': 'null',
  'null': 'null',
  'true': 'true',
  'false': 'false'
};

// ================== Liquid 运算符映射表 ==================
const LIQUID_OPERATOR_MAPPINGS = {
  // 逻辑运算符
  'and': '&&',
  'or': '||',
  
  // 比较运算符
  '==': '==',
  '!=': '!=',
  '<>': '!=',
  '>': '>',
  '<': '<',
  '>=': '>=',
  '<=': '<=',
  
  // 包含运算符
  'contains': 'contains',
  
  // 赋值运算符
  '=': '='
};

// ================== 特殊操作符映射 ==================
const LIQUID_SPECIAL_OPERATORS = {
  // 大小比较
  'size': 'size()',
  
  // 范围操作
  '..': 'range',
  
  // 属性访问
  '.': '.',
  
  // 数组索引
  '[': '[',
  ']': ']'
};

// ================== 条件表达式转换规则 ==================
const LIQUID_CONDITION_TRANSFORMS = [
  // 大小检查转换
  {
    pattern: /(\w+(?:\.\w+)*?)\.size/g,
    replacement: '$1|size()',
    description: '转换 .size 属性为 size() 过滤器'
  },
  
  // contains 操作转换
  {
    pattern: /(\w+(?:\.\w+)*)\s+contains\s+['"]?([\w\.-]+)['"]?/g,
    replacement: '$1|contains("$2")',
    description: '转换 contains 操作为过滤器'
  },
  
  // 逻辑运算符转换
  {
    pattern: /\s+or\s+/g,
    replacement: ' || ',
    description: '转换 or 为 ||'
  },
  {
    pattern: /\s+and\s+/g,
    replacement: ' && ',
    description: '转换 and 为 &&'
  },
  
  // 比较运算符标准化
  {
    pattern: /\s*(==|!=|>=|<=|>|<)\s*/g,
    replacement: ' $1 ',
    description: '标准化比较运算符空格'
  },
  
  // 引号标准化
  {
    pattern: /'([^']*)'/g,
    replacement: '"$1"',
    description: '将单引号转换为双引号'
  }
];

// ================== 辅助函数 ==================

/**
 * 根据名称获取变量映射
 * @param {string} variableName - 变量名称
 * @returns {string|null} 映射后的变量名称或 null
 */
function getLiquidVariableByName(variableName) {
  return LIQUID_VARIABLE_MAPPINGS[variableName] || null;
}

/**
 * 根据名称获取运算符映射
 * @param {string} operatorName - 运算符名称
 * @returns {string|null} 映射后的运算符或 null
 */
function getLiquidOperatorByName(operatorName) {
  return LIQUID_OPERATOR_MAPPINGS[operatorName] || null;
}

/**
 * 检查是否为循环变量
 * @param {string} variableName - 变量名称
 * @returns {boolean} 是否为循环变量
 */
function isLiquidLoopVariable(variableName) {
  return variableName.startsWith('forloop.') || 
         variableName.startsWith('tablerowloop.') ||
         variableName.startsWith('@');
}

/**
 * 检查是否为可转换的运算符
 * @param {string} operator - 运算符
 * @returns {boolean} 是否可转换
 */
function isLiquidConvertibleOperator(operator) {
  return Object.prototype.hasOwnProperty.call(LIQUID_OPERATOR_MAPPINGS, operator);
}

/**
 * 转换条件表达式
 * @param {string} condition - 原始条件表达式
 * @returns {string} 转换后的条件表达式
 */
function convertLiquidCondition(condition) {
  let result = condition;
  
  LIQUID_CONDITION_TRANSFORMS.forEach(transform => {
    result = result.replace(transform.pattern, transform.replacement);
  });
  
  return result;
}

/**
 * 判断条件是否为单个变量
 * @param {string} condition - 条件表达式
 * @returns {boolean} 是否为单个变量
 */
function isLiquidSingleVariable(condition) {
  return /^\w+(?:\.\w+)*$/.test(condition.trim());
}

/**
 * 否定条件表达式（用于 unless 转换）
 * @param {string} condition - 原始条件
 * @returns {string} 否定后的条件
 */
function negateLiquidCondition(condition) {
  const cleanCondition = condition.trim();
  
  if (isLiquidSingleVariable(cleanCondition)) {
    return `!${cleanCondition}`;
  }
  
  const convertedCondition = convertLiquidCondition(cleanCondition);
  return `!(${convertedCondition})`;
}

/**
 * 获取变量统计信息
 * @returns {Object} 统计信息
 */
function getLiquidVariableStats() {
  return {
    totalVariables: Object.keys(LIQUID_VARIABLE_MAPPINGS).length,
    totalOperators: Object.keys(LIQUID_OPERATOR_MAPPINGS).length,
    totalSpecialOperators: Object.keys(LIQUID_SPECIAL_OPERATORS).length,
    totalTransforms: LIQUID_CONDITION_TRANSFORMS.length,
    variablesByType: {
      forloop: Object.keys(LIQUID_VARIABLE_MAPPINGS).filter(key => key.startsWith('forloop.')).length,
      tablerowloop: Object.keys(LIQUID_VARIABLE_MAPPINGS).filter(key => key.startsWith('tablerowloop.')).length,
      paginate: Object.keys(LIQUID_VARIABLE_MAPPINGS).filter(key => key.startsWith('paginate.')).length,
      special: Object.keys(LIQUID_VARIABLE_MAPPINGS).filter(key => !key.includes('.')).length
    }
  };
}

// ================== 导出 ==================
module.exports = {
  LIQUID_VARIABLE_MAPPINGS,
  LIQUID_OPERATOR_MAPPINGS,
  LIQUID_SPECIAL_OPERATORS,
  LIQUID_CONDITION_TRANSFORMS,
  getLiquidVariableByName,
  getLiquidOperatorByName,
  isLiquidLoopVariable,
  isLiquidConvertibleOperator,
  convertLiquidCondition,
  isLiquidSingleVariable,
  negateLiquidCondition,
  getLiquidVariableStats
};
