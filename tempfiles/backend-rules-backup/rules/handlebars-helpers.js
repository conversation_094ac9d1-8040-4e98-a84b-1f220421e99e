/**
 * 转换辅助函数
 * 基于 liquid-to-sline-rules 的实现
 */

/**
 * 处理空白符控制，基于 liquid-to-sline-rules 的实现
 * @param {string} originalTag - 原始标签
 * @param {string} content - 转换后的内容
 * @returns {string} 处理后的标签
 */
function handleWhitespace(originalTag, content) {
  const hasStartTilde = originalTag.includes('{%-');
  const hasEndTilde = originalTag.includes('-%}');
  
  let prefix = '{{';
  let suffix = '}}';
  
  if (hasStartTilde) prefix = '{{~';
  if (hasEndTilde) suffix = '~}}';
  
  return `${prefix}${content}${suffix}`;
}

/**
 * 获取字符串中指定位置的行号
 * @param {string} text - 文本内容
 * @param {number} position - 位置
 * @returns {number} 行号
 */
function getLineNumber(text, position) {
  const lines = text.slice(0, position).split('\n');
  return lines.length;
}

/**
 * 提取标签中的参数
 * @param {string} tagContent - 标签内容
 * @returns {Object} 解析后的参数
 */
function parseTagParameters(tagContent) {
  const params = {};
  const paramPattern = /(\w+)=(['"]?)([^'"\s]+)\2/g;
  let match;
  
  while ((match = paramPattern.exec(tagContent)) !== null) {
    params[match[1]] = match[3];
  }
  
  return params;
}

/**
 * 检查是否为有效的变量名
 * @param {string} name - 变量名
 * @returns {boolean} 是否有效
 */
function isValidVariableName(name) {
  return /^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(name);
}

/**
 * 提取引号内的字符串
 * @param {string} text - 文本
 * @returns {Array} 字符串数组
 */
function extractQuotedStrings(text) {
  const singleQuoteMatches = text.match(/'([^']+)'/g) || [];
  const doubleQuoteMatches = text.match(/"([^"]+)"/g) || [];
  
  return [
    ...singleQuoteMatches.map(str => str.slice(1, -1)),
    ...doubleQuoteMatches.map(str => str.slice(1, -1))
  ];
}

/**
 * 检查文件扩展名
 * @param {string} filepath - 文件路径
 * @param {Array} extensions - 允许的扩展名数组
 * @returns {boolean} 是否匹配
 */
function hasValidExtension(filepath, extensions) {
  const ext = filepath.split('.').pop()?.toLowerCase();
  return extensions.includes(ext);
}

/**
 * 清理标签内容（移除多余空格）
 * @param {string} content - 标签内容
 * @returns {string} 清理后的内容
 */
function cleanTagContent(content) {
  return content.trim().replace(/\s+/g, ' ');
}

/**
 * 检查是否为自闭合标签格式
 * @param {string} tag - 标签
 * @returns {boolean} 是否为自闭合标签
 */
function isSelfClosingTag(tag) {
  return tag.includes(' /}}') || tag.endsWith('/}}');
}

/**
 * 转换为自闭合标签格式
 * @param {string} tagName - 标签名
 * @param {string} content - 标签内容
 * @returns {string} 自闭合标签
 */
function toSelfClosingTag(tagName, content = '') {
  const cleanContent = content.trim();
  return cleanContent 
    ? `{{#${tagName} ${cleanContent} /}}`
    : `{{#${tagName} /}}`;
}

/**
 * 检查条件表达式的复杂度
 * @param {string} expression - 表达式
 * @returns {Object} 复杂度信息
 */
function analyzeExpressionComplexity(expression) {
  const hasParentheses = expression.includes('(') && expression.includes(')');
  const hasLogicalOperators = /\b(and|or|not)\b/.test(expression);
  const hasComparisonOperators = /\b(gt|lt|eq|gte|lte|ne)\b/.test(expression);
  const nestingLevel = (expression.match(/\(/g) || []).length;
  
  return {
    hasParentheses,
    hasLogicalOperators,
    hasComparisonOperators,
    nestingLevel,
    isComplex: nestingLevel > 1 || (hasLogicalOperators && hasComparisonOperators)
  };
}

module.exports = {
  handleWhitespace,
  getLineNumber,
  parseTagParameters,
  isValidVariableName,
  extractQuotedStrings,
  hasValidExtension,
  cleanTagContent,
  isSelfClosingTag,
  toSelfClosingTag,
  analyzeExpressionComplexity
};
