/**
 * HBS2Sline 模块化规则系统 - 主入口文件
 * 
 * 这个文件汇总所有转换规则和工具函数，提供统一的接口
 * 
 * 模块结构：
 * - mappings/     - 映射表配置
 * - tags/         - 标签转换规则
 * - processing/   - 预处理和后处理
 * - utils/        - 辅助工具函数
 */

// ================== 导入映射表 ==================
const { 
  OBJECT_MAPPINGS, 
  OBJECT_CATEGORIES, 
  getObjectByName, 
  getObjectStats 
} = require('./mappings/objects');

const { 
  FILTER_MAPPINGS, 
  FILTER_CATEGORIES, 
  getFilterByName, 
  getFilterStats, 
  getFiltersByCategory 
} = require('./mappings/filters');

const { 
  LOOP_VARIABLE_MAPPINGS, 
  OPERATOR_MAPPINGS, 
  getVariableByName, 
  getOperatorByName, 
  getVariableStats, 
  isLoopVariable, 
  isConvertibleOperator 
} = require('./mappings/variables');

// ================== 导入标签转换规则 ==================
const { 
  SHOPLINE_TAG_RULES, 
  getShoplineTagStats, 
  getShoplineRuleByName 
} = require('./tags/shopline');

const { 
  CONDITIONAL_TAG_RULES, 
  getConditionalTagStats, 
  getConditionalRuleByName 
} = require('./tags/conditional');

const { 
  ITERATION_TAG_RULES, 
  getIterationTagStats, 
  getIterationRuleByName, 
  getLoopVariableRules 
} = require('./tags/iteration');

const { 
  CONTEXT_TAG_RULES, 
  getContextTagStats 
} = require('./tags/context');

const { 
  OUTPUT_TAG_RULES, 
  getOutputTagStats 
} = require('./tags/output');

const { 
  PARTIAL_TAG_RULES, 
  getPartialTagStats 
} = require('./tags/partial');

const { 
  FILTER_TAG_RULES, 
  getFilterTagStats, 
  getKnownSingleParamFilters, 
  getKnownMultiParamFilters 
} = require('./tags/filter');

const { 
  OPERATOR_TAG_RULES, 
  getOperatorTagStats, 
  getSupportedComparisonOperators, 
  getSupportedLogicalOperators 
} = require('./tags/operator');

// ================== 导入处理规则 ==================
const { 
  PREPROCESS_RULES, 
  applyPreprocessRules, 
  getPreprocessStats 
} = require('./processing/preprocess');

const { 
  POSTPROCESS_RULES, 
  applyPostprocessRules, 
  getPostprocessStats, 
  cleanupConversionResult 
} = require('./processing/postprocess');

// ================== 导入辅助工具 ==================
const { 
  handleWhitespace, 
  getLineNumber, 
  parseTagParameters, 
  isValidVariableName, 
  extractQuotedStrings, 
  hasValidExtension, 
  cleanTagContent, 
  isSelfClosingTag, 
  toSelfClosingTag, 
  analyzeExpressionComplexity 
} = require('./utils/helpers');

const { 
  validateConversion, 
  getConversionStats, 
  analyzeCodeComplexity, 
  checkSyntaxValidity, 
  generateConversionReport, 
  isSelfClosingBlock 
} = require('./utils/validation');

// ================== 组合所有规则 ==================
const ALL_TAG_RULES = [
  ...SHOPLINE_TAG_RULES,
  ...CONDITIONAL_TAG_RULES,
  ...ITERATION_TAG_RULES,
  ...CONTEXT_TAG_RULES,
  ...OUTPUT_TAG_RULES,
  ...PARTIAL_TAG_RULES,
  ...FILTER_TAG_RULES,
  ...OPERATOR_TAG_RULES
];

// ================== 高级接口函数 ==================

/**
 * 根据分类获取规则
 * @param {string} category - 规则分类
 * @returns {Array} 规则数组
 */
function getRulesByCategory(category) {
  switch(category.toLowerCase()) {
    case 'shopline':
      return SHOPLINE_TAG_RULES;
    case 'conditional':
      return CONDITIONAL_TAG_RULES;
    case 'iteration':
      return ITERATION_TAG_RULES;
    case 'context':
      return CONTEXT_TAG_RULES;
    case 'output':
      return OUTPUT_TAG_RULES;
    case 'partial':
      return PARTIAL_TAG_RULES;
    case 'filter':
      return FILTER_TAG_RULES;
    case 'operator':
      return OPERATOR_TAG_RULES;
    default:
      return ALL_TAG_RULES.filter(rule => rule.category === category);
  }
}

/**
 * 获取所有可用的分类
 * @returns {Array} 分类数组
 */
function getAllCategories() {
  return [
    { name: 'shopline', description: 'Shopline 平台特有标签', count: SHOPLINE_TAG_RULES.length },
    { name: 'conditional', description: '条件判断标签', count: CONDITIONAL_TAG_RULES.length },
    { name: 'iteration', description: '循环迭代标签', count: ITERATION_TAG_RULES.length },
    { name: 'context', description: '上下文处理标签', count: CONTEXT_TAG_RULES.length },
    { name: 'output', description: '输出格式标签', count: OUTPUT_TAG_RULES.length },
    { name: 'partial', description: '部分模板标签', count: PARTIAL_TAG_RULES.length },
    { name: 'filter', description: '过滤器处理标签', count: FILTER_TAG_RULES.length },
    { name: 'operator', description: '运算符转换标签', count: OPERATOR_TAG_RULES.length }
  ];
}

/**
 * 根据名称获取特定规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getRuleByName(ruleName) {
  return ALL_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

/**
 * 获取系统完整统计信息
 * @returns {Object} 详细统计信息
 */
function getStats() {
  return {
    // 规则统计
    totalRules: ALL_TAG_RULES.length,
    rulesByCategory: {
      shopline: SHOPLINE_TAG_RULES.length,
      conditional: CONDITIONAL_TAG_RULES.length,
      iteration: ITERATION_TAG_RULES.length,
      context: CONTEXT_TAG_RULES.length,
      output: OUTPUT_TAG_RULES.length,
      partial: PARTIAL_TAG_RULES.length,
      filter: FILTER_TAG_RULES.length,
      operator: OPERATOR_TAG_RULES.length
    },
    
    // 映射统计
    mappings: {
      objects: Object.keys(OBJECT_MAPPINGS).length,
      filters: Object.keys(FILTER_MAPPINGS).length,
      loopVariables: Object.keys(LOOP_VARIABLE_MAPPINGS).length,
      operators: Object.keys(OPERATOR_MAPPINGS).length
    },
    
    // 处理规则统计
    processing: {
      preprocess: PREPROCESS_RULES.length,
      postprocess: POSTPROCESS_RULES.length
    },
    
    // 分类统计
    categories: {
      objects: OBJECT_CATEGORIES.length,
      filters: FILTER_CATEGORIES.length
    }
  };
}

/**
 * 获取系统信息
 * @returns {Object} 系统信息
 */
function getSystemInfo() {
  const stats = getStats();
  
  return {
    version: '2.0.0',
    architecture: 'modular',
    lastUpdated: new Date().toISOString(),
    totalComponents: 12,
    description: 'HBS2Sline 模块化转换规则系统',
    
    modules: {
      mappings: ['objects', 'filters', 'variables'],
      tags: ['shopline', 'conditional', 'iteration', 'context', 'output', 'partial', 'filter', 'operator'],
      processing: ['preprocess', 'postprocess'],
      utils: ['helpers', 'validation']
    },
    
    stats: stats,
    
    features: [
      '🆕 模块化架构',
      '🔧 可独立测试',
      '📊 详细统计',
      '✅ 质量验证',
      '🚀 易于扩展',
      '📝 完整文档'
    ]
  };
}

// ================== 核心转换功能 ==================
/**
 * 应用转换规则到文本
 * @param {string} text - 要转换的文本
 * @param {Object} options - 转换选项
 * @returns {Object} 转换结果 {text, stats, warnings}
 */
function applyConversionRules(text, options = {}) {
  const {
    logLevel = 'error',
    includeStats = true,
    includeWarnings = true
  } = options;
  
  let output = text;
  const appliedRules = [];
  const warnings = [];
  
  try {
    // 按分类应用规则
    const categories = ['shopline', 'conditional', 'iteration', 'context', 'output', 'partial', 'filter', 'operator'];
    
    for (const category of categories) {
      const rules = getRulesByCategory(category);
      let categoryApplied = 0;
      
      rules.forEach(rule => {
        if (rule.pattern && rule.replacement) {
          const beforeMatch = output.match(rule.pattern);
          if (beforeMatch) {
            output = output.replace(rule.pattern, rule.replacement);
            appliedRules.push({
              category,
              name: rule.name || 'unnamed',
              matches: beforeMatch.length
            });
            categoryApplied++;
          }
        } else if (rule.apply && typeof rule.apply === 'function') {
          const beforeText = output;
          output = rule.apply(output);
          if (output !== beforeText) {
            appliedRules.push({
              category,
              name: rule.name || 'custom function',
              matches: 1
            });
            categoryApplied++;
          }
        }
      });
      
      if (logLevel === 'info' && categoryApplied > 0) {
        console.log(`[INFO] ${category} 分类转换完成，应用了 ${categoryApplied} 个规则`);
      }
    }
    
    const result = {
      text: output,
      success: true
    };
    
    if (includeStats) {
      result.stats = {
        totalRulesApplied: appliedRules.length,
        rulesByCategory: appliedRules.reduce((acc, rule) => {
          acc[rule.category] = (acc[rule.category] || 0) + 1;
          return acc;
        }, {}),
        appliedRules
      };
    }
    
    if (includeWarnings && warnings.length > 0) {
      result.warnings = warnings;
    }
    
    return result;
    
  } catch (error) {
    return {
      text: text,
      success: false,
      error: error.message
    };
  }
}

// ================== 兼容性导出 ==================
// 保持向后兼容性的别名导出
const HELPER_MAPPINGS = FILTER_MAPPINGS;
const BASIC_RULES = CONDITIONAL_TAG_RULES;
const LOOP_RULES = ITERATION_TAG_RULES;
const CONTEXT_RULES = CONTEXT_TAG_RULES;
const PARTIAL_RULES = PARTIAL_TAG_RULES;
const ALL_RULES = ALL_TAG_RULES;

// ================== 导出所有内容 ==================
module.exports = {
  // 主要映射表
  OBJECT_MAPPINGS,
  FILTER_MAPPINGS,
  LOOP_VARIABLE_MAPPINGS,
  OPERATOR_MAPPINGS,
  
  // 分类结构
  FILTER_CATEGORIES,
  OBJECT_CATEGORIES,
  
  // 标签转换规则
  SHOPLINE_TAG_RULES,
  CONDITIONAL_TAG_RULES,
  ITERATION_TAG_RULES,
  CONTEXT_TAG_RULES,
  OUTPUT_TAG_RULES,
  PARTIAL_TAG_RULES,
  FILTER_TAG_RULES,
  OPERATOR_TAG_RULES,
  
  // 组合规则
  ALL_TAG_RULES,
  PREPROCESS_RULES,
  POSTPROCESS_RULES,
  
  // 兼容性导出
  HELPER_MAPPINGS,
  BASIC_RULES,
  LOOP_RULES,
  CONTEXT_RULES,
  PARTIAL_RULES,
  ALL_RULES,
  
  // 高级接口函数
  getRulesByCategory,
  getAllCategories,
  getRuleByName,
  getFilterByName,
  getObjectByName,
  getVariableByName,
  getOperatorByName,
  
  // 统计函数
  getStats,
  getSystemInfo,
  getObjectStats,
  getFilterStats,
  getVariableStats,
  getShoplineTagStats,
  getConditionalTagStats,
  getIterationTagStats,
  getContextTagStats,
  getOutputTagStats,
  getPartialTagStats,
  getFilterTagStats,
  getOperatorTagStats,
  getPreprocessStats,
  getPostprocessStats,
  
  // 处理函数
  applyPreprocessRules,
  applyPostprocessRules,
  
  // 核心转换功能
  applyConversionRules,
  
  // 验证函数
  validateConversion,
  getConversionStats,
  analyzeCodeComplexity,
  checkSyntaxValidity,
  generateConversionReport,
  
  // 辅助函数
  handleWhitespace,
  getLineNumber,
  parseTagParameters,
  isValidVariableName,
  extractQuotedStrings,
  hasValidExtension,
  cleanTagContent,
  isSelfClosingTag,
  toSelfClosingTag,
  analyzeExpressionComplexity,
  isSelfClosingBlock,
  isLoopVariable,
  isConvertibleOperator,
  
  // 专用函数
  getFiltersByCategory,
  getShoplineRuleByName,
  getConditionalRuleByName,
  getIterationRuleByName,
  getLoopVariableRules,
  getKnownSingleParamFilters,
  getKnownMultiParamFilters,
  getSupportedComparisonOperators,
  getSupportedLogicalOperators
};
