/**
 * 循环标签转换规则
 * 处理 each 循环、循环变量等相关转换
 */

// ================== 循环标签转换规则 ==================
const ITERATION_TAG_RULES = [
  {
    name: 'each 循环转换 - 带别名和索引',
    category: 'iteration',
    description: '转换 each 循环带别名和索引',
    pattern: /\{\{\s*#each\s+([^}]+?)\s+as\s+\|\s*(\w+)\s+(\w+)\s*\|\s*\}\}/g,
    replacement: '{{#for $2 in $1}}',
    examples: {
      handlebars: '{{#each products as |product index|}}',
      sline: '{{#for product in products}}'
    }
  },
  {
    name: 'each 循环转换 - 带别名',
    category: 'iteration',
    description: '转换 each 循环带别名',
    pattern: /\{\{\s*#each\s+([^}]+?)\s+as\s+\|\s*(\w+)\s*\|\s*\}\}/g,
    replacement: '{{#for $2 in $1}}',
    examples: {
      handlebars: '{{#each products as |product|}}',
      sline: '{{#for product in products}}'
    }
  },
  {
    name: 'each 循环转换 - 基础',
    category: 'iteration',
    description: '转换基础 each 循环',
    pattern: /\{\{\s*#each\s+([^}]+?)\s*\}\}/g,
    replacement: '{{#for item in $1}}',
    examples: {
      handlebars: '{{#each products}}',
      sline: '{{#for item in products}}'
    }
  },
  {
    name: 'each 结束标签',
    category: 'iteration',
    description: '转换 each 结束标签',
    pattern: /\{\{\s*\/each\s*\}\}/g,
    replacement: '{{/for}}',
    examples: {
      handlebars: '{{/each}}',
      sline: '{{/for}}'
    }
  },
  // 循环变量转换
  {
    name: '循环索引变量转换',
    category: 'iteration',
    description: '转换循环内的索引变量',
    pattern: /\{\{\s*@index\s*\}\}/g,
    replacement: '{{forloop.index0}}',
    examples: {
      handlebars: '{{@index}}',
      sline: '{{forloop.index0}}'
    }
  },
  {
    name: '循环第一项变量转换',
    category: 'iteration',
    description: '转换循环内的第一项检查',
    pattern: /\{\{\s*@first\s*\}\}/g,
    replacement: '{{forloop.first}}',
    examples: {
      handlebars: '{{@first}}',
      sline: '{{forloop.first}}'
    }
  },
  {
    name: '循环最后项变量转换',
    category: 'iteration',
    description: '转换循环内的最后项检查',
    pattern: /\{\{\s*@last\s*\}\}/g,
    replacement: '{{forloop.last}}',
    examples: {
      handlebars: '{{@last}}',
      sline: '{{forloop.last}}'
    }
  },
  {
    name: '循环键值变量转换',
    category: 'iteration',
    description: '转换循环内的键值访问',
    pattern: /\{\{\s*@key\s*\}\}/g,
    replacement: '{{forloop.key}}',
    examples: {
      handlebars: '{{@key}}',
      sline: '{{forloop.key}}'
    }
  },
  {
    name: '当前项变量转换',
    category: 'iteration',
    description: '转换循环内的当前项引用',
    pattern: /\{\{\s*this\s*\}\}/g,
    replacement: '{{item}}',
    examples: {
      handlebars: '{{this}}',
      sline: '{{item}}'
    }
  }
];

/**
 * 获取循环标签规则统计信息
 * @returns {Object} 统计信息
 */
function getIterationTagStats() {
  return {
    totalRules: ITERATION_TAG_RULES.length,
    eachRules: ITERATION_TAG_RULES.filter(rule => rule.name.includes('each')).length,
    variableRules: ITERATION_TAG_RULES.filter(rule => rule.name.includes('变量')).length
  };
}

/**
 * 根据名称获取特定的循环标签规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getIterationRuleByName(ruleName) {
  return ITERATION_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

/**
 * 获取所有循环变量转换规则
 * @returns {Array} 循环变量规则数组
 */
function getLoopVariableRules() {
  return ITERATION_TAG_RULES.filter(rule => 
    rule.name.includes('变量') || 
    rule.name.includes('@') || 
    rule.name.includes('this')
  );
}

module.exports = {
  ITERATION_TAG_RULES,
  getIterationTagStats,
  getIterationRuleByName,
  getLoopVariableRules
};
