/**
 * Shopline 对象映射配置
 * 基于 Shopline 官方文档和实际使用的对象定义
 * 参考: https://developer.shopline.com/zh-hans-cn/docs/handlebars/
 * 参考: https://developer.shopline.com/zh-hans-cn/docs/sline/
 */

// ================== Shopline Objects 对象映射 ==================
const OBJECT_MAPPINGS = {
  // 商店对象
  shop: 'shop',
  store: 'shop', // 兼容性映射
  
  // 产品对象
  product: 'product',
  products: 'products',
  'product.title': 'product.title',
  'product.price': 'product.price',
  'product.compare_at_price': 'product.compare_at_price',
  'product.description': 'product.description',
  'product.images': 'product.images',
  'product.variants': 'product.variants',
  'product.tags': 'product.tags',
  'product.handle': 'product.handle',
  'product.available': 'product.available',
  'product.vendor': 'product.vendor',
  'product.type': 'product.type',
  
  // 集合对象
  collection: 'collection',
  collections: 'collections',
  'collection.title': 'collection.title',
  'collection.description': 'collection.description',
  'collection.products': 'collection.products',
  'collection.handle': 'collection.handle',
  
  // 页面对象
  page: 'page',
  pages: 'pages',
  page_title: 'page.title',
  page_description: 'page.description',
  
  // 客户对象
  customer: 'customer',
  'customer.email': 'customer.email',
  'customer.first_name': 'customer.first_name',
  'customer.last_name': 'customer.last_name',
  
  // 购物车对象
  cart: 'cart',
  'cart.item_count': 'cart.item_count',
  'cart.total_price': 'cart.total_price',
  'cart.items': 'cart.items',
  
  // 主题设置
  settings: 'settings',
  
  // 模板相关
  template: 'template',
  content_for_header: 'content_for_header',
  content_for_layout: 'content_for_layout',
  content_for_footer: 'content_for_footer',
  
  // 请求对象
  request: 'request',
  'request.locale': 'request.locale.iso_code'
};

// ================== 对象分类结构 ==================
const OBJECT_CATEGORIES = [
  {
    name: '商店对象',
    description: '商店基本信息和设置',
    objects: ['shop', 'store', 'settings']
  },
  {
    name: '产品对象',
    description: '产品相关的所有数据',
    objects: ['product', 'products', 'product.title', 'product.price', 'product.compare_at_price', 'product.description', 'product.images', 'product.variants', 'product.tags', 'product.handle', 'product.available', 'product.vendor', 'product.type']
  },
  {
    name: '集合对象',
    description: '产品集合相关数据',
    objects: ['collection', 'collections', 'collection.title', 'collection.description', 'collection.products', 'collection.handle']
  },
  {
    name: '页面对象',
    description: '页面和内容相关数据',
    objects: ['page', 'pages', 'page_title', 'page_description']
  },
  {
    name: '客户对象',
    description: '客户信息相关数据',
    objects: ['customer', 'customer.email', 'customer.first_name', 'customer.last_name']
  },
  {
    name: '购物车对象',
    description: '购物车和订单相关数据',
    objects: ['cart', 'cart.item_count', 'cart.total_price', 'cart.items']
  },
  {
    name: '模板对象',
    description: '模板系统相关对象',
    objects: ['template', 'content_for_header', 'content_for_layout', 'content_for_footer']
  },
  {
    name: '请求对象',
    description: '请求和上下文相关数据',
    objects: ['request', 'request.locale']
  }
];

/**
 * 根据对象名获取映射后的对象名
 * @param {string} objectName - 原始对象名
 * @returns {string} 映射后的对象名
 */
function getObjectByName(objectName) {
  return OBJECT_MAPPINGS[objectName] || objectName;
}

/**
 * 获取对象统计信息
 * @returns {Object} 统计信息
 */
function getObjectStats() {
  return {
    totalObjects: Object.keys(OBJECT_MAPPINGS).length,
    categories: OBJECT_CATEGORIES.length,
    objectsByCategory: OBJECT_CATEGORIES.reduce((acc, category) => {
      acc[category.name] = category.objects.length;
      return acc;
    }, {})
  };
}

module.exports = {
  OBJECT_MAPPINGS,
  OBJECT_CATEGORIES,
  getObjectByName,
  getObjectStats
};
