/**
 * 运算符标签转换规则
 * 处理逻辑运算符、比较运算符等的转换
 */

// ================== 运算符标签转换规则 ==================
const OPERATOR_TAG_RULES = [
  // 优先处理辅助函数形式
  {
    name: 'Handlebars and 辅助函数转换',
    category: 'operator', 
    description: '转换 and 辅助函数到 && 运算符',
    pattern: /\band\s+([^}\s)]+)\s+([^}\s)]+)/g,
    replacement: '$1 && $2',
    examples: {
      handlebars: '{{#if (and product.available product.price)}}',
      sline: '{{#if product.available && product.price}}'
    }
  },
  {
    name: 'Handlebars or 辅助函数转换',
    category: 'operator',
    description: '转换 or 辅助函数到 || 运算符',
    pattern: /\bor\s+([^}\s)]+)\s+([^}\s)]+)/g,
    replacement: '$1 || $2',
    examples: {
      handlebars: '{{#if (or product.available product.preorder)}}',
      sline: '{{#if product.available || product.preorder}}'
    }
  },
  // 然后处理关键字形式（仅在条件表达式中）
  {
    name: 'Handlebars and 关键字转换',
    category: 'operator',
    description: '转换 Handlebars and 关键字到 && 运算符（仅在条件中）',
    pattern: /(\w+(?:\.\w+)*)\s+and\s+(\w+(?:\.\w+)*)/g,
    replacement: '$1 && $2',
    examples: {
      handlebars: '{{#if product.available and product.price}}',
      sline: '{{#if product.available && product.price}}'
    }
  },
  {
    name: 'Handlebars or 关键字转换',
    category: 'operator',
    description: '转换 Handlebars or 关键字到 || 运算符（仅在条件中）',
    pattern: /(\w+(?:\.\w+)*)\s+or\s+(\w+(?:\.\w+)*)/g,
    replacement: '$1 || $2',
    examples: {
      handlebars: '{{#if product.available or product.preorder}}',
      sline: '{{#if product.available || product.preorder}}'
    }
  },
  {
    name: '严格相等运算符转换',
    category: 'operator',
    description: '转换 === 到 ==',
    pattern: /===/g,
    replacement: '==',
    examples: {
      handlebars: '{{#if product.type === "book"}}',
      sline: '{{#if product.type == "book"}}'
    }
  },
  {
    name: '严格不等运算符转换',
    category: 'operator',
    description: '转换 !== 到 !=',
    pattern: /!==/g,
    replacement: '!=',
    examples: {
      handlebars: '{{#if product.type !== "book"}}',
      sline: '{{#if product.type != "book"}}'
    }
  },
  {
    name: 'Handlebars gt 辅助函数转换',
    category: 'operator',
    description: '转换 gt 辅助函数到 > 运算符',
    pattern: /\bgt\s+([^}\s]+)\s+([^}\s]+)/g,
    replacement: '$1 > $2',
    examples: {
      handlebars: '{{#if (gt product.price 100)}}',
      sline: '{{#if product.price > 100}}'
    }
  },
  {
    name: 'Handlebars lt 辅助函数转换',
    category: 'operator',
    description: '转换 lt 辅助函数到 < 运算符',
    pattern: /\blt\s+([^}\s]+)\s+([^}\s]+)/g,
    replacement: '$1 < $2',
    examples: {
      handlebars: '{{#if (lt product.price 50)}}',
      sline: '{{#if product.price < 50}}'
    }
  },
  {
    name: 'Handlebars eq 辅助函数转换',
    category: 'operator',
    description: '转换 eq 辅助函数到 == 运算符',
    pattern: /\beq\s+([^}\s]+)\s+([^}\s]+)/g,
    replacement: '$1 == $2',
    examples: {
      handlebars: '{{#if (eq product.type "book")}}',
      sline: '{{#if product.type == "book"}}'
    }
  }
];

/**
 * 获取运算符标签规则统计信息
 * @returns {Object} 统计信息
 */
function getOperatorTagStats() {
  return {
    totalRules: OPERATOR_TAG_RULES.length,
    logicalOperators: OPERATOR_TAG_RULES.filter(rule => 
      rule.name.includes('and') || rule.name.includes('or')
    ).length,
    comparisonOperators: OPERATOR_TAG_RULES.filter(rule => 
      rule.name.includes('gt') || rule.name.includes('lt') || rule.name.includes('eq')
    ).length,
    equalityOperators: OPERATOR_TAG_RULES.filter(rule => 
      rule.name.includes('===') || rule.name.includes('!==')
    ).length
  };
}

/**
 * 获取所有支持的比较运算符
 * @returns {Array} 运算符名称数组
 */
function getSupportedComparisonOperators() {
  return ['gt', 'lt', 'eq', 'gte', 'lte', 'ne'];
}

/**
 * 获取所有支持的逻辑运算符
 * @returns {Array} 运算符名称数组
 */
function getSupportedLogicalOperators() {
  return ['and', 'or', 'not'];
}

module.exports = {
  OPERATOR_TAG_RULES,
  getOperatorTagStats,
  getSupportedComparisonOperators,
  getSupportedLogicalOperators
};
