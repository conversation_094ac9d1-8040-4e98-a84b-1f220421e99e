/**
 * 输出标签转换规则
 * 处理原始输出、注释等输出相关的转换
 */

// ================== 输出标签转换规则 ==================
const OUTPUT_TAG_RULES = [
  {
    name: '原始输出转换',
    category: 'output',
    description: '转换三重花括号原始输出到 raw 过滤器',
    pattern: /\{\{\{\s*([^}]+?)\s*\}\}\}/g,
    replacement: '{{$1|raw()}}',
    examples: {
      handlebars: '{{{product.description}}}',
      sline: '{{product.description|raw()}}'
    }
  },
  {
    name: '注释转换 - 块注释（已经是正确格式）',
    category: 'output',
    description: 'Handlebars 块注释已经是 Sline 兼容格式，无需转换',
    pattern: /\{\{\!\-\-\s*(.*?)\s*\-\-\}\}/gs,
    replacement: '{{!-- $1 --}}',
    examples: {
      handlebars: '{{!-- This is a comment --}}',
      sline: '{{!-- This is a comment --}}'
    }
  },
  {
    name: '注释转换 - 简单注释',
    category: 'output',
    description: '转换简单注释格式到 Sline',
    pattern: /\{\{\!\s*(.*?)\s*\}\}/g,
    replacement: '{{!-- $1 --}}',
    examples: {
      handlebars: '{{! This is a comment}}',
      sline: '{{!-- This is a comment --}}'
    }
  }
];

/**
 * 获取输出标签规则统计信息
 * @returns {Object} 统计信息
 */
function getOutputTagStats() {
  return {
    totalRules: OUTPUT_TAG_RULES.length,
    rawOutputRules: OUTPUT_TAG_RULES.filter(rule => rule.name.includes('原始输出')).length,
    commentRules: OUTPUT_TAG_RULES.filter(rule => rule.name.includes('注释')).length
  };
}

module.exports = {
  OUTPUT_TAG_RULES,
  getOutputTagStats
};
