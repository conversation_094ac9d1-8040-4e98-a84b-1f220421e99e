/**
 * 部分模板标签转换规则
 * 处理 partial、include 等模板包含相关的转换
 */

// ================== 部分模板标签转换规则 ==================
const PARTIAL_TAG_RULES = [
  {
    name: '部分模板转换 - 带参数',
    category: 'partial',
    description: '转换带参数的部分模板到 include 自闭合标签',
    pattern: /\{\{\s*>\s*([a-zA-Z0-9_/-]+)\s+([^}]+?)\s*\}\}/g,
    replacement: '{{#include $1 $2 /}}',
    examples: {
      handlebars: '{{>product-card product=product}}',
      sline: '{{#include product-card product=product /}}'
    }
  },
  {
    name: '部分模板转换 - 基础',
    category: 'partial',
    description: '转换基础部分模板到 include 自闭合标签',
    pattern: /\{\{\s*>\s*([a-zA-Z0-9_/-]+)\s*\}\}/g,
    replacement: '{{#include $1 /}}',
    examples: {
      handlebars: '{{>header}}',
      sline: '{{#include header /}}'
    }
  }
];

/**
 * 获取部分模板标签规则统计信息
 * @returns {Object} 统计信息
 */
function getPartialTagStats() {
  return {
    totalRules: PARTIAL_TAG_RULES.length,
    basicPartialRules: PARTIAL_TAG_RULES.filter(rule => rule.name.includes('基础')).length,
    parameterizedPartialRules: PARTIAL_TAG_RULES.filter(rule => rule.name.includes('带参数')).length
  };
}

module.exports = {
  PARTIAL_TAG_RULES,
  getPartialTagStats
};
