/**
 * 后处理规则
 * 在主要转换之后执行的规则，用于清理和优化输出内容
 */

// ================== 后处理规则 ==================
const POSTPROCESS_RULES = [
  {
    name: '清理多余的空行',
    description: '清理连续的空行（超过2个）',
    pattern: /\n\s*\n\s*\n\s*\n/g,
    replacement: '\n\n\n'
  },
  {
    name: '标准化标签空格',
    description: '标准化标签内部空格',
    pattern: /\{\{\s*([^}]+?)\s*\}\}/g,
    replacement: '{{$1}}'
  },
  {
    name: '修复连续的条件标签',
    description: '修复可能出现的连续 else 标签问题',
    pattern: /\{\{#else\/\}\}\s*\{\{#else\/\}\}/g,
    replacement: '{{#else/}}'
  },
  {
    name: '修复自闭合标签空格',
    description: '标准化自闭合标签的空格',
    pattern: /\{\{#(\w+)\s+([^}]+?)\s+\/\}\}/g,
    replacement: '{{#$1 $2 /}}'
  }
];

/**
 * 应用所有后处理规则
 * @param {string} input - 输入文本
 * @returns {Object} 处理结果和统计信息
 */
function applyPostprocessRules(input) {
  let output = input;
  const appliedRules = [];
  
  POSTPROCESS_RULES.forEach(rule => {
    const before = output;
    output = output.replace(rule.pattern, rule.replacement);
    
    if (before !== output) {
      appliedRules.push(rule.name);
    }
  });
  
  return {
    output,
    appliedRules,
    stats: {
      rulesApplied: appliedRules.length
    }
  };
}

/**
 * 获取后处理规则统计信息
 * @returns {Object} 统计信息
 */
function getPostprocessStats() {
  return {
    totalRules: POSTPROCESS_RULES.length
  };
}

/**
 * 清理转换结果中的常见问题
 * @param {string} input - 输入文本
 * @returns {string} 清理后的文本
 */
function cleanupConversionResult(input) {
  let output = input;
  
  // 移除多余的空白
  output = output.replace(/\s+$/gm, ''); // 移除行尾空白
  output = output.replace(/^\s*\n/gm, '\n'); // 移除只有空白的行
  
  // 标准化换行符
  output = output.replace(/\r\n/g, '\n');
  output = output.replace(/\r/g, '\n');
  
  return output;
}

module.exports = {
  POSTPROCESS_RULES,
  applyPostprocessRules,
  getPostprocessStats,
  cleanupConversionResult
};
