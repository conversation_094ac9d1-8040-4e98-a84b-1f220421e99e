/**
 * 预处理规则
 * 在主要转换之前执行的规则，用于标准化输入内容
 */

// ================== 预处理规则 ==================
const PREPROCESS_RULES = [
// 暂时禁用空格标准化，保持原有格式
  // {
  //   name: '标准化花括号内空格',
  //   description: '标准化花括号内的空格，但保留基本格式',
  //   pattern: /\{\{\s{2,}([^}]+?)\s{2,}\}\}/g,
  //   replacement: '{{ $1 }}'
  // },
  {
    name: '处理空白符控制',
    description: '移除 Handlebars 的空白符控制字符（Sline 可能不支持）',
    pattern: /\{\{~\s*([^}]+?)\s*~\}\}/g,
    replacement: '{{$1}}',
    warning: 'Sline 可能不支持空白符控制，已移除 ~ 字符'
  },
  {
    name: '转换注释中的 Handlebars 为 Sline',
    description: '将注释中的 Handlebars 文本替换为 Sline',
    pattern: /(<!--[\s\S]*?)Handlebars([\s\S]*?-->)/g,
    replacement: '$1Sline$2',
    examples: {
      handlebars: '<!-- This is a Handlebars template -->',
      sline: '<!-- This is a Sline template -->'
    }
  }
];

/**
 * 应用所有预处理规则
 * @param {string} input - 输入文本
 * @returns {Object} 处理结果和统计信息
 */
function applyPreprocessRules(input) {
  let output = input;
  const appliedRules = [];
  const warnings = [];
  
  PREPROCESS_RULES.forEach(rule => {
    const before = output;
    output = output.replace(rule.pattern, rule.replacement);
    
    if (before !== output) {
      appliedRules.push(rule.name);
      if (rule.warning) {
        warnings.push(rule.warning);
      }
    }
  });
  
  return {
    output,
    appliedRules,
    warnings,
    stats: {
      rulesApplied: appliedRules.length,
      warningsGenerated: warnings.length
    }
  };
}

/**
 * 获取预处理规则统计信息
 * @returns {Object} 统计信息
 */
function getPreprocessStats() {
  return {
    totalRules: PREPROCESS_RULES.length,
    rulesWithWarnings: PREPROCESS_RULES.filter(rule => rule.warning).length
  };
}

module.exports = {
  PREPROCESS_RULES,
  applyPreprocessRules,
  getPreprocessStats
};
