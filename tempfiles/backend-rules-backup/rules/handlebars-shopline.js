/**
 * Shopline 特有标签转换规则
 * 这些是 Shopline 平台特有的语法，需要转换为 Sline 对应的语法
 */

// ================== Shopline 特有标签转换规则 ==================
const SHOPLINE_TAG_RULES = [
  {
    name: 'Handlebars assign 变量赋值转换',
    category: 'shopline',
    description: '转换 Handlebars assign 辅助函数到 Sline var 语法',
    pattern: /\{\{\s*assign\s+([^=\s]+)\s*=\s*([^}]+)\s*\}\}/g,
    replacement: '{{#var $1 = $2 /}}',
    examples: {
      handlebars: '{{assign price = product.price}}',
      sline: '{{#var price = product.price /}}'
    }
  },
  {
    name: 'snippet 转换为 include 自闭合标签',
    category: 'shopline',
    description: '转换 Handlebars snippet 辅助函数到 Sline include 自闭合标签',
    pattern: /\{\{\s*snippet\s+['"]([^'"]+)['"]\s*\}\}/g,
    replacement: '{{#include "$1" /}}',
    examples: {
      handlebars: '{{ snippet \'theme-css-var\' }}',
      sline: '{{#include "theme-css-var" /}}'
    }
  },
  {
    name: 'snippet 带参数转换为 include',
    category: 'shopline',
    description: '转换带参数的 snippet 到 include',
    pattern: /\{\{\s*snippet\s+['"]([^'"]+)['"]\s+([^}]+)\s*\}\}/g,
    replacement: '{{#include "$1" $2 /}}',
    examples: {
      handlebars: '{{ snippet \'activity/promotion-tag\' benefitCondition=this }}',
      sline: '{{#include "activity/promotion-tag" benefitCondition=this /}}'
    }
  },
  {
    name: 'content_for_header 转换',
    category: 'shopline',
    description: '转换 content_for_header 到 include 自闭合标签',
    pattern: /\{\{\s*content_for_header\s*\}\}/g,
    replacement: '{{#include "content_for_header" /}}',
    examples: {
      handlebars: '{{ content_for_header }}',
      sline: '{{#include "content_for_header" /}}'
    }
  },
  {
    name: 'content_for_layout 转换',
    category: 'shopline',
    description: '转换 content_for_layout 到 include 自闭合标签',
    pattern: /\{\{\s*content_for_layout\s*\}\}/g,
    replacement: '{{#include "content_for_layout" /}}',
    examples: {
      handlebars: '{{ content_for_layout }}',
      sline: '{{#include "content_for_layout" /}}'
    }
  },
  {
    name: 'content_for_footer 转换',
    category: 'shopline',
    description: '转换 content_for_footer 到 include 自闭合标签',
    pattern: /\{\{\s*content_for_footer\s*\}\}/g,
    replacement: '{{#include "content_for_footer" /}}',
    examples: {
      handlebars: '{{ content_for_footer }}',
      sline: '{{#include "content_for_footer" /}}'
    }
  },
  {
    name: 'section 自闭合标签',
    category: 'shopline',
    description: '转换 section 标签到 Sline 自闭合语法',
    pattern: /\{\{\s*section\s+['"]([^'"]+)['"]\s*\}\}/g,
    replacement: '{{#section "$1" /}}',
    examples: {
      handlebars: '{{section "main-password-header"}}',
      sline: '{{#section "main-password-header" /}}'
    }
  },
  {
    name: 'stylesheet 标签转换',
    category: 'shopline',
    description: '转换样式表标签到 Sline component 语法',
    pattern: /\{\{\s*stylesheet\s+src=['"]([^'"]+)['"]\s*([^}]*)\s*\}\}/g,
    replacement: '{{#component "stylesheet" src="$1" | asset_url() /}}',
    examples: {
      handlebars: '{{ stylesheet src="base/index.css" }}',
      sline: '{{#component "stylesheet" src="base/index.css" | asset_url() /}}'
    }
  },
  {
    name: 'script 标签转换',
    category: 'shopline',
    description: '转换脚本标签到 Sline component 语法',
    pattern: /\{\{\s*script\s+src=['"]([^'"]+)['"]\s*([^}]*)\s*\}\}/g,
    replacement: '{{#component "script" src="$1" | asset_url() /}}',
    examples: {
      handlebars: '{{ script src="base/index.js" }}',
      sline: '{{#component "script" src="base/index.js" | asset_url() /}}'
    }
  },
  {
    name: 'combine_asset_tag 智能转换',
    category: 'shopline',
    description: '智能转换 combine_asset_tag，CSS 转为 stylesheet，JS 转为 script component',
    apply: (text) => {
      return text.replace(/\{\{\s*combine_asset_tag\s+([^}]+)\s*\}\}/g, (match, content) => {
        // 提取所有引号内的内容
        const singleQuoteMatches = content.match(/'([^']+)'/g) || [];
        const doubleQuoteMatches = content.match(/"([^"]+)"/g) || [];
        
        // 提取内容并严格过滤，只保留真正的文件路径
        const allQuotedContent = [
          ...singleQuoteMatches.map(path => path.slice(1, -1)),
          ...doubleQuoteMatches.map(path => path.slice(1, -1))
        ];
        
        // 严格过滤：只保留以文件扩展名结尾的路径
        const filePaths = allQuotedContent.filter(path => {
          return path.match(/\.(css|js|scss|less|ts)$/i);
        });
        
        if (filePaths.length === 0) {
          return `{{!-- TODO: combine_asset_tag needs manual conversion: no file paths found --}}`;
        }
        
        // 检查所有文件是否都是同一类型
        const cssFiles = filePaths.filter(path => path.match(/\.css$/i));
        const jsFiles = filePaths.filter(path => path.match(/\.js$/i));
        
        if (cssFiles.length === filePaths.length && cssFiles.length > 0) {
          // 所有文件都是 CSS
          return cssFiles.map(path => `{{#component "stylesheet" src="${path}" | asset_url() /}}`).join('\n');
        } else if (jsFiles.length === filePaths.length && jsFiles.length > 0) {
          // 所有文件都是 JS
          return jsFiles.map(path => `{{#component "script" src="${path}" | asset_url() /}}`).join('\n');
        } else {
          // 混合类型或其他文件类型
          return `{{!-- TODO: combine_asset_tag needs manual conversion: found ${filePaths.length} files (${jsFiles.length} JS, ${cssFiles.length} CSS): ${filePaths.join(', ')} --}}`;
        }
      });
    },
    examples: {
      handlebars: `{{ combine_asset_tag 'commons/layout/theme/index.rtl.css' inline=true}}
{{ combine_asset_tag 'vendors/swiper.min.js' type="text/javascript" defer=true }}
{{ combine_asset_tag 'theme-shared/utils/sectionsLoad/index.js' 'stage/slideshow/index.js' defer=true }}`,
      sline: `{{#component "stylesheet" src="commons/layout/theme/index.rtl.css" | asset_url() /}}
{{#component "script" src="vendors/swiper.min.js" | asset_url() /}}
{{#component "script" src="theme-shared/utils/sectionsLoad/index.js" | asset_url() /}}
{{#component "script" src="stage/slideshow/index.js" | asset_url() /}}`
    }
  },
  {
    name: 'preload_state 转换警告',
    category: 'shopline',
    description: 'preload_state 需要手动处理',
    pattern: /\{\{\s*preload_state\s+([^}]+)\s*\}\}/g,
    replacement: '{{!-- TODO: preload_state needs manual conversion: $1 --}}',
    warning: 'preload_state 需要根据 Sline 的状态管理机制手动转换',
    examples: {
      handlebars: '{{ preload_state \'storeInfo\' \'cartInfo\' }}',
      sline: '{{!-- TODO: preload_state needs manual conversion --}}'
    }
  },
  {
    name: 'Handlebars with 上下文转换警告',
    category: 'shopline',
    description: 'with 语法需要手动处理（Sline 不支持 with）',
    pattern: /\{\{\s*#with\s+([^}]+)\s*\}\}/g,
    replacement: '{{!-- TODO: with context needs manual conversion: $1 --}}',
    warning: 'Sline 不支持 with 语法，需要手动重构代码',
    examples: {
      handlebars: '{{#with product}}',
      sline: '{{!-- TODO: with context needs manual conversion: product --}}'
    }
  },
  {
    name: 'Handlebars range 辅助函数转换',
    category: 'shopline',
    description: '转换 Handlebars range 到 Sline range 过滤器语法',
    pattern: /\{\{\s*#each\s+\((\d+|[\w.]+)\.\.(\d+|[\w.]+)\)\s+as\s+\|\s*(\w+)\s*\|\s*\}\}/g,
    replacement: '{{#for $3 in $2|range($1)}}',
    examples: {
      handlebars: '{{#each (1..5) as |i|}}',
      sline: '{{#for i in 5|range(1)}}'
    }
  }
];

/**
 * 获取 Shopline 标签规则统计信息
 * @returns {Object} 统计信息
 */
function getShoplineTagStats() {
  return {
    totalRules: SHOPLINE_TAG_RULES.length,
    rulesWithWarnings: SHOPLINE_TAG_RULES.filter(rule => rule.warning).length,
    rulesWithApplyFunction: SHOPLINE_TAG_RULES.filter(rule => rule.apply).length
  };
}

/**
 * 根据名称获取特定的 Shopline 标签规则
 * @param {string} ruleName - 规则名称
 * @returns {Object|null} 规则对象或 null
 */
function getShoplineRuleByName(ruleName) {
  return SHOPLINE_TAG_RULES.find(rule => rule.name === ruleName) || null;
}

module.exports = {
  SHOPLINE_TAG_RULES,
  getShoplineTagStats,
  getShoplineRuleByName
};
