/**
 * 转换验证和统计函数
 * 用于检查转换质量和生成统计报告
 */

/**
 * 转换质量检查函数
 * @param {string} originalCode - 原始代码
 * @param {string} convertedCode - 转换后的代码
 * @returns {Object} 验证结果
 */
function validateConversion(originalCode, convertedCode) {
  const warnings = [];
  
  // 检查是否包含未转换的 Handlebars 语法
  if (convertedCode.includes('{{#each')) {
    warnings.push('发现未转换的 {{#each}} 语法');
  }
  
  if (convertedCode.includes('{{#unless')) {
    warnings.push('发现未转换的 {{#unless}} 语法');
  }
  
  if (convertedCode.includes('{{{')) {
    warnings.push('发现未转换的原始输出语法 {{{...}}}');
  }
  
  if (convertedCode.includes('{{>')) {
    warnings.push('发现未转换的部分模板语法 {{>...}}');
  }
  
  if (convertedCode.includes('{{ snippet')) {
    warnings.push('发现未转换的 snippet 语法');
  }
  
  if (convertedCode.includes('content_for_')) {
    warnings.push('发现未转换的 content_for_ 语法');
  }
  
  if (convertedCode.includes('combine_asset_tag')) {
    warnings.push('发现需要手动处理的 combine_asset_tag');
  }
  
  return {
    isValid: warnings.length === 0,
    warnings: warnings
  };
}

/**
 * 获取转换统计信息
 * @param {string} originalCode - 原始代码
 * @param {string} convertedCode - 转换后的代码
 * @returns {Object} 统计信息
 */
function getConversionStats(originalCode, convertedCode) {
  const stats = {
    originalLength: originalCode.length,
    convertedLength: convertedCode.length,
    handlebarsBlocks: (originalCode.match(/\{\{#\w+/g) || []).length,
    slineBlocks: (convertedCode.match(/\{\{#\w+/g) || []).length,
    filtersConverted: 0,
    conditionalConverted: 0,
    loopsConverted: 0,
    snippetsConverted: (originalCode.match(/\{\{\s*snippet/g) || []).length,
    contentForConverted: (originalCode.match(/content_for_/g) || []).length,
    selfClosingTagsGenerated: (convertedCode.match(/\{\{#\w+[^}]+\/\}\}/g) || []).length
  };
  
  // 统计各类转换
  stats.filtersConverted = (originalCode.match(/\{\{\s*\w+\s+[^}]+\s*\}\}/g) || []).length;
  stats.conditionalConverted = (originalCode.match(/\{\{#(if|unless)/g) || []).length;
  stats.loopsConverted = (originalCode.match(/\{\{#each/g) || []).length;
  
  return stats;
}

/**
 * 分析代码复杂度
 * @param {string} code - 代码
 * @returns {Object} 复杂度分析
 */
function analyzeCodeComplexity(code) {
  const lines = code.split('\n');
  const nonEmptyLines = lines.filter(line => line.trim().length > 0);
  
  return {
    totalLines: lines.length,
    nonEmptyLines: nonEmptyLines.length,
    averageLineLength: nonEmptyLines.reduce((acc, line) => acc + line.length, 0) / nonEmptyLines.length,
    maxLineLength: Math.max(...nonEmptyLines.map(line => line.length)),
    handlebarsExpressions: (code.match(/\{\{[^}]+\}\}/g) || []).length,
    nestedBlocks: (code.match(/\{\{#\w+[^}]*\}\}[\s\S]*?\{\{\/\w+\}\}/g) || []).length
  };
}

/**
 * 检查语法有效性
 * @param {string} code - 代码
 * @returns {Object} 语法检查结果
 */
function checkSyntaxValidity(code) {
  const issues = [];
  
  // 检查括号匹配
  const openBraces = (code.match(/\{\{/g) || []).length;
  const closeBraces = (code.match(/\}\}/g) || []).length;
  if (openBraces !== closeBraces) {
    issues.push(`括号不匹配: ${openBraces} 个开括号, ${closeBraces} 个闭括号`);
  }
  
  // 检查块标签匹配
  const openBlocks = (code.match(/\{\{#\w+/g) || []).map(match => match.replace('{{#', ''));
  const closeBlocks = (code.match(/\{\{\/\w+/g) || []).map(match => match.replace('{{/', ''));
  
  openBlocks.forEach(block => {
    const blockName = block.split(/\s+/)[0];
    if (!closeBlocks.includes(blockName) && !isSelfClosingBlock(blockName)) {
      issues.push(`未闭合的块标签: ${blockName}`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues: issues
  };
}

/**
 * 检查是否为自闭合块标签
 * @param {string} blockName - 块标签名
 * @returns {boolean} 是否为自闭合
 */
function isSelfClosingBlock(blockName) {
  const selfClosingBlocks = ['include', 'var', 'section', 'component', 'else', 'case'];
  return selfClosingBlocks.includes(blockName);
}

/**
 * 生成转换报告
 * @param {string} originalCode - 原始代码
 * @param {string} convertedCode - 转换后的代码
 * @param {Object} appliedRules - 应用的规则
 * @returns {Object} 转换报告
 */
function generateConversionReport(originalCode, convertedCode, appliedRules = {}) {
  const validation = validateConversion(originalCode, convertedCode);
  const stats = getConversionStats(originalCode, convertedCode);
  const complexity = analyzeCodeComplexity(convertedCode);
  const syntax = checkSyntaxValidity(convertedCode);
  
  return {
    timestamp: new Date().toISOString(),
    validation,
    stats,
    complexity,
    syntax,
    appliedRules,
    summary: {
      conversionSuccessful: validation.isValid && syntax.isValid,
      warningCount: validation.warnings.length,
      issueCount: syntax.issues.length,
      reductionRate: ((stats.originalLength - stats.convertedLength) / stats.originalLength * 100).toFixed(2) + '%'
    }
  };
}

module.exports = {
  validateConversion,
  getConversionStats,
  analyzeCodeComplexity,
  checkSyntaxValidity,
  generateConversionReport,
  isSelfClosingBlock
};
