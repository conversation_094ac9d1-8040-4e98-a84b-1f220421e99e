/**
 * 循环变量和操作符映射配置
 * 基于 Handlebars 到 Sline 的语法转换规则
 */

// ================== 循环变量映射 ==================
const LOOP_VARIABLE_MAPPINGS = {
  '@index': 'forloop.index0',      // 从 0 开始的索引
  '@first': 'forloop.first',       // 是否是第一个
  '@last': 'forloop.last',         // 是否是最后一个
  '@key': 'forloop.key',           // 对象的键
  '@root': 'global',               // 根上下文
  '@../': '../',                   // 父级上下文
  'this': 'item'                   // 当前项（在 each 循环中）
};

// ================== 操作符映射 ==================
// 基于真实 Sline 语法（仅精确匹配）
const OPERATOR_MAPPINGS = {
  '===': '==',
  '!==': '!='
  // 注意：and 和 or 的转换已经在 OPERATOR_TAG_RULES 中更精确地处理
};

/**
 * 根据变量名获取映射后的变量名
 * @param {string} variableName - 原始变量名
 * @returns {string} 映射后的变量名
 */
function getVariableByName(variableName) {
  return LOOP_VARIABLE_MAPPINGS[variableName] || variableName;
}

/**
 * 根据操作符获取映射后的操作符
 * @param {string} operator - 原始操作符
 * @returns {string} 映射后的操作符
 */
function getOperatorByName(operator) {
  return OPERATOR_MAPPINGS[operator] || operator;
}

/**
 * 获取变量映射统计信息
 * @returns {Object} 统计信息
 */
function getVariableStats() {
  return {
    totalLoopVariables: Object.keys(LOOP_VARIABLE_MAPPINGS).length,
    totalOperators: Object.keys(OPERATOR_MAPPINGS).length
  };
}

/**
 * 检查是否为循环变量
 * @param {string} variableName - 变量名
 * @returns {boolean} 是否为循环变量
 */
function isLoopVariable(variableName) {
  return variableName in LOOP_VARIABLE_MAPPINGS;
}

/**
 * 检查是否为需要转换的操作符
 * @param {string} operator - 操作符
 * @returns {boolean} 是否需要转换
 */
function isConvertibleOperator(operator) {
  return operator in OPERATOR_MAPPINGS;
}

module.exports = {
  LOOP_VARIABLE_MAPPINGS,
  OPERATOR_MAPPINGS,
  getVariableByName,
  getOperatorByName,
  getVariableStats,
  isLoopVariable,
  isConvertibleOperator
};
